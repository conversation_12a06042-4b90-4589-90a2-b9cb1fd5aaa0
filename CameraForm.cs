﻿using AForge.Video;
using AForge.Video.DirectShow;
using System;
using System.Drawing;
using System.Windows.Forms;
using Accord.Vision.Detection;
using Accord.Vision.Detection.Cascades;
using OpenCvSharp;
using OpenCvSharp.Extensions;

namespace EmployeeManagementSystem
{
    public partial class CameraForm : Form
    {
        private FilterInfoCollection? videoDevices;
        private VideoCaptureDevice? videoSource;
        private Bitmap? latestFrame;  // لحفظ آخر صورة أصلية بدون إطار
        private readonly CascadeClassifier faceCascade = new("haarcascade_frontalface_alt.xml");

        public event Action<Bitmap>? OnImageCaptured;
        private HaarObjectDetector? faceDetector;

        public CameraForm()
        {
            InitializeComponent();
        }

    
        private void CameraForm_Load(object sender, EventArgs e)
        {
            videoDevices = new FilterInfoCollection(FilterCategory.VideoInputDevice);

            if (videoDevices.Count == 0)
            {
                MessageBox.Show("لا توجد كاميرات متصلة!");
                this.Close();
                return;
            }

            var cascade = new FaceHaarCascade();

            faceDetector = new HaarObjectDetector(cascade, 30)
            {
                SearchMode = ObjectDetectorSearchMode.Average,
                ScalingMode = ObjectDetectorScalingMode.GreaterToSmaller,
                ScalingFactor = 1.5f,
                UseParallelProcessing = true,
                Suppression = 3
            };

            videoSource = new VideoCaptureDevice(videoDevices[0].MonikerString);

            try
            {
                // إذا الخاصية مدعومة يرجع true
                bool supported = videoSource.GetCameraProperty(CameraControlProperty.Focus, out int focusValue, out CameraControlFlags flags);

                if (supported)
                {
                    // ضبط الفوكس تلقائياً
                    videoSource.SetCameraProperty(CameraControlProperty.Focus, 0, CameraControlFlags.Auto);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في ضبط الفوكس: " + ex.Message);
            }

            videoSource.NewFrame += VideoSource_NewFrame;
            videoSource.Start();
        }

        private void VideoSource_NewFrame(object sender, NewFrameEventArgs eventArgs)
        {
            try
            {
                Bitmap frame = (Bitmap)eventArgs.Frame.Clone();

                // احفظ النسخة الأصلية
                latestFrame?.Dispose();
                latestFrame = (Bitmap)frame.Clone();

                // تحويل الصورة إلى Mat من OpenCV
                using (Mat mat = BitmapConverter.ToMat(frame))
                {
                    // تحويل إلى رمادي (مطلوب لـ Haar)
                    using Mat gray = new();
                    Cv2.CvtColor(mat, gray, ColorConversionCodes.BGR2GRAY);

                    // كشف الوجوه
                    var faces = faceCascade.DetectMultiScale(gray, 1.1, 4, HaarDetectionTypes.ScaleImage);

                    foreach (var rect in faces)
                    {
                        Cv2.Rectangle(mat, rect, Scalar.YellowGreen, 2);
                    }

                    // تحويل إلى Bitmap
                    Bitmap frameWithFaces = BitmapConverter.ToBitmap(mat);

                    if (pictureBox1.InvokeRequired)
                    {
                        pictureBox1.Invoke(new Action(() =>
                        {
                            pictureBox1.Image?.Dispose();
                            pictureBox1.Image = frameWithFaces;
                        }));
                    }
                    else
                    {
                        pictureBox1.Image?.Dispose();
                        pictureBox1.Image = frameWithFaces;
                    }
                }

                frame.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine("خطأ: " + ex.Message);
            }
        }

        private async void BtnCapture_Click(object sender, EventArgs e)
        {
            if (latestFrame != null)
            {
                // استخدم latestFrame (بدون إطار) لحفظ الصورة أو إرسالها
                OnImageCaptured?.Invoke(new Bitmap(latestFrame));

                if (videoSource != null && videoSource.IsRunning)
                {
                    videoSource.SignalToStop();
                    await Task.Run(() => videoSource.WaitForStop());
                    videoSource.NewFrame -= VideoSource_NewFrame;
                    videoSource = null;
                }

                pictureBox1.Image.Dispose();
                pictureBox1.Image = null;

                latestFrame.Dispose();
                latestFrame = null;

                this.Close();
            }
           
        }

        private async void BtnStopCamera_Click(object sender, EventArgs e)
        {
            try
            {
                if (videoSource != null && videoSource.IsRunning)
                {
                    videoSource.SignalToStop();
                    await Task.Run(() => videoSource.WaitForStop());
                    videoSource.NewFrame -= VideoSource_NewFrame;
                    videoSource = null;
                }

                pictureBox1.Image?.Dispose();
                pictureBox1.Image = null;

                MessageBox.Show("تم إيقاف الكامرة بنجاح.");
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء إيقاف الكامرة: " + ex.Message);
            }
        }

        private async void CameraForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                if (videoSource != null)
                {
                    if (videoSource.IsRunning)
                    {
                        videoSource.SignalToStop();
                        await Task.Run(() =>
                        {
                            while (videoSource.IsRunning)
                            {
                                System.Threading.Thread.Sleep(50);
                            }
                        });
                    }

                    videoSource.NewFrame -= VideoSource_NewFrame;
                    videoSource = null;
                }

                pictureBox1.Image?.Dispose();
                pictureBox1.Image = null;
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء إغلاق الكامرة: " + ex.Message);
            }
        }
    }
}
