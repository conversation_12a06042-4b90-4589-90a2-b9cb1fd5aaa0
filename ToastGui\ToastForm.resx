﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBox1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAABGdBTUEAALGPC/xhBQAABrdJREFUeF7t
        W1lsE0cYTg+16lNP9amt2peqLxXqpb70tYdUtVIP+l5VRTTehVBKQDRQizZAiL0m4QjB8c7aiZ3YiVNC
        MJQjISRUJTQ0FxBajhLCDcU5IAdHpv8/O+vgeEVIyO7akT/pE2RmZ/7v/3eOf3bXGWmkkUYaZkMSyFyn
        QLqdgnIL/qUq2f9PYx2/bGYCnJw95rQ+JZvyFb98ZsFutz8Kd/gfdNIhKNn4N69idU4bWawGQTl+d92M
        gSTIX6sOykdDs0OP8OIYsAzr2CiAa3lx6iNf8L0iibIAjsG8jx/u92C306bYXFnkZd5N6kESlQ/AkYZx
        jk2aMG32Omzkfd5t8qNgvu8lEL5Tc2BjdtnodnkHPbzvAD3f1UGjZ7ro0NWT9Gb0VByxLNrdxa7phGux
        DbbV+nGKZEee6H6Bm0lOSJnkIxDbpzpeOnow0kgHr5xIcPZ+iW2btzXSDYtigehN2tEAq/enIHAYhVav
        r6F9Z//WdWoqxL7C62q0aTEsieQTbjY5kG+T3wVhQyiwKVxPb17Td0SPg5dPULIiSIuWlFFlRYju9P5G
        jx9sSbwW+mysrNeCMOTIJO9w89Yif67veRB0DoXVeQN3Rnqq6WR4499q6lnu1RyLsSzXR8+1VCZcX6cE
        bmO9JCg9jjnFz3EZ1gESGA8KqsgrHRo5Ey/2fjkM7fqOh+n51kr6R1U53bREDYhrHqGnfo8PAl7rX1nK
        RhvkC8VchjVwCOQ1EHLbNV+5dam9Kk7og/DGqTCOJlqwQKEn94cS6i+1V1K0yc4QNu+rXI75gD16Ld6J
        HW7/yHiRRjOy2T+ijgJF4nLMBcvfBXIRRZz9M3GuThcHT4dp+epS2uAvjyvvAZtoG3heL7U2HE7RMwsF
        bMxWrt8tbLrZfyIMa4FC185X2Dpxd93GbO911AAj8XUuyzxAZvYtGq8pLDM0AMiQo5Td7c5dFXHlW9aV
        DagBUL7hsswDBMCFxpur44emEWyqCLAANJQF4sqbqytYuSXrABxda9B4V30wTpQRPHeoCoc7bd0ePwK6
        6kLqOiCSLVyWeYB0tB2N6yUrZvFsS5UaAIG0cVnmAYyyQw8uUnrizCAuijwAfVyWOZAWlDyDhguyyLCe
        MDNZkKWwA9iq7/xPc3nGw2WT30SjJcu8UT1RZtL9o6+XjQLR+waXZzwcIvkcjcL2ZHkAgvmlLACSzfsZ
        l2c8JFFZiEZ3y4FBPVFmcpfHzw5GcCj7nsszHmCwEI2akQNMRNSgBkAu4PKMh8NGtqJR3If1RJnJo3VB
        vhPINVye8QCDHWgU92E9UWYSD2KoBfMSLs94wHzrR6PjDydWMJYLgCYuz1jgYyg0iPuvniArqOUC623e
        Z7lM4yAJnrfRmDvH+hxAY0mOwrZCxzzlLS7TODhE+Us0BvvvNT0xVlDLBZyC8gWXaRxgtf0BjcH+e0NP
        jBXcWeJnUwDzEy7TOECU16Gx5l/LR/XEWMFYLgD5CZdpHCRB3o7GkiEH0DiWC5AIl2kM1Hf4hM23q0es
        zwE0Ro/FjsVRu93+MJc7/dB2gPULvQNTfQliFDdkm7ATSDalEo3sIYF+PRFWcrfsj7IAiHKQy51e4MtI
        MDD2vt4kbl7qZdneUHeYen/2JdSXLPPRgZNh2gvTANJhPBnewZHKZU8PCsXCx2H17xxv3AwWL5FptKWI
        Xu8oomSZnFC/Gep7D22ig51FtEF2a+UddnvoMS7/wSHZSC527Fkqj/a3qcaSkf2tRVTOkfkolX/h8h8M
        jkzPe9DhHZdIRs/sLdY1nEzsAY2oFTWjdu7G1CAtCD0BZ/9jGNEGj5tWrPTQ4CpPnMFkLNvHpwJqRx+4
        O5MHdLAGOyLLPXSgvUibX3FGk7EMtXrtsamQx92ZHGJDf54yisNqIqPJVsamAmiHvyc/FWgGfcghkFbs
        rL7EHTMQyJVpeW78sEvmMtSuBkX+C33i7k0M7bn/psUyHWgbM5BqxF0BfWBBED2zuHsTQ3v3j42vNifv
        tjcRUbsWgEl9Q4DDBRqxB58zhG2TmgII9ZNXpRYas29xUpQj7BF+pvwid8sa4CKEgiKRo7Tx0IV7MhI5
        oolv4c1TH+DMT+iUvCai63SMLReoJy/CAiCJcg5vnvrAR+kOQf4PHQvK+/WdB5Z7mrS7f9mUR9xmIl9U
        PoYgsB9KuVfW0m21h2nDgR7GbbWd1J27VXXeRm7iV+e82cwCBOBDp6hc4XdZj5dg0UqdH0dMBa4s8hT+
        aAqc3Q/EDy0vQlCaJJEsWj2n+El+WRpppJGGwcjI+B98B5my1IvQZAAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="timerToast.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>