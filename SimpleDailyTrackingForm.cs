using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public class SimpleDailyTrackingForm : Form
    {
        private int workPeriodId;
        private DateTime startDate;
        private DateTime endDate;
        private string projectName;
        
        private Label lblTitle;
        private DataGridView dataGridViewEmployees;
        private Button btnEditStatus;
        private Button btnClose;

        public SimpleDailyTrackingForm(int workPeriodId, DateTime startDate, DateTime endDate, string projectName)
        {
            this.workPeriodId = workPeriodId;
            this.startDate = startDate;
            this.endDate = endDate;
            this.projectName = projectName;
            
            SetupForm();
            LoadEmployeesData();
        }

        private void SetupForm()
        {
            // إعداد النموذج
            this.Size = new Size(800, 600);
            this.Text = "التتبع اليومي لفترة العمل";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Font = new Font("Cairo", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // العنوان
            lblTitle = new Label();
            lblTitle.Text = $"التتبع اليومي - {projectName}";
            lblTitle.Font = new Font("Cairo", 14F, FontStyle.Bold);
            lblTitle.ForeColor = Color.DarkBlue;
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            lblTitle.Location = new Point(10, 10);
            lblTitle.Size = new Size(760, 40);

            // جدول الموظفين
            dataGridViewEmployees = new DataGridView();
            dataGridViewEmployees.Location = new Point(10, 60);
            dataGridViewEmployees.Size = new Size(760, 450);
            dataGridViewEmployees.AllowUserToAddRows = false;
            dataGridViewEmployees.AllowUserToDeleteRows = false;
            dataGridViewEmployees.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewEmployees.MultiSelect = false;
            dataGridViewEmployees.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewEmployees.RowHeadersVisible = false;
            dataGridViewEmployees.BackgroundColor = Color.White;

            // زر تعديل الحالة
            btnEditStatus = new Button();
            btnEditStatus.Text = "تعديل الحالة اليومية";
            btnEditStatus.Location = new Point(10, 520);
            btnEditStatus.Size = new Size(150, 35);
            btnEditStatus.BackColor = Color.Orange;
            btnEditStatus.ForeColor = Color.White;
            btnEditStatus.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnEditStatus.Click += BtnEditStatus_Click;

            // زر الإغلاق
            btnClose = new Button();
            btnClose.Text = "إغلاق";
            btnClose.Location = new Point(170, 520);
            btnClose.Size = new Size(100, 35);
            btnClose.BackColor = Color.Gray;
            btnClose.ForeColor = Color.White;
            btnClose.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnClose.Click += (s, e) => this.Close();

            // إضافة العناصر للنموذج
            this.Controls.Add(lblTitle);
            this.Controls.Add(dataGridViewEmployees);
            this.Controls.Add(btnEditStatus);
            this.Controls.Add(btnClose);
        }

        private void LoadEmployeesData()
        {
            try
            {
                // الحصول على موظفي فترة العمل
                var employeesData = DatabaseHelper.GetWorkPeriodEmployees(workPeriodId);
                
                // إنشاء جدول للعرض
                var displayTable = new DataTable();
                displayTable.Columns.Add("كود الموظف", typeof(int));
                displayTable.Columns.Add("اسم الموظف", typeof(string));
                displayTable.Columns.Add("إجمالي الأيام", typeof(int));
                displayTable.Columns.Add("أيام الحضور", typeof(int));
                displayTable.Columns.Add("أيام الغياب", typeof(int));
                displayTable.Columns.Add("أيام الإجازة", typeof(int));
                displayTable.Columns.Add("معدل الحضور %", typeof(string));

                foreach (DataRow empRow in employeesData.Rows)
                {
                    int employeeCode = Convert.ToInt32(empRow["EmployeeCode"]);
                    string employeeName = empRow["EmployeeName"].ToString();
                    
                    // الحصول على ملخص التتبع اليومي
                    var (totalDays, presentDays, absentDays, vacationDays, attendanceRate) = 
                        DatabaseHelper.GetDailyWorkStatusSummary(workPeriodId, employeeCode);
                    
                    var newRow = displayTable.NewRow();
                    newRow["كود الموظف"] = employeeCode;
                    newRow["اسم الموظف"] = employeeName;
                    newRow["إجمالي الأيام"] = totalDays;
                    newRow["أيام الحضور"] = presentDays;
                    newRow["أيام الغياب"] = absentDays;
                    newRow["أيام الإجازة"] = vacationDays;
                    newRow["معدل الحضور %"] = $"{attendanceRate:F1}%";
                    
                    displayTable.Rows.Add(newRow);
                }

                dataGridViewEmployees.DataSource = displayTable;
                
                // تنسيق الأعمدة
                foreach (DataGridViewColumn column in dataGridViewEmployees.Columns)
                {
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.HeaderCell.Style.Font = new Font("Cairo", 9F, FontStyle.Bold);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEditStatus_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewEmployees.SelectedRows.Count == 0)
                {
                    MessageBox.Show("الرجاء اختيار موظف لتعديل حالته", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = dataGridViewEmployees.SelectedRows[0];
                int employeeCode = Convert.ToInt32(selectedRow.Cells["كود الموظف"].Value);
                string employeeName = selectedRow.Cells["اسم الموظف"].Value.ToString();

                // فتح نموذج تعديل الحالة اليومية
                var editForm = new SimpleEmployeeStatusFormV2(workPeriodId, employeeCode, employeeName, startDate, endDate);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    // تحديث البيانات
                    LoadEmployeesData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تعديل الحالة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
