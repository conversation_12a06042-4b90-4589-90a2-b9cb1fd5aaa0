using System;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    internal static class Program
    {
        [STAThread]        
        static void Main()
        {        
            
            try
            {
                // تهيئة قاعدة البيانات عند بدء التشغيل
                DatabaseHelper.InitializeDatabase();

                ApplicationConfiguration.Initialize();

                // التحقق من وجود مستخدمين
                if (!DatabaseHelper.HasUsers())
                {
                    // إذا لم يكن هناك مستخدمين، نعرض نموذج إضافة المستخدم الأول
                    var firstUserForm = new UserForm() 
                    { 
                        Text = "إضافة المستخدم الأول للنظام",
                        StartPosition = FormStartPosition.CenterScreen
                    };
                    
                    // إخفاء الأزرار غير الضرورية
                    foreach (Control ctrl in firstUserForm.Controls)
                    {
                        if (ctrl is GroupBox groupBox)
                        {
                            foreach (Control c in groupBox.Controls)
                            {
                                if (c is Button btn)
                                {
                                    if (btn.Text != "إضافة" && btn.Text != "مسح الحقول")
                                        btn.Visible = false;
                                }
                            }
                        }
                        else if (ctrl is DataGridView)
                        {
                            ctrl.Visible = false;
                        }
                    }                    // تعيين نوع المستخدم كمدير بشكل افتراضي مع عدم إتاحة التغيير
                    var cmbUserType = firstUserForm.Controls.Find("cmbUserType", true).FirstOrDefault() as ComboBox;
                    if (cmbUserType != null)
                    {
                        cmbUserType.SelectedIndex = cmbUserType.Items.IndexOf("مدير");
                        cmbUserType.Enabled = false; // منع تغيير نوع المستخدم
                    }
                    
                    // منع إغلاق النموذج حتى يتم إضافة مستخدم
                    firstUserForm.FormClosing += (s, e) => {
                        if (!DatabaseHelper.HasUsers() && e.CloseReason == CloseReason.UserClosing)
                        {
                            e.Cancel = true;
                            MessageBox.Show("يجب إضافة مستخدم مدير أولاً قبل إغلاق النموذج.", "تنبيه", 
                                MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    };

                    // التأكد من أن المستخدم أضاف حساباً قبل إغلاق النموذج
                    firstUserForm.FormClosing += (sender, e) =>
                    {
                        if (!DatabaseHelper.HasUsers())
                        {
                            if (e.CloseReason == CloseReason.UserClosing)
                            {
                                e.Cancel = true;
                                MessageBox.Show("يجب إضافة مستخدم واحد على الأقل قبل المتابعة", "تنبيه",
                                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            }
                        }
                    };

                    // عرض نموذج إضافة المستخدم الأول
                    firstUserForm.ShowDialog();

                    // التحقق مرة أخرى من إضافة مستخدم
                    if (!DatabaseHelper.HasUsers())
                    {
                        return; // إنهاء البرنامج إذا لم يتم إضافة مستخدم
                    }
                }

                // التحقق من وجود مستخدمين
                if (!DatabaseHelper.HasUsers())
                {
                    // إذا لم يكن هناك مستخدمين، نعرض نموذج إضافة المستخدم الأول
                    var firstUserForm = new UserForm() 
                    { 
                        Text = "إضافة المستخدم الأول للنظام",
                        StartPosition = FormStartPosition.CenterScreen
                    };
                    
                    // إخفاء الأزرار غير الضرورية
                    foreach (Control ctrl in firstUserForm.Controls)
                    {
                        if (ctrl is Button btn)
                        {
                            if (btn.Text != "إضافة" && btn.Text != "مسح الحقول")
                                btn.Visible = false;
                        }
                        else if (ctrl is DataGridView)
                        {
                            ctrl.Visible = false;
                        }
                    }

                    // التأكد من أن المستخدم أضاف حساباً قبل إغلاق النموذج
                    firstUserForm.FormClosing += (sender, e) =>
                    {
                        if (!DatabaseHelper.HasUsers())
                        {
                            if (e.CloseReason == CloseReason.UserClosing)
                            {
                                e.Cancel = true;
                                MessageBox.Show("يجب إضافة مستخدم واحد على الأقل قبل المتابعة", "تنبيه",
                                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            }
                        }
                    };

                    // عرض نموذج إضافة المستخدم الأول
                    if (firstUserForm.ShowDialog() != DialogResult.OK && !DatabaseHelper.HasUsers())
                    {
                        return; // إنهاء البرنامج إذا لم يتم إضافة مستخدم
                    }
                }

                // المتابعة إلى شاشة تسجيل الدخول
                //Application.Run(new LoginForm());
                Application.Run(new StartForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء بدء تشغيل البرنامج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}