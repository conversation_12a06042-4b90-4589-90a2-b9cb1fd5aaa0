namespace EmployeeManagementSystem
{
    partial class SettingsForm
    {
        private System.ComponentModel.IContainer components = null;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(SettingsForm));
            pictureBoxLogo = new PictureBox();
            btnSelectLogo = new Button();
            btnBackup = new Button();
            btnRestore = new Button();
            btnSave = new Button();
            btnCancel = new Button();
            btnClear = new Button();
            btn_update = new Button();
            btnBackupDocuments = new Button();
            btnRestoreDocuments = new Button();
            groupBox1 = new GroupBox();
            groupBox2 = new GroupBox();
            cmbTheme = new ComboBox();
            lblTheme = new Label();
            txtDescription = new TextBox();
            lblDescription = new Label();
            txtOrganizationName = new TextBox();
            lblOrganizationName = new Label();
            toolTip1 = new ToolTip(components);
            ((System.ComponentModel.ISupportInitialize)pictureBoxLogo).BeginInit();
            groupBox1.SuspendLayout();
            groupBox2.SuspendLayout();
            SuspendLayout();
            // 
            // pictureBoxLogo
            // 
            pictureBoxLogo.BorderStyle = BorderStyle.FixedSingle;
            pictureBoxLogo.Image = (Image)resources.GetObject("pictureBoxLogo.Image");
            pictureBoxLogo.Location = new Point(23, 33);
            pictureBoxLogo.Margin = new Padding(4, 3, 4, 3);
            pictureBoxLogo.Name = "pictureBoxLogo";
            pictureBoxLogo.Size = new Size(233, 239);
            pictureBoxLogo.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBoxLogo.TabIndex = 8;
            pictureBoxLogo.TabStop = false;
            // 
            // btnSelectLogo
            // 
            btnSelectLogo.BackColor = Color.FromArgb(45, 66, 91);
            btnSelectLogo.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnSelectLogo.ForeColor = Color.White;
            btnSelectLogo.Image = Properties.Resources.image_file_add_32px;
            btnSelectLogo.ImageAlign = ContentAlignment.MiddleLeft;
            btnSelectLogo.Location = new Point(23, 282);
            btnSelectLogo.Margin = new Padding(4, 3, 4, 3);
            btnSelectLogo.Name = "btnSelectLogo";
            btnSelectLogo.Size = new Size(233, 46);
            btnSelectLogo.TabIndex = 7;
            btnSelectLogo.Text = "اختيار الشعار";
            toolTip1.SetToolTip(btnSelectLogo, "اختيار الشعار");
            btnSelectLogo.UseVisualStyleBackColor = false;
            btnSelectLogo.Click += btnSelectLogo_Click;
            // 
            // btnBackup
            // 
            btnBackup.BackColor = Color.FromArgb(45, 66, 91);
            btnBackup.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnBackup.ForeColor = Color.White;
            btnBackup.Location = new Point(274, 35);
            btnBackup.Margin = new Padding(4, 3, 4, 3);
            btnBackup.Name = "btnBackup";
            btnBackup.Size = new Size(270, 46);
            btnBackup.TabIndex = 4;
            btnBackup.Text = "نسخة احتياطية من قاعدة البيانات";
            toolTip1.SetToolTip(btnBackup, "نسخة احتياطية من قاعدة البيانات");
            btnBackup.UseVisualStyleBackColor = false;
            btnBackup.Click += btnBackup_Click;
            // 
            // btnRestore
            // 
            btnRestore.BackColor = Color.FromArgb(45, 66, 91);
            btnRestore.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnRestore.ForeColor = Color.White;
            btnRestore.Location = new Point(17, 35);
            btnRestore.Margin = new Padding(4, 3, 4, 3);
            btnRestore.Name = "btnRestore";
            btnRestore.Size = new Size(249, 46);
            btnRestore.TabIndex = 3;
            btnRestore.Text = "استعادة نسخة قاعدة البيانات";
            toolTip1.SetToolTip(btnRestore, "استعادة نسخة قاعدة البيانات");
            btnRestore.UseVisualStyleBackColor = false;
            btnRestore.Click += btnRestore_Click;
            // 
            // btnSave
            // 
            btnSave.BackColor = Color.FromArgb(45, 66, 91);
            btnSave.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnSave.ForeColor = Color.White;
            btnSave.Image = Properties.Resources.ok_32px;
            btnSave.ImageAlign = ContentAlignment.MiddleLeft;
            btnSave.Location = new Point(273, 334);
            btnSave.Margin = new Padding(4, 3, 4, 3);
            btnSave.Name = "btnSave";
            btnSave.Size = new Size(145, 46);
            btnSave.TabIndex = 2;
            btnSave.Text = "حفظ";
            toolTip1.SetToolTip(btnSave, "حفظ الاعدادات");
            btnSave.UseVisualStyleBackColor = false;
            btnSave.Click += btnSave_Click;
            // 
            // btnCancel
            // 
            btnCancel.BackColor = Color.FromArgb(45, 66, 91);
            btnCancel.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnCancel.ForeColor = Color.White;
            btnCancel.Image = Properties.Resources.delete_32px;
            btnCancel.ImageAlign = ContentAlignment.MiddleLeft;
            btnCancel.Location = new Point(273, 386);
            btnCancel.Margin = new Padding(4, 3, 4, 3);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(145, 46);
            btnCancel.TabIndex = 1;
            btnCancel.Text = "إلغاء";
            toolTip1.SetToolTip(btnCancel, "إلغاء");
            btnCancel.UseVisualStyleBackColor = false;
            btnCancel.Click += btnCancel_Click;
            // 
            // btnClear
            // 
            btnClear.BackColor = Color.FromArgb(45, 66, 91);
            btnClear.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnClear.ForeColor = Color.White;
            btnClear.Image = Properties.Resources.clear_formatting_32px;
            btnClear.ImageAlign = ContentAlignment.MiddleLeft;
            btnClear.Location = new Point(23, 334);
            btnClear.Margin = new Padding(4, 3, 4, 3);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(233, 46);
            btnClear.TabIndex = 0;
            btnClear.Text = "إفراغ الحقول";
            toolTip1.SetToolTip(btnClear, "إفراغ الحقول");
            btnClear.UseVisualStyleBackColor = false;
            btnClear.Click += btnClear_Click;
            // 
            // btn_update
            // 
            btn_update.BackColor = Color.FromArgb(45, 66, 91);
            btn_update.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btn_update.ForeColor = Color.White;
            btn_update.Image = Properties.Resources.available_updates_32px;
            btn_update.ImageAlign = ContentAlignment.MiddleLeft;
            btn_update.Location = new Point(23, 386);
            btn_update.Margin = new Padding(4, 3, 4, 3);
            btn_update.Name = "btn_update";
            btn_update.Size = new Size(233, 46);
            btn_update.TabIndex = 1;
            btn_update.Text = "تحديث البرنامج";
            toolTip1.SetToolTip(btn_update, "تحديث البرنامج");
            btn_update.UseVisualStyleBackColor = false;
            btn_update.Click += btn_update_Click;
            // 
            // btnBackupDocuments
            // 
            btnBackupDocuments.BackColor = Color.FromArgb(45, 66, 91);
            btnBackupDocuments.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnBackupDocuments.ForeColor = Color.White;
            btnBackupDocuments.Location = new Point(274, 87);
            btnBackupDocuments.Margin = new Padding(4, 3, 4, 3);
            btnBackupDocuments.Name = "btnBackupDocuments";
            btnBackupDocuments.Size = new Size(270, 46);
            btnBackupDocuments.TabIndex = 4;
            btnBackupDocuments.Text = "نسخة احتياطية من مجلد الملفات";
            toolTip1.SetToolTip(btnBackupDocuments, "نسخة احتياطية من مجلد الملفات");
            btnBackupDocuments.UseVisualStyleBackColor = false;
            btnBackupDocuments.Click += btnBackupDocuments_Click;
            // 
            // btnRestoreDocuments
            // 
            btnRestoreDocuments.BackColor = Color.FromArgb(45, 66, 91);
            btnRestoreDocuments.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnRestoreDocuments.ForeColor = Color.White;
            btnRestoreDocuments.Location = new Point(17, 87);
            btnRestoreDocuments.Margin = new Padding(4, 3, 4, 3);
            btnRestoreDocuments.Name = "btnRestoreDocuments";
            btnRestoreDocuments.Size = new Size(249, 46);
            btnRestoreDocuments.TabIndex = 3;
            btnRestoreDocuments.Text = "استعادة نسخة مجلد الملفات";
            toolTip1.SetToolTip(btnRestoreDocuments, "استعادة نسخة مجلد الملفات");
            btnRestoreDocuments.UseVisualStyleBackColor = false;
            btnRestoreDocuments.Click += btnRestoreDocuments_Click;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(btnRestore);
            groupBox1.Controls.Add(btnBackup);
            groupBox1.Controls.Add(btnBackupDocuments);
            groupBox1.Controls.Add(btnRestoreDocuments);
            groupBox1.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBox1.Location = new Point(448, 299);
            groupBox1.Name = "groupBox1";
            groupBox1.RightToLeft = RightToLeft.Yes;
            groupBox1.Size = new Size(564, 148);
            groupBox1.TabIndex = 13;
            groupBox1.TabStop = false;
            groupBox1.Text = "النسخ الاحتياطي";
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(cmbTheme);
            groupBox2.Controls.Add(lblTheme);
            groupBox2.Controls.Add(txtDescription);
            groupBox2.Controls.Add(lblDescription);
            groupBox2.Controls.Add(txtOrganizationName);
            groupBox2.Controls.Add(lblOrganizationName);
            groupBox2.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBox2.Location = new Point(448, 23);
            groupBox2.Name = "groupBox2";
            groupBox2.RightToLeft = RightToLeft.Yes;
            groupBox2.Size = new Size(564, 270);
            groupBox2.TabIndex = 14;
            groupBox2.TabStop = false;
            groupBox2.Text = "معلومات المؤسسة";
            // 
            // cmbTheme
            // 
            cmbTheme.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbTheme.Font = new Font("Cairo", 12F, FontStyle.Bold);
            cmbTheme.FormattingEnabled = true;
            cmbTheme.Items.AddRange(new object[] { "افتراضي", "فاتح", "مظلم" });
            cmbTheme.Location = new Point(17, 215);
            cmbTheme.Margin = new Padding(4, 3, 4, 3);
            cmbTheme.Name = "cmbTheme";
            cmbTheme.RightToLeft = RightToLeft.No;
            cmbTheme.Size = new Size(368, 38);
            cmbTheme.TabIndex = 13;
            toolTip1.SetToolTip(cmbTheme, "اختيار المظهر");
            // 
            // lblTheme
            // 
            lblTheme.AutoSize = true;
            lblTheme.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblTheme.Location = new Point(400, 218);
            lblTheme.Margin = new Padding(4, 0, 4, 0);
            lblTheme.Name = "lblTheme";
            lblTheme.Size = new Size(68, 30);
            lblTheme.TabIndex = 14;
            lblTheme.Text = "المظهر";
            // 
            // txtDescription
            // 
            txtDescription.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtDescription.Location = new Point(17, 88);
            txtDescription.Margin = new Padding(4, 3, 4, 3);
            txtDescription.Multiline = true;
            txtDescription.Name = "txtDescription";
            txtDescription.RightToLeft = RightToLeft.No;
            txtDescription.Size = new Size(368, 115);
            txtDescription.TabIndex = 15;
            txtDescription.TextAlign = HorizontalAlignment.Right;
            // 
            // lblDescription
            // 
            lblDescription.AutoSize = true;
            lblDescription.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblDescription.Location = new Point(400, 92);
            lblDescription.Margin = new Padding(4, 0, 4, 0);
            lblDescription.Name = "lblDescription";
            lblDescription.Size = new Size(127, 30);
            lblDescription.TabIndex = 16;
            lblDescription.Text = "وصف المؤسسة";
            // 
            // txtOrganizationName
            // 
            txtOrganizationName.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtOrganizationName.Location = new Point(17, 30);
            txtOrganizationName.Margin = new Padding(4, 3, 4, 3);
            txtOrganizationName.Name = "txtOrganizationName";
            txtOrganizationName.RightToLeft = RightToLeft.No;
            txtOrganizationName.Size = new Size(368, 37);
            txtOrganizationName.TabIndex = 17;
            txtOrganizationName.TextAlign = HorizontalAlignment.Right;
            // 
            // lblOrganizationName
            // 
            lblOrganizationName.AutoSize = true;
            lblOrganizationName.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblOrganizationName.Location = new Point(400, 34);
            lblOrganizationName.Margin = new Padding(4, 0, 4, 0);
            lblOrganizationName.Name = "lblOrganizationName";
            lblOrganizationName.Size = new Size(118, 30);
            lblOrganizationName.TabIndex = 18;
            lblOrganizationName.Text = "اسم المؤسسة";
            // 
            // SettingsForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1029, 461);
            Controls.Add(groupBox2);
            Controls.Add(groupBox1);
            Controls.Add(btnClear);
            Controls.Add(btn_update);
            Controls.Add(btnCancel);
            Controls.Add(btnSave);
            Controls.Add(btnSelectLogo);
            Controls.Add(pictureBoxLogo);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Icon = (Icon)resources.GetObject("$this.Icon");
            Margin = new Padding(4, 3, 4, 3);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "SettingsForm";
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "الإعدادات";
            ((System.ComponentModel.ISupportInitialize)pictureBoxLogo).EndInit();
            groupBox1.ResumeLayout(false);
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            ResumeLayout(false);
        }
        private System.Windows.Forms.PictureBox pictureBoxLogo;
        private System.Windows.Forms.Button btnSelectLogo;
        private System.Windows.Forms.Button btnBackup;
        private System.Windows.Forms.Button btnRestore;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnClear;
        private Button btn_update;
        private Button btnBackupDocuments;
        private Button btnRestoreDocuments;
        private GroupBox groupBox1;
        private GroupBox groupBox2;
        private ComboBox cmbTheme;
        private Label lblTheme;
        private TextBox txtDescription;
        private Label lblDescription;
        private TextBox txtOrganizationName;
        private Label lblOrganizationName;
        private ToolTip toolTip1;
    }
}