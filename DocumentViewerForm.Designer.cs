
namespace EmployeeManagementSystem
{
    partial class DocumentViewerForm
    {
        private System.ComponentModel.IContainer components = null;
        private System.Windows.Forms.WebBrowser webBrowser1;
        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.Button btnPrint;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            webBrowser1 = new WebBrowser();
            pictureBox1 = new PictureBox();
            btnPrint = new Button();
            ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();
            SuspendLayout();
            // 
            // webBrowser1
            // 
            webBrowser1.Dock = DockStyle.Fill;
            webBrowser1.Location = new Point(0, 0);
            webBrowser1.Margin = new Padding(3, 2, 3, 2);
            webBrowser1.MinimumSize = new Size(18, 15);
            webBrowser1.Name = "webBrowser1";
            webBrowser1.Size = new Size(884, 681);
            webBrowser1.TabIndex = 0;
            // 
            // pictureBox1
            // 
            pictureBox1.Dock = DockStyle.Fill;
            pictureBox1.Location = new Point(0, 0);
            pictureBox1.Margin = new Padding(3, 2, 3, 2);
            pictureBox1.Name = "pictureBox1";
            pictureBox1.Size = new Size(884, 681);
            pictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBox1.TabIndex = 1;
            pictureBox1.TabStop = false;
            // 
            // btnPrint
            // 
            btnPrint.BackColor = Color.FromArgb(45, 66, 91);
            btnPrint.Dock = DockStyle.Bottom;
            btnPrint.FlatAppearance.BorderSize = 0;
            btnPrint.FlatStyle = FlatStyle.Flat;
            btnPrint.Font = new Font("Cairo", 14.25F, FontStyle.Bold, GraphicsUnit.Point);
            btnPrint.ForeColor = Color.White;
            btnPrint.Location = new Point(0, 630);
            btnPrint.Margin = new Padding(3, 2, 3, 2);
            btnPrint.Name = "btnPrint";
            btnPrint.Size = new Size(884, 51);
            btnPrint.TabIndex = 2;
            btnPrint.Text = "طباعة";
            btnPrint.UseVisualStyleBackColor = false;
            btnPrint.Click += PrintPdf;
            // 
            // DocumentViewerForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(884, 681);
            Controls.Add(btnPrint);
            Controls.Add(pictureBox1);
            Controls.Add(webBrowser1);
            Margin = new Padding(3, 2, 3, 2);
            MaximizeBox = false;
            MinimizeBox = false;
            MinimumSize = new Size(352, 235);
            Name = "DocumentViewerForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "عرض الوثيقة";
            ((System.ComponentModel.ISupportInitialize)pictureBox1).EndInit();
            ResumeLayout(false);
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            throw new NotImplementedException();
        }
    }
}