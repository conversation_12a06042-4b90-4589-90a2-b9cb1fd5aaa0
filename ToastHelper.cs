﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using EmployeeManagementSystem.ToastGui;

namespace EmployeeManagementSystem
{
    public static class ToastHelper
    {
        public static void ShowAddToast()
        {
            ToastForm.Instance("اضافة بيانات", "تم اضافة البيانات بنجاح").Show();
        }

        public static void ShowEditToast()
        {
            ToastForm.Instance("تعديل بيانات", "تم تعديل البيانات بنجاح").Show();
        }

        public static void ShowDeleteToast()
        {
            ToastForm.Instance("حذف بيانات", "تم حذف البيانات بنجاح").Show();
        }

        public static void ShowfileToast()
        {
            ToastForm.Instance("اضافة ملف", "تم اضافة ملف بنجاح").Show();
        }

        public static void ShowDeletefileToast()
        {
            ToastForm.Instance("حذف ملف", "تم حذف الملف بنجاح").Show();
        }

        public static void ShowDeleteNotificationsToast()
        {
            ToastForm.Instance("حذف اشعار", "تم حذف الاشعار بنجاح").Show();
        }

        public static void ShowSettingsToast()
        {
            ToastForm.Instance("حفظ الاعدادات", "تم حفظ الاعدادات بنجاح").Show();
        }
    }
}
