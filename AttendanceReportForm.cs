using System;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace EmployeeManagementSystem
{
    public partial class AttendanceReportForm : Form
    {
        private DataTable currentReportData;

        public AttendanceReportForm()
        {
            InitializeComponent();
        }

        private void AttendanceReportForm_Load(object sender, EventArgs e)
        {
            LoadEmployees();
            SetDefaultDates();
            cmbReportType.SelectedIndex = 0; // تقرير شامل
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = DatabaseHelper.GetEmployeesForAttendance();
                cmbEmployee.Items.Clear();
                cmbEmployee.Items.Add("جميع الموظفين");

                foreach (DataRow row in employees.Rows)
                {
                    string employeeInfo = $"{row["Name"]} - {row["EmployeeCode"]}";
                    cmbEmployee.Items.Add(employeeInfo);
                }

                cmbEmployee.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetDefaultDates()
        {
            dateTimePickerStart.Value = DateTime.Today.AddDays(-30);
            dateTimePickerEnd.Value = DateTime.Today;
        }

        private async void btnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                ShowProgress(true);
                await Task.Delay(100); // للسماح بتحديث الواجهة

                await Task.Run(() => GenerateReport());
                
                ShowProgress(false);
                MessageBox.Show("تم إنشاء التقرير بنجاح", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowProgress(false);
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateReport()
        {
            try
            {
                // الحصول على القيم من الواجهة في الخيط الرئيسي
                DateTime startDate = DateTime.Today;
                DateTime endDate = DateTime.Today;
                int selectedEmployeeIndex = 0;
                string selectedEmployeeText = "";
                string reportType = "تقرير شامل";

                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        startDate = dateTimePickerStart.Value.Date;
                        endDate = dateTimePickerEnd.Value.Date;
                        selectedEmployeeIndex = cmbEmployee.SelectedIndex;
                        selectedEmployeeText = cmbEmployee.SelectedItem?.ToString() ?? "";
                        reportType = cmbReportType.SelectedItem?.ToString() ?? "تقرير شامل";
                    }));
                }
                else
                {
                    startDate = dateTimePickerStart.Value.Date;
                    endDate = dateTimePickerEnd.Value.Date;
                    selectedEmployeeIndex = cmbEmployee.SelectedIndex;
                    selectedEmployeeText = cmbEmployee.SelectedItem?.ToString() ?? "";
                    reportType = cmbReportType.SelectedItem?.ToString() ?? "تقرير شامل";
                }

                // الحصول على بيانات التقرير
                DataTable reportData = GetReportDataSafe(startDate, endDate, selectedEmployeeIndex, selectedEmployeeText, reportType);
                currentReportData = reportData;

                // تحديث الواجهة في الخيط الرئيسي
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        SetupReportDisplay(reportData, selectedEmployeeIndex, selectedEmployeeText, reportType);
                    }));
                }
                else
                {
                    SetupReportDisplay(reportData, selectedEmployeeIndex, selectedEmployeeText, reportType);
                }
            }
            catch (Exception ex)
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }));
                }
                else
                {
                    MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private DataTable GetReportData()
        {
            DataTable data;

            // تحديد نطاق التاريخ
            DateTime startDate = dateTimePickerStart.Value.Date;
            DateTime endDate = dateTimePickerEnd.Value.Date;

            // تحديد الموظف
            if (cmbEmployee.SelectedIndex > 0)
            {
                try
                {
                    string selectedText = cmbEmployee.SelectedItem.ToString();
                    string[] parts = selectedText.Split('-');
                    if (parts.Length >= 2)
                    {
                        int employeeCode = int.Parse(parts[1].Trim());
                        data = DatabaseHelper.GetAttendanceByEmployee(employeeCode);

                        // تصفية بالتاريخ
                        if (data.Rows.Count > 0)
                        {
                            var filteredRows = data.AsEnumerable()
                                .Where(row =>
                                {
                                    string dateStr = row.Field<string>("التاريخ");
                                    if (DateTime.TryParse(dateStr, out DateTime rowDate))
                                    {
                                        return rowDate >= startDate && rowDate <= endDate;
                                    }
                                    return false;
                                });

                            if (filteredRows.Any())
                            {
                                data = filteredRows.CopyToDataTable();
                            }
                            else
                            {
                                data.Clear();
                            }
                        }
                    }
                    else
                    {
                        data = DatabaseHelper.GetAttendanceByDateRange(startDate, endDate);
                    }
                }
                catch
                {
                    data = DatabaseHelper.GetAttendanceByDateRange(startDate, endDate);
                }
            }
            else
            {
                data = DatabaseHelper.GetAttendanceByDateRange(startDate, endDate);
            }

            // تطبيق فلتر نوع التقرير
            string reportType = cmbReportType.SelectedItem?.ToString() ?? "تقرير شامل";
            data = ApplyReportTypeFilter(data, reportType);

            return data;
        }

        private DataTable GetReportDataSafe(DateTime startDate, DateTime endDate, int selectedEmployeeIndex, string selectedEmployeeText, string reportType)
        {
            DataTable data;

            // إذا كان تقرير فترات العمل، استخدم دالة منفصلة
            if (reportType == "تقرير فترات العمل")
            {
                return GetWorkPeriodsReportData(startDate, endDate, selectedEmployeeIndex, selectedEmployeeText);
            }

            // تحديد الموظف
            if (selectedEmployeeIndex > 0)
            {
                try
                {
                    string[] parts = selectedEmployeeText.Split('-');
                    if (parts.Length >= 2)
                    {
                        int employeeCode = int.Parse(parts[1].Trim());
                        data = DatabaseHelper.GetAttendanceByEmployee(employeeCode);

                        // تصفية بالتاريخ
                        if (data.Rows.Count > 0)
                        {
                            var filteredRows = data.AsEnumerable()
                                .Where(row =>
                                {
                                    string dateStr = row.Field<string>("التاريخ");
                                    if (DateTime.TryParse(dateStr, out DateTime rowDate))
                                    {
                                        return rowDate >= startDate && rowDate <= endDate;
                                    }
                                    return false;
                                });

                            if (filteredRows.Any())
                            {
                                data = filteredRows.CopyToDataTable();
                            }
                            else
                            {
                                data.Clear();
                            }
                        }
                    }
                    else
                    {
                        data = DatabaseHelper.GetAttendanceByDateRange(startDate, endDate);
                    }
                }
                catch
                {
                    data = DatabaseHelper.GetAttendanceByDateRange(startDate, endDate);
                }
            }
            else
            {
                data = DatabaseHelper.GetAttendanceByDateRange(startDate, endDate);
            }

            // تطبيق فلتر نوع التقرير
            data = ApplyReportTypeFilter(data, reportType);

            return data;
        }

        private DataTable GetWorkPeriodsReportData(DateTime startDate, DateTime endDate, int selectedEmployeeIndex, string selectedEmployeeText)
        {
            try
            {
                DataTable workPeriodsData;

                if (selectedEmployeeIndex > 0)
                {
                    // تقرير لموظف محدد
                    string[] parts = selectedEmployeeText.Split('-');
                    if (parts.Length >= 2)
                    {
                        int employeeCode = int.Parse(parts[1].Trim());
                        workPeriodsData = DatabaseHelper.GetWorkPeriodsByEmployee(employeeCode, startDate, endDate);
                    }
                    else
                    {
                        workPeriodsData = DatabaseHelper.GetAllWorkPeriods(startDate, endDate);
                    }
                }
                else
                {
                    // تقرير لجميع الموظفين
                    workPeriodsData = DatabaseHelper.GetAllWorkPeriods(startDate, endDate);
                }

                return workPeriodsData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على تقرير فترات العمل: {ex.Message}");
                return new DataTable();
            }
        }

        private DataTable ApplyReportTypeFilter(DataTable data, string reportType)
        {
            if (reportType == "تقرير شامل" || data.Rows.Count == 0)
                return data;

            try
            {
                var filteredRows = data.AsEnumerable().Where(row =>
                {
                    try
                    {
                        string status = "";
                        if (data.Columns.Contains("الحالة"))
                        {
                            status = row.Field<string>("الحالة") ?? "";
                        }

                        return reportType switch
                        {
                            "الحضور فقط" => status == "حاضر",
                            "الغياب فقط" => status == "غائب",
                            "المتأخرين فقط" => status == "متأخر",
                            "الساعات الإضافية" => data.Columns.Contains("الساعات الإضافية") &&
                                                   Convert.ToDouble(row.Field<object>("الساعات الإضافية") ?? 0) > 0,
                            _ => true
                        };
                    }
                    catch
                    {
                        return true; // في حالة خطأ، اعرض الصف
                    }
                });

                if (filteredRows.Any())
                {
                    return filteredRows.CopyToDataTable();
                }
                else
                {
                    data.Clear();
                    return data;
                }
            }
            catch
            {
                return data; // في حالة خطأ، ارجع البيانات كما هي
            }
        }

        private void CalculateAndDisplaySummary(DataTable data)
        {
            if (data.Rows.Count == 0)
            {
                ResetSummaryLabels();
                return;
            }

            try
            {
                // التحقق من نوع التقرير
                string reportType = cmbReportType.SelectedItem?.ToString() ?? "تقرير شامل";

                if (reportType == "تقرير فترات العمل")
                {
                    CalculateWorkPeriodsSummary(data);
                    return;
                }

                int totalDays = data.Rows.Count;
                int presentDays = 0;
                int lateDays = 0;
                int absentDays = 0;
                double totalWorkingHours = 0;
                double totalOvertimeHours = 0;

                // التحقق من وجود الأعمدة قبل الوصول إليها
                bool hasStatusColumn = data.Columns.Contains("الحالة");
                bool hasWorkingHoursColumn = data.Columns.Contains("ساعات العمل");
                bool hasOvertimeHoursColumn = data.Columns.Contains("الساعات الإضافية");

                foreach (DataRow row in data.Rows)
                {
                    try
                    {
                        if (hasStatusColumn)
                        {
                            string status = row.Field<string>("الحالة") ?? "";
                            switch (status)
                            {
                                case "حاضر": presentDays++; break;
                                case "متأخر": lateDays++; break;
                                case "غائب": absentDays++; break;
                            }
                        }

                        if (hasWorkingHoursColumn)
                        {
                            totalWorkingHours += Convert.ToDouble(row.Field<object>("ساعات العمل") ?? 0);
                        }

                        if (hasOvertimeHoursColumn)
                        {
                            totalOvertimeHours += Convert.ToDouble(row.Field<object>("الساعات الإضافية") ?? 0);
                        }
                    }
                    catch
                    {
                        // تجاهل الأخطاء في الصفوف الفردية
                        continue;
                    }
                }

                double averageWorkingHours = totalDays > 0 ? totalWorkingHours / totalDays : 0;

                // تنسيق ملخص التقرير بشكل احترافي ومنظم
                DisplayProfessionalSummary(totalDays, presentDays, lateDays, absentDays,
                                         totalWorkingHours, totalOvertimeHours, averageWorkingHours);
            }
            catch (Exception ex)
            {
                // في حالة خطأ، اعرض رسالة خطأ واستخدم القيم الافتراضية
                ResetSummaryLabels();
                MessageBox.Show($"خطأ في حساب الملخص: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void CalculateWorkPeriodsSummary(DataTable data)
        {
            try
            {
                int totalPeriods = data.Rows.Count;
                int activePeriods = 0;
                int completedPeriods = 0;
                double totalPlannedHours = 0;

                foreach (DataRow row in data.Rows)
                {
                    try
                    {
                        if (data.Columns.Contains("الحالة"))
                        {
                            string status = row.Field<string>("الحالة") ?? "";
                            switch (status)
                            {
                                case "نشط": activePeriods++; break;
                                case "مكتمل": completedPeriods++; break;
                            }
                        }

                        if (data.Columns.Contains("ساعات العمل اليومية"))
                        {
                            double dailyHours = Convert.ToDouble(row.Field<object>("ساعات العمل اليومية") ?? 0);

                            // حساب عدد الأيام في الفترة (تقدير)
                            if (data.Columns.Contains("تاريخ البداية") && data.Columns.Contains("تاريخ النهاية"))
                            {
                                string startDateStr = row.Field<string>("تاريخ البداية") ?? "";
                                string endDateStr = row.Field<string>("تاريخ النهاية") ?? "";

                                if (DateTime.TryParse(startDateStr, out DateTime startDate) &&
                                    DateTime.TryParse(endDateStr, out DateTime endDate))
                                {
                                    int totalDays = (int)(endDate - startDate).TotalDays + 1;
                                    totalPlannedHours += dailyHours * totalDays;
                                }
                            }
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }

                lblTotalDays.Text = $"إجمالي الفترات: {totalPeriods}";
                lblTotalPresent.Text = $"فترات نشطة: {activePeriods}";
                lblTotalLate.Text = $"فترات مكتملة: {completedPeriods}";
                lblTotalAbsent.Text = $"فترات أخرى: {totalPeriods - activePeriods - completedPeriods}";
                lblTotalWorkingHours.Text = $"إجمالي الساعات المخططة: {totalPlannedHours:F1}";
                lblTotalOvertimeHours.Text = $"متوسط الساعات اليومية: {(totalPeriods > 0 ? totalPlannedHours / totalPeriods : 0):F1}";
                lblAverageWorkingHours.Text = $"أماكن عمل مختلفة: {GetUniqueWorkPlaces(data)}";
            }
            catch (Exception ex)
            {
                ResetSummaryLabels();
                System.Diagnostics.Debug.WriteLine($"خطأ في حساب ملخص فترات العمل: {ex.Message}");
            }
        }

        private int GetUniqueWorkPlaces(DataTable data)
        {
            try
            {
                if (!data.Columns.Contains("مكان العمل"))
                    return 0;

                var uniquePlaces = data.AsEnumerable()
                    .Select(row => row.Field<string>("مكان العمل") ?? "")
                    .Where(place => !string.IsNullOrEmpty(place))
                    .Distinct()
                    .Count();

                return uniquePlaces;
            }
            catch
            {
                return 0;
            }
        }

        private void ResetSummaryLabels()
        {
            lblTotalDays.Text = "لم يتم إنشاء تقرير بعد";
            lblTotalPresent.Text = "الرجاء اختيار الفترة الزمنية والضغط على 'إنشاء التقرير'";
            lblTotalLate.Text = "";
            lblTotalAbsent.Text = "";
            lblTotalWorkingHours.Text = "";
            lblTotalOvertimeHours.Text = "";
            lblAverageWorkingHours.Text = "";
            lblEmployeeTitle.Visible = false;
            lblCreationDate.Text = "";
        }

        private void FormatDataGridView()
        {
            try
            {
                if (dataGridViewReport.Columns.Count > 0)
                {
                    foreach (DataGridViewColumn column in dataGridViewReport.Columns)
                    {
                        column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    }

                    // تلوين الحالات
                    if (dataGridViewReport.Columns.Contains("الحالة"))
                    {
                        foreach (DataGridViewRow row in dataGridViewReport.Rows)
                        {
                            try
                            {
                                if (row.Cells["الحالة"].Value != null)
                                {
                                    string status = row.Cells["الحالة"].Value.ToString();
                                    switch (status)
                                    {
                                        case "حاضر":
                                            row.Cells["الحالة"].Style.ForeColor = System.Drawing.Color.Green;
                                            break;
                                        case "متأخر":
                                            row.Cells["الحالة"].Style.ForeColor = System.Drawing.Color.Orange;
                                            break;
                                        case "غائب":
                                            row.Cells["الحالة"].Style.ForeColor = System.Drawing.Color.Red;
                                            break;
                                        case "إجازة":
                                            row.Cells["الحالة"].Style.ForeColor = System.Drawing.Color.Green;
                                            row.Cells["الحالة"].Style.BackColor = System.Drawing.Color.LightGreen;
                                            break;
                                        case "حضور":
                                            row.Cells["الحالة"].Style.ForeColor = System.Drawing.Color.Blue;
                                            row.Cells["الحالة"].Style.BackColor = System.Drawing.Color.LightBlue;
                                            break;
                                        case "هروب":
                                            row.Cells["الحالة"].Style.ForeColor = System.Drawing.Color.White;
                                            row.Cells["الحالة"].Style.BackColor = System.Drawing.Color.Red;
                                            row.Cells["الحالة"].Style.Font = new System.Drawing.Font(row.Cells["الحالة"].Style.Font, System.Drawing.FontStyle.Bold);
                                            break;
                                    }
                                }
                            }
                            catch
                            {
                                // تجاهل أخطاء الصفوف الفردية
                                continue;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // في حالة خطأ، لا تفعل شيئاً
                System.Diagnostics.Debug.WriteLine($"خطأ في تنسيق DataGridView: {ex.Message}");
            }
        }

        private void ShowProgress(bool show)
        {
            progressBar.Visible = show;
            lblProgress.Visible = show;
            
            if (show)
            {
                progressBar.Style = ProgressBarStyle.Marquee;
            }
        }

        private void btnExportExcel_Click(object sender, EventArgs e)
        {
            if (currentReportData == null || currentReportData.Rows.Count == 0)
            {
                MessageBox.Show("لا توجد بيانات للتصدير. الرجاء إنشاء التقرير أولاً.", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                SaveFileDialog saveDialog = new SaveFileDialog
                {
                    Filter = "CSV Files (*.csv)|*.csv",
                    DefaultExt = "csv",
                    FileName = $"تقرير_الحضور_{DateTime.Now:yyyy-MM-dd}.csv"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToCSV(currentReportData, saveDialog.FileName);
                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToCSV(DataTable data, string filePath)
        {
            StringBuilder csv = new StringBuilder();
            
            // إضافة رؤوس الأعمدة
            string[] columnNames = data.Columns.Cast<DataColumn>()
                .Select(column => column.ColumnName).ToArray();
            csv.AppendLine(string.Join(",", columnNames));

            // إضافة البيانات
            foreach (DataRow row in data.Rows)
            {
                string[] fields = row.ItemArray.Select(field => 
                    $"\"{field?.ToString().Replace("\"", "\"\"")}\"").ToArray();
                csv.AppendLine(string.Join(",", fields));
            }

            File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
        }

        private void btnPrintReport_Click(object sender, EventArgs e)
        {
            if (currentReportData == null || currentReportData.Rows.Count == 0)
            {
                MessageBox.Show("لا توجد بيانات للطباعة. الرجاء إنشاء التقرير أولاً.", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                string htmlReport = GenerateHTMLReport();
                string tempFile = Path.Combine(Path.GetTempPath(), $"attendance_report_{DateTime.Now:yyyyMMdd_HHmmss}.html");
                File.WriteAllText(tempFile, htmlReport, Encoding.UTF8);
                
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GenerateHTMLReport()
        {
            StringBuilder html = new StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير الحضور والغياب</title>
    <style>
        @page { size: A4; margin: 2cm; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; direction: rtl; font-size: 12px; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { font-size: 18px; margin-bottom: 10px; color: #333; }
        .employee-title { text-align: center; font-size: 14px; font-weight: bold; margin-bottom: 20px; color: #555; }
        .summary { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .summary p { margin: 5px 0; font-size: 11px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 10px; }
        th, td { border: 1px solid #ddd; padding: 6px; text-align: center; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .present { color: green; font-weight: bold; }
        .late { color: orange; font-weight: bold; }
        .absent { color: red; font-weight: bold; }
        .footer { text-align: center; margin-top: 30px; font-size: 10px; color: #666; }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class=""header"">
        <h1>تقرير الحضور والغياب</h1>");

            // إضافة اسم الموظف إذا كان تقرير لموظف واحد
            if (lblEmployeeTitle.Visible && !string.IsNullOrEmpty(lblEmployeeTitle.Text))
            {
                html.Append($@"<div class=""employee-title"">{lblEmployeeTitle.Text}</div>");
            }

            html.Append(@"</div>");

            // إضافة الملخص
            html.Append(@"<div class=""summary"">
        <h3>ملخص التقرير</h3>
        <p>" + lblTotalDays.Text + @"</p>
        <p>" + lblTotalPresent.Text + @"</p>
    </div>");

            // إضافة الجدول
            html.Append("<table><thead><tr>");
            foreach (DataColumn column in currentReportData.Columns)
            {
                html.AppendFormat("<th>{0}</th>", column.ColumnName);
            }
            html.Append("</tr></thead><tbody>");

            foreach (DataRow row in currentReportData.Rows)
            {
                html.Append("<tr>");
                foreach (var item in row.ItemArray)
                {
                    string cellValue = item?.ToString() ?? "";
                    string cssClass = "";
                    
                    if (cellValue == "حاضر") cssClass = "present";
                    else if (cellValue == "متأخر") cssClass = "late";
                    else if (cellValue == "غائب") cssClass = "absent";
                    else if (cellValue == "إجازة") cssClass = "vacation";
                    else if (cellValue == "حضور") cssClass = "attendance";
                    else if (cellValue == "هروب") cssClass = "escape";
                    
                    html.AppendFormat("<td class=\"{0}\">{1}</td>", cssClass, cellValue);
                }
                html.Append("</tr>");
            }

            html.Append("</tbody></table>");

            // إضافة تاريخ الإنشاء في النهاية
            html.Append($@"<div class=""footer"">تاريخ الإنشاء: {DateTime.Now:dd/MM/yyyy HH:mm}</div>");

            html.Append("</body></html>");
            return html.ToString();
        }

        private void dataGridViewReport_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            // منع ظهور رسالة الخطأ الافتراضية
            e.Cancel = true;

            // تسجيل الخطأ للتشخيص
            System.Diagnostics.Debug.WriteLine($"DataGridView Error: {e.Exception?.Message} at Row: {e.RowIndex}, Column: {e.ColumnIndex}");

            // يمكن إضافة معالجة خاصة هنا إذا لزم الأمر
        }

        private void SetupReportDisplay(DataTable reportData, int selectedEmployeeIndex, string selectedEmployeeText, string reportType)
        {
            try
            {
                // تحديد نوع التقرير (موظف واحد أم جميع الموظفين)
                bool isSingleEmployee = selectedEmployeeIndex > 0;

                if (isSingleEmployee)
                {
                    // تقرير لموظف واحد - إظهار اسم الموظف في العنوان
                    string employeeName = selectedEmployeeText.Split('-')[0].Trim();
                    lblEmployeeTitle.Text = $"الموظف: {employeeName}";
                    lblEmployeeTitle.Visible = true;

                    // إزالة عمود اسم الموظف من الجدول (غير مطلوب)
                    if (reportData.Columns.Contains("اسم الموظف"))
                    {
                        reportData.Columns.Remove("اسم الموظف");
                    }
                }
                else
                {
                    // تقرير لجميع الموظفين - إخفاء عنوان الموظف
                    lblEmployeeTitle.Visible = false;

                    // التأكد من وجود عمود اسم الموظف
                    CheckAndDisplayEmployeeColumn();
                }

                // تنسيق ساعات العمل قبل العرض
                FormatWorkingHoursInReport(reportData);

                // عرض البيانات
                dataGridViewReport.DataSource = reportData;
                CalculateAndDisplaySummary(reportData);
                FormatDataGridView();

                // عرض تاريخ الإنشاء
                lblCreationDate.Text = $"تاريخ الإنشاء: {DateTime.Now:dd/MM/yyyy HH:mm}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إعداد عرض التقرير: {ex.Message}");
            }
        }

        private void DisplayProfessionalSummary(int totalDays, int presentDays, int lateDays, int absentDays,
                                               double totalWorkingHours, double totalOvertimeHours, double averageWorkingHours)
        {
            try
            {
                // الإحصائيات الأساسية في السطر الأول
                lblTotalDays.Text = $"إجمالي الأيام: {totalDays}  |  أيام الحضور: {presentDays}  |  أيام التأخير: {lateDays}  |  أيام الغياب: {absentDays}";

                // إحصائيات الساعات في السطر الثاني
                lblTotalPresent.Text = $"إجمالي ساعات العمل: {totalWorkingHours:F1}  |  الساعات الإضافية: {totalOvertimeHours:F1}  |  متوسط ساعات العمل: {averageWorkingHours:F1}";

                // إخفاء الـ labels الإضافية
                lblTotalLate.Text = "";
                lblTotalAbsent.Text = "";
                lblTotalWorkingHours.Text = "";
                lblTotalOvertimeHours.Text = "";
                lblAverageWorkingHours.Text = "";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض الملخص الاحترافي: {ex.Message}");
                // العودة للعرض التقليدي في حالة الخطأ
                lblTotalDays.Text = $"إجمالي الأيام: {totalDays}";
                lblTotalPresent.Text = $"أيام الحضور: {presentDays}";
                lblTotalLate.Text = $"أيام التأخير: {lateDays}";
                lblTotalAbsent.Text = $"أيام الغياب: {absentDays}";
            }
        }

        private string GetReportHeaderInfo()
        {
            try
            {
                string reportType = cmbReportType.SelectedItem?.ToString() ?? "تقرير شامل";
                string employeeInfo = "";

                if (cmbEmployee.SelectedIndex > 0)
                {
                    string fullEmployeeName = cmbEmployee.SelectedItem.ToString().Split('-')[0].Trim();
                    // اختصار الاسم إذا كان طويلاً
                    employeeInfo = ShortenEmployeeName(fullEmployeeName);
                }
                else
                {
                    employeeInfo = "جميع الموظفين";
                }

                string dateRange = $"من {dateTimePickerStart.Value:dd/MM/yyyy} إلى {dateTimePickerEnd.Value:dd/MM/yyyy}";

                return $"📊 {reportType}  |  👤 {employeeInfo}  |  📅 {dateRange}";
            }
            catch
            {
                return "📊 تقرير الحضور والغياب";
            }
        }

        private string ShortenEmployeeName(string fullName)
        {
            try
            {
                if (string.IsNullOrEmpty(fullName))
                    return "غير محدد";

                // إذا كان الاسم أقل من 25 حرف، اعرضه كاملاً
                if (fullName.Length <= 25)
                    return fullName;

                // تقسيم الاسم إلى كلمات
                string[] nameParts = fullName.Split(' ');

                if (nameParts.Length >= 2)
                {
                    // عرض الاسم الأول والأخير مع نقاط
                    return $"{nameParts[0]} {nameParts[1]} ...";
                }
                else
                {
                    // اختصار الاسم إلى 25 حرف مع نقاط
                    return fullName.Substring(0, Math.Min(22, fullName.Length)) + "...";
                }
            }
            catch
            {
                return fullName;
            }
        }

        private void CheckAndDisplayEmployeeColumn()
        {
            try
            {
                if (dataGridViewReport.DataSource is DataTable table && table.Rows.Count > 0)
                {
                    // التحقق من وجود عمود اسم الموظف
                    if (!table.Columns.Contains("اسم الموظف"))
                    {
                        System.Diagnostics.Debug.WriteLine("تحذير: عمود 'اسم الموظف' غير موجود في التقرير");

                        // محاولة إضافة عمود اسم الموظف إذا كان هناك كود موظف
                        if (table.Columns.Contains("كود الموظف"))
                        {
                            table.Columns.Add("اسم الموظف", typeof(string));

                            foreach (DataRow row in table.Rows)
                            {
                                if (int.TryParse(row["كود الموظف"]?.ToString(), out int employeeCode))
                                {
                                    var employee = DatabaseHelper.GetEmployeeById(employeeCode);
                                    if (employee != null)
                                    {
                                        row["اسم الموظف"] = employee.Name;
                                    }
                                }
                            }

                            // إعادة ترتيب الأعمدة لجعل اسم الموظف بعد كود الموظف
                            if (table.Columns.Contains("كود الموظف"))
                            {
                                table.Columns["اسم الموظف"].SetOrdinal(table.Columns["كود الموظف"].Ordinal + 1);
                            }
                        }
                    }

                    // تحديث عرض البيانات
                    dataGridViewReport.Refresh();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التحقق من عمود اسم الموظف: {ex.Message}");
            }
        }

        private void FormatWorkingHoursInReport(DataTable table)
        {
            try
            {
                // تنسيق عمود ساعات العمل
                if (table.Columns.Contains("ساعات العمل"))
                {
                    foreach (DataRow row in table.Rows)
                    {
                        if (row["ساعات العمل"] != null && row["ساعات العمل"] != DBNull.Value)
                        {
                            if (double.TryParse(row["ساعات العمل"].ToString(), out double workingHours))
                            {
                                row["ساعات العمل"] = FormatHoursDisplay(workingHours);
                            }
                        }
                    }
                }

                // تنسيق عمود الساعات الإضافية
                if (table.Columns.Contains("الساعات الإضافية"))
                {
                    foreach (DataRow row in table.Rows)
                    {
                        if (row["الساعات الإضافية"] != null && row["الساعات الإضافية"] != DBNull.Value)
                        {
                            if (double.TryParse(row["الساعات الإضافية"].ToString(), out double overtimeHours))
                            {
                                row["الساعات الإضافية"] = FormatHoursDisplay(overtimeHours);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنسيق ساعات العمل: {ex.Message}");
            }
        }

        private string FormatHoursDisplay(double totalHours)
        {
            if (totalHours <= 0)
                return "0";

            // تقريب إلى منزلة عشرية واحدة
            totalHours = Math.Round(totalHours, 1);

            // إذا كان رقم صحيح، اعرضه بدون منازل عشرية
            if (totalHours == Math.Floor(totalHours))
                return totalHours.ToString("F0");
            else
                return totalHours.ToString("F1");
        }


    }
}
