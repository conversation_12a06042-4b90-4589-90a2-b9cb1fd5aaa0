using System;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace EmployeeManagementSystem
{
    public partial class AttendanceReportForm : Form
    {
        private DataTable currentReportData;

        public AttendanceReportForm()
        {
            InitializeComponent();
        }

        private void AttendanceReportForm_Load(object sender, EventArgs e)
        {
            LoadEmployees();
            SetDefaultDates();
            cmbReportType.SelectedIndex = 0; // تقرير شامل
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = DatabaseHelper.GetEmployeesForAttendance();
                cmbEmployee.Items.Clear();
                cmbEmployee.Items.Add("جميع الموظفين");

                foreach (DataRow row in employees.Rows)
                {
                    string employeeInfo = $"{row["Name"]} - {row["EmployeeCode"]}";
                    cmbEmployee.Items.Add(employeeInfo);
                }

                cmbEmployee.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetDefaultDates()
        {
            dateTimePickerStart.Value = DateTime.Today.AddDays(-30);
            dateTimePickerEnd.Value = DateTime.Today;
        }

        private async void btnGenerateReport_Click(object sender, EventArgs e)
        {
            try
            {
                ShowProgress(true);
                await Task.Delay(100); // للسماح بتحديث الواجهة

                await Task.Run(() => GenerateReport());
                
                ShowProgress(false);
                MessageBox.Show("تم إنشاء التقرير بنجاح", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ShowProgress(false);
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenerateReport()
        {
            try
            {
                // الحصول على القيم من الواجهة في الخيط الرئيسي
                DateTime startDate = DateTime.Today;
                DateTime endDate = DateTime.Today;
                int selectedEmployeeIndex = 0;
                string selectedEmployeeText = "";
                string reportType = "تقرير شامل";

                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        startDate = dateTimePickerStart.Value.Date;
                        endDate = dateTimePickerEnd.Value.Date;
                        selectedEmployeeIndex = cmbEmployee.SelectedIndex;
                        selectedEmployeeText = cmbEmployee.SelectedItem?.ToString() ?? "";
                        reportType = cmbReportType.SelectedItem?.ToString() ?? "تقرير شامل";
                    }));
                }
                else
                {
                    startDate = dateTimePickerStart.Value.Date;
                    endDate = dateTimePickerEnd.Value.Date;
                    selectedEmployeeIndex = cmbEmployee.SelectedIndex;
                    selectedEmployeeText = cmbEmployee.SelectedItem?.ToString() ?? "";
                    reportType = cmbReportType.SelectedItem?.ToString() ?? "تقرير شامل";
                }

                // الحصول على بيانات التقرير
                DataTable reportData = GetReportDataSafe(startDate, endDate, selectedEmployeeIndex, selectedEmployeeText, reportType);
                currentReportData = reportData;

                // تحديث الواجهة في الخيط الرئيسي
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        dataGridViewReport.DataSource = reportData;
                        CalculateAndDisplaySummary(reportData);
                        FormatDataGridView();
                    }));
                }
                else
                {
                    dataGridViewReport.DataSource = reportData;
                    CalculateAndDisplaySummary(reportData);
                    FormatDataGridView();
                }
            }
            catch (Exception ex)
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(() =>
                    {
                        MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }));
                }
                else
                {
                    MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private DataTable GetReportData()
        {
            DataTable data;

            // تحديد نطاق التاريخ
            DateTime startDate = dateTimePickerStart.Value.Date;
            DateTime endDate = dateTimePickerEnd.Value.Date;

            // تحديد الموظف
            if (cmbEmployee.SelectedIndex > 0)
            {
                try
                {
                    string selectedText = cmbEmployee.SelectedItem.ToString();
                    string[] parts = selectedText.Split('-');
                    if (parts.Length >= 2)
                    {
                        int employeeCode = int.Parse(parts[1].Trim());
                        data = DatabaseHelper.GetAttendanceByEmployee(employeeCode);

                        // تصفية بالتاريخ
                        if (data.Rows.Count > 0)
                        {
                            var filteredRows = data.AsEnumerable()
                                .Where(row =>
                                {
                                    string dateStr = row.Field<string>("التاريخ");
                                    if (DateTime.TryParse(dateStr, out DateTime rowDate))
                                    {
                                        return rowDate >= startDate && rowDate <= endDate;
                                    }
                                    return false;
                                });

                            if (filteredRows.Any())
                            {
                                data = filteredRows.CopyToDataTable();
                            }
                            else
                            {
                                data.Clear();
                            }
                        }
                    }
                    else
                    {
                        data = DatabaseHelper.GetAttendanceByDateRange(startDate, endDate);
                    }
                }
                catch
                {
                    data = DatabaseHelper.GetAttendanceByDateRange(startDate, endDate);
                }
            }
            else
            {
                data = DatabaseHelper.GetAttendanceByDateRange(startDate, endDate);
            }

            // تطبيق فلتر نوع التقرير
            string reportType = cmbReportType.SelectedItem?.ToString() ?? "تقرير شامل";
            data = ApplyReportTypeFilter(data, reportType);

            return data;
        }

        private DataTable GetReportDataSafe(DateTime startDate, DateTime endDate, int selectedEmployeeIndex, string selectedEmployeeText, string reportType)
        {
            DataTable data;

            // تحديد الموظف
            if (selectedEmployeeIndex > 0)
            {
                try
                {
                    string[] parts = selectedEmployeeText.Split('-');
                    if (parts.Length >= 2)
                    {
                        int employeeCode = int.Parse(parts[1].Trim());
                        data = DatabaseHelper.GetAttendanceByEmployee(employeeCode);

                        // تصفية بالتاريخ
                        if (data.Rows.Count > 0)
                        {
                            var filteredRows = data.AsEnumerable()
                                .Where(row =>
                                {
                                    string dateStr = row.Field<string>("التاريخ");
                                    if (DateTime.TryParse(dateStr, out DateTime rowDate))
                                    {
                                        return rowDate >= startDate && rowDate <= endDate;
                                    }
                                    return false;
                                });

                            if (filteredRows.Any())
                            {
                                data = filteredRows.CopyToDataTable();
                            }
                            else
                            {
                                data.Clear();
                            }
                        }
                    }
                    else
                    {
                        data = DatabaseHelper.GetAttendanceByDateRange(startDate, endDate);
                    }
                }
                catch
                {
                    data = DatabaseHelper.GetAttendanceByDateRange(startDate, endDate);
                }
            }
            else
            {
                data = DatabaseHelper.GetAttendanceByDateRange(startDate, endDate);
            }

            // تطبيق فلتر نوع التقرير
            data = ApplyReportTypeFilter(data, reportType);

            return data;
        }

        private DataTable ApplyReportTypeFilter(DataTable data, string reportType)
        {
            if (reportType == "تقرير شامل" || data.Rows.Count == 0)
                return data;

            try
            {
                var filteredRows = data.AsEnumerable().Where(row =>
                {
                    try
                    {
                        string status = "";
                        if (data.Columns.Contains("الحالة"))
                        {
                            status = row.Field<string>("الحالة") ?? "";
                        }

                        return reportType switch
                        {
                            "الحضور فقط" => status == "حاضر",
                            "الغياب فقط" => status == "غائب",
                            "المتأخرين فقط" => status == "متأخر",
                            "الساعات الإضافية" => data.Columns.Contains("الساعات الإضافية") &&
                                                   Convert.ToDouble(row.Field<object>("الساعات الإضافية") ?? 0) > 0,
                            _ => true
                        };
                    }
                    catch
                    {
                        return true; // في حالة خطأ، اعرض الصف
                    }
                });

                if (filteredRows.Any())
                {
                    return filteredRows.CopyToDataTable();
                }
                else
                {
                    data.Clear();
                    return data;
                }
            }
            catch
            {
                return data; // في حالة خطأ، ارجع البيانات كما هي
            }
        }

        private void CalculateAndDisplaySummary(DataTable data)
        {
            if (data.Rows.Count == 0)
            {
                ResetSummaryLabels();
                return;
            }

            try
            {
                int totalDays = data.Rows.Count;
                int presentDays = 0;
                int lateDays = 0;
                int absentDays = 0;
                double totalWorkingHours = 0;
                double totalOvertimeHours = 0;

                // التحقق من وجود الأعمدة قبل الوصول إليها
                bool hasStatusColumn = data.Columns.Contains("الحالة");
                bool hasWorkingHoursColumn = data.Columns.Contains("ساعات العمل");
                bool hasOvertimeHoursColumn = data.Columns.Contains("الساعات الإضافية");

                foreach (DataRow row in data.Rows)
                {
                    try
                    {
                        if (hasStatusColumn)
                        {
                            string status = row.Field<string>("الحالة") ?? "";
                            switch (status)
                            {
                                case "حاضر": presentDays++; break;
                                case "متأخر": lateDays++; break;
                                case "غائب": absentDays++; break;
                            }
                        }

                        if (hasWorkingHoursColumn)
                        {
                            totalWorkingHours += Convert.ToDouble(row.Field<object>("ساعات العمل") ?? 0);
                        }

                        if (hasOvertimeHoursColumn)
                        {
                            totalOvertimeHours += Convert.ToDouble(row.Field<object>("الساعات الإضافية") ?? 0);
                        }
                    }
                    catch
                    {
                        // تجاهل الأخطاء في الصفوف الفردية
                        continue;
                    }
                }

                double averageWorkingHours = totalDays > 0 ? totalWorkingHours / totalDays : 0;

                lblTotalDays.Text = $"إجمالي الأيام: {totalDays}";
                lblTotalPresent.Text = $"أيام الحضور: {presentDays}";
                lblTotalLate.Text = $"أيام التأخير: {lateDays}";
                lblTotalAbsent.Text = $"أيام الغياب: {absentDays}";
                lblTotalWorkingHours.Text = $"إجمالي ساعات العمل: {totalWorkingHours:F1}";
                lblTotalOvertimeHours.Text = $"الساعات الإضافية: {totalOvertimeHours:F1}";
                lblAverageWorkingHours.Text = $"متوسط ساعات العمل: {averageWorkingHours:F1}";
            }
            catch (Exception ex)
            {
                // في حالة خطأ، اعرض رسالة خطأ واستخدم القيم الافتراضية
                ResetSummaryLabels();
                MessageBox.Show($"خطأ في حساب الملخص: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void ResetSummaryLabels()
        {
            lblTotalDays.Text = "إجمالي الأيام: 0";
            lblTotalPresent.Text = "أيام الحضور: 0";
            lblTotalLate.Text = "أيام التأخير: 0";
            lblTotalAbsent.Text = "أيام الغياب: 0";
            lblTotalWorkingHours.Text = "إجمالي ساعات العمل: 0";
            lblTotalOvertimeHours.Text = "الساعات الإضافية: 0.0";
            lblAverageWorkingHours.Text = "متوسط ساعات العمل: 0";
        }

        private void FormatDataGridView()
        {
            try
            {
                if (dataGridViewReport.Columns.Count > 0)
                {
                    foreach (DataGridViewColumn column in dataGridViewReport.Columns)
                    {
                        column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    }

                    // تلوين الحالات
                    if (dataGridViewReport.Columns.Contains("الحالة"))
                    {
                        foreach (DataGridViewRow row in dataGridViewReport.Rows)
                        {
                            try
                            {
                                if (row.Cells["الحالة"].Value != null)
                                {
                                    string status = row.Cells["الحالة"].Value.ToString();
                                    switch (status)
                                    {
                                        case "حاضر":
                                            row.Cells["الحالة"].Style.ForeColor = System.Drawing.Color.Green;
                                            break;
                                        case "متأخر":
                                            row.Cells["الحالة"].Style.ForeColor = System.Drawing.Color.Orange;
                                            break;
                                        case "غائب":
                                            row.Cells["الحالة"].Style.ForeColor = System.Drawing.Color.Red;
                                            break;
                                    }
                                }
                            }
                            catch
                            {
                                // تجاهل أخطاء الصفوف الفردية
                                continue;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // في حالة خطأ، لا تفعل شيئاً
                System.Diagnostics.Debug.WriteLine($"خطأ في تنسيق DataGridView: {ex.Message}");
            }
        }

        private void ShowProgress(bool show)
        {
            progressBar.Visible = show;
            lblProgress.Visible = show;
            
            if (show)
            {
                progressBar.Style = ProgressBarStyle.Marquee;
            }
        }

        private void btnExportExcel_Click(object sender, EventArgs e)
        {
            if (currentReportData == null || currentReportData.Rows.Count == 0)
            {
                MessageBox.Show("لا توجد بيانات للتصدير. الرجاء إنشاء التقرير أولاً.", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                SaveFileDialog saveDialog = new SaveFileDialog
                {
                    Filter = "CSV Files (*.csv)|*.csv",
                    DefaultExt = "csv",
                    FileName = $"تقرير_الحضور_{DateTime.Now:yyyy-MM-dd}.csv"
                };

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportToCSV(currentReportData, saveDialog.FileName);
                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToCSV(DataTable data, string filePath)
        {
            StringBuilder csv = new StringBuilder();
            
            // إضافة رؤوس الأعمدة
            string[] columnNames = data.Columns.Cast<DataColumn>()
                .Select(column => column.ColumnName).ToArray();
            csv.AppendLine(string.Join(",", columnNames));

            // إضافة البيانات
            foreach (DataRow row in data.Rows)
            {
                string[] fields = row.ItemArray.Select(field => 
                    $"\"{field?.ToString().Replace("\"", "\"\"")}\"").ToArray();
                csv.AppendLine(string.Join(",", fields));
            }

            File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
        }

        private void btnPrintReport_Click(object sender, EventArgs e)
        {
            if (currentReportData == null || currentReportData.Rows.Count == 0)
            {
                MessageBox.Show("لا توجد بيانات للطباعة. الرجاء إنشاء التقرير أولاً.", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                string htmlReport = GenerateHTMLReport();
                string tempFile = Path.Combine(Path.GetTempPath(), $"attendance_report_{DateTime.Now:yyyyMMdd_HHmmss}.html");
                File.WriteAllText(tempFile, htmlReport, Encoding.UTF8);
                
                System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                {
                    FileName = tempFile,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GenerateHTMLReport()
        {
            StringBuilder html = new StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير الحضور والغياب</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; direction: rtl; }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .present { color: green; font-weight: bold; }
        .late { color: orange; font-weight: bold; }
        .absent { color: red; font-weight: bold; }
        @media print { body { margin: 0; } }
    </style>
</head>
<body>
    <div class=""header"">
        <h1>تقرير الحضور والغياب</h1>
        <p>من " + dateTimePickerStart.Value.ToString("dd/MM/yyyy") + @" إلى " + dateTimePickerEnd.Value.ToString("dd/MM/yyyy") + @"</p>
        <p>تاريخ الإنشاء: " + DateTime.Now.ToString("dd/MM/yyyy HH:mm") + @"</p>
    </div>");

            // إضافة الملخص
            html.Append(@"<div class=""summary"">
        <h3>ملخص التقرير</h3>
        <p>" + lblTotalDays.Text + @" | " + lblTotalPresent.Text + @" | " + lblTotalLate.Text + @" | " + lblTotalAbsent.Text + @"</p>
        <p>" + lblTotalWorkingHours.Text + @" | " + lblTotalOvertimeHours.Text + @" | " + lblAverageWorkingHours.Text + @"</p>
    </div>");

            // إضافة الجدول
            html.Append("<table><thead><tr>");
            foreach (DataColumn column in currentReportData.Columns)
            {
                html.AppendFormat("<th>{0}</th>", column.ColumnName);
            }
            html.Append("</tr></thead><tbody>");

            foreach (DataRow row in currentReportData.Rows)
            {
                html.Append("<tr>");
                foreach (var item in row.ItemArray)
                {
                    string cellValue = item?.ToString() ?? "";
                    string cssClass = "";
                    
                    if (cellValue == "حاضر") cssClass = "present";
                    else if (cellValue == "متأخر") cssClass = "late";
                    else if (cellValue == "غائب") cssClass = "absent";
                    
                    html.AppendFormat("<td class=\"{0}\">{1}</td>", cssClass, cellValue);
                }
                html.Append("</tr>");
            }

            html.Append("</tbody></table></body></html>");
            return html.ToString();
        }

        private void dataGridViewReport_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            // منع ظهور رسالة الخطأ الافتراضية
            e.Cancel = true;

            // تسجيل الخطأ للتشخيص
            System.Diagnostics.Debug.WriteLine($"DataGridView Error: {e.Exception?.Message} at Row: {e.RowIndex}, Column: {e.ColumnIndex}");

            // يمكن إضافة معالجة خاصة هنا إذا لزم الأمر
        }
    }
}
