﻿namespace EmployeeManagementSystem
{
    partial class StartForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            progressBar1 = new ProgressBar();
            pictureBox1 = new PictureBox();
            labelState = new Label();
            labelCopyright = new Label();
            timerStart = new System.Windows.Forms.Timer(components);
            ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();
            SuspendLayout();
            // 
            // progressBar1
            // 
            progressBar1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            progressBar1.Location = new Point(15, 360);
            progressBar1.Name = "progressBar1";
            progressBar1.Size = new Size(486, 39);
            progressBar1.Style = ProgressBarStyle.Marquee;
            progressBar1.TabIndex = 0;
            // 
            // pictureBox1
            // 
            pictureBox1.Image = Properties.Resources.Welcome11;
            pictureBox1.Location = new Point(15, 29);
            pictureBox1.Name = "pictureBox1";
            pictureBox1.Size = new Size(486, 265);
            pictureBox1.SizeMode = PictureBoxSizeMode.StretchImage;
            pictureBox1.TabIndex = 1;
            pictureBox1.TabStop = false;
            // 
            // labelState
            // 
            labelState.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            labelState.Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            labelState.Location = new Point(15, 303);
            labelState.Name = "labelState";
            labelState.RightToLeft = RightToLeft.Yes;
            labelState.Size = new Size(486, 54);
            labelState.TabIndex = 2;
            labelState.Text = "جاري الدخول ...";
            labelState.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // labelCopyright
            // 
            labelCopyright.Anchor = AnchorStyles.Bottom;
            labelCopyright.AutoSize = true;
            labelCopyright.Font = new Font("Cairo", 8.999999F, FontStyle.Bold);
            labelCopyright.Location = new Point(17, 404);
            labelCopyright.Name = "labelCopyright";
            labelCopyright.RightToLeft = RightToLeft.Yes;
            labelCopyright.Size = new Size(60, 23);
            labelCopyright.TabIndex = 7;
            labelCopyright.Text = "Copyright";
            labelCopyright.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // timerStart
            // 
            timerStart.Enabled = true;
            timerStart.Interval = 5000;
            timerStart.Tick += TimerStart_Tick;
            // 
            // StartForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(517, 436);
            ControlBox = false;
            Controls.Add(labelCopyright);
            Controls.Add(labelState);
            Controls.Add(pictureBox1);
            Controls.Add(progressBar1);
            FormBorderStyle = FormBorderStyle.None;
            MaximizeBox = false;
            Name = "StartForm";
            StartPosition = FormStartPosition.CenterScreen;
            ((System.ComponentModel.ISupportInitialize)pictureBox1).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private ProgressBar progressBar1;
        private PictureBox pictureBox1;
        private Label labelState;
        private Label labelCopyright;
        private System.Windows.Forms.Timer timerStart;
    }
}