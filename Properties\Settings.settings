﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="EmployeeManagementSystem.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="IsActivated" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="ActivationKey" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="CompanyName" Type="System.String" Scope="User">
      <Value Profile="(Default)">اسم المؤسسة</Value>
    </Setting>
    <Setting Name="CompanyDes" Type="System.String" Scope="User">
      <Value Profile="(Default)">وصف المؤسسة</Value>
    </Setting>
    <Setting Name="ToastDuration" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">2000</Value>
    </Setting>
  </Settings>
</SettingsFile>