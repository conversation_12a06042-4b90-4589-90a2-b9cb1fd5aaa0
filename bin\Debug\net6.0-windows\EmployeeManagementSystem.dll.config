﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="EmployeeManagementSystem.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <userSettings>
        <EmployeeManagementSystem.Properties.Settings>
            <setting name="IsActivated" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="ActivationKey" serializeAs="String">
                <value />
            </setting>
            <setting name="CompanyName" serializeAs="String">
                <value>اسم المؤسسة</value>
            </setting>
            <setting name="CompanyDes" serializeAs="String">
                <value>وصف المؤسسة</value>
            </setting>
            <setting name="ToastDuration" serializeAs="String">
                <value>2000</value>
            </setting>
        </EmployeeManagementSystem.Properties.Settings>
    </userSettings>
</configuration>