using System;
using System.Text;
using System.Collections.Generic;
using System.Linq;

namespace EmployeeManagementSystem
{
    public class WorkPeriod
    {
        public int WorkPeriodId { get; set; }
        public int EmployeeCode { get; set; }
        public string? EmployeeName { get; set; }
        public string? ProjectName { get; set; }
        public string? Description { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string? WorkingDays { get; set; } // أيام العمل (مثل: "الأحد,الاثنين,الثلاثاء")
        public double DailyWorkingHours { get; set; } // ساعات العمل اليومية
        public string? Status { get; set; } // "نشط", "مكتمل", "ملغي"
        public DateTime CreatedDate { get; set; }

        public WorkPeriod()
        {
            StartDate = DateTime.Today;
            EndDate = DateTime.Today.AddDays(30);
            DailyWorkingHours = 8.0;
            Status = "نشط";
            CreatedDate = DateTime.Now;
            WorkingDays = "الأحد,الاثنين,الثلاثاء,الأربعاء,الخميس"; // أيام العمل الافتراضية
        }

        // حساب إجمالي أيام العمل في الفترة
        public int GetTotalWorkingDays()
        {
            if (string.IsNullOrEmpty(WorkingDays))
                return 0;

            var workingDaysList = WorkingDays.Split(',').Select(d => d.Trim()).ToList();
            var dayMapping = new Dictionary<string, DayOfWeek>
            {
                {"الأحد", DayOfWeek.Sunday},
                {"الاثنين", DayOfWeek.Monday},
                {"الثلاثاء", DayOfWeek.Tuesday},
                {"الأربعاء", DayOfWeek.Wednesday},
                {"الخميس", DayOfWeek.Thursday},
                {"الجمعة", DayOfWeek.Friday},
                {"السبت", DayOfWeek.Saturday}
            };

            var workingDaysOfWeek = workingDaysList
                .Where(day => dayMapping.ContainsKey(day))
                .Select(day => dayMapping[day])
                .ToList();

            int totalDays = 0;
            for (var date = StartDate; date <= EndDate; date = date.AddDays(1))
            {
                if (workingDaysOfWeek.Contains(date.DayOfWeek))
                {
                    totalDays++;
                }
            }

            return totalDays;
        }

        // حساب إجمالي ساعات العمل المتوقعة
        public double GetTotalExpectedHours()
        {
            return GetTotalWorkingDays() * DailyWorkingHours;
        }

        // التحقق من أن التاريخ المحدد يقع ضمن فترة العمل
        public bool IsDateInPeriod(DateTime date)
        {
            return date.Date >= StartDate.Date && date.Date <= EndDate.Date;
        }

        // التحقق من أن اليوم المحدد هو يوم عمل
        public bool IsWorkingDay(DateTime date)
        {
            if (!IsDateInPeriod(date) || string.IsNullOrEmpty(WorkingDays))
                return false;

            var workingDaysList = WorkingDays.Split(',').Select(d => d.Trim()).ToList();
            var dayMapping = new Dictionary<DayOfWeek, string>
            {
                {DayOfWeek.Sunday, "الأحد"},
                {DayOfWeek.Monday, "الاثنين"},
                {DayOfWeek.Tuesday, "الثلاثاء"},
                {DayOfWeek.Wednesday, "الأربعاء"},
                {DayOfWeek.Thursday, "الخميس"},
                {DayOfWeek.Friday, "الجمعة"},
                {DayOfWeek.Saturday, "السبت"}
            };

            string dayName = dayMapping[date.DayOfWeek];
            return workingDaysList.Contains(dayName);
        }

        // تحديث حالة فترة العمل بناءً على التاريخ الحالي
        public void UpdateStatus()
        {
            var today = DateTime.Today;
            
            if (today < StartDate)
            {
                Status = "لم تبدأ";
            }
            else if (today > EndDate)
            {
                Status = "مكتملة";
            }
            else
            {
                Status = "نشطة";
            }
        }

        // تحويل إلى تقرير HTML
        public string GetHtmlReport()
        {
            StringBuilder html = new StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير فترة العمل</title>
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            padding: 20px;
            direction: rtl;
            background-color: #f5f5f5;
        }
        .report-header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .report-header h1 {
            margin: 0;
            font-size: 28px;
        }
        .period-details {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .label {
            font-weight: bold;
            color: #333;
            flex: 1;
        }
        .value {
            flex: 2;
            text-align: left;
            color: #666;
        }
        .status {
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
        }
        .status.active { background-color: #28a745; }
        .status.completed { background-color: #6c757d; }
        .status.cancelled { background-color: #dc3545; }
        .status.pending { background-color: #ffc107; color: #333; }
        @media print {
            body {
                background-color: white;
            }
            .report-header {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class=""report-header"">
        <h1>تقرير فترة عمل</h1>
        <p>التاريخ: " + DateTime.Now.ToString("dd/MM/yyyy") + @"</p>
    </div>
    
    <div class=""period-details"">");

            AddDetailRow(html, "اسم الموظف", EmployeeName ?? "");
            AddDetailRow(html, "اسم المشروع", ProjectName ?? "");
            AddDetailRow(html, "الوصف", Description ?? "");
            AddDetailRow(html, "تاريخ البداية", StartDate.ToString("dd/MM/yyyy"));
            AddDetailRow(html, "تاريخ النهاية", EndDate.ToString("dd/MM/yyyy"));
            AddDetailRow(html, "أيام العمل", WorkingDays ?? "");
            AddDetailRow(html, "ساعات العمل اليومية", DailyWorkingHours.ToString("F1") + " ساعة");
            AddDetailRow(html, "إجمالي أيام العمل", GetTotalWorkingDays().ToString() + " يوم");
            AddDetailRow(html, "إجمالي الساعات المتوقعة", GetTotalExpectedHours().ToString("F1") + " ساعة");
            
            // إضافة الحالة مع التنسيق المناسب
            string statusClass = Status?.ToLower() switch
            {
                "نشطة" or "نشط" => "active",
                "مكتملة" or "مكتمل" => "completed",
                "ملغية" or "ملغي" => "cancelled",
                "لم تبدأ" => "pending",
                _ => "pending"
            };
            
            html.AppendFormat(@"
        <div class=""detail-row"">
            <div class=""label"">الحالة:</div>
            <div class=""value""><span class=""status {0}"">{1}</span></div>
        </div>", statusClass, Status ?? "غير محدد");

            html.Append(@"</div>
</body>
</html>");

            return html.ToString();
        }

        private void AddDetailRow(StringBuilder html, string label, string value)
        {
            html.AppendFormat(@"
        <div class=""detail-row"">
            <div class=""label"">{0}:</div>
            <div class=""value"">{1}</div>
        </div>", label, value);
        }
    }
}
