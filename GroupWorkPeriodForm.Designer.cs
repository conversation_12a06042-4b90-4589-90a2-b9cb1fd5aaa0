namespace EmployeeManagementSystem
{
    partial class GroupWorkPeriodForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            groupBoxEmployees = new GroupBox();
            lblVacationCount = new Label();
            lblWorkCount = new Label();
            cmbWorkStatus = new ComboBox();
            cmbVacationStatus = new ComboBox();
            lblWorkStatus = new Label();
            lblVacationStatus = new Label();
            btnRemoveFromVacation = new Button();
            btnAddToVacation = new Button();
            btnRemoveFromWork = new Button();
            btnAddToWork = new Button();
            listBoxVacationEmployees = new ListBox();
            listBoxWorkEmployees = new ListBox();
            listBoxAllEmployees = new ListBox();
            lblVacationEmployees = new Label();
            lblWorkEmployees = new Label();
            lblAllEmployees = new Label();
            groupBoxWorkPeriod = new GroupBox();
            numericUpDownDailyHours = new NumericUpDown();
            lblDailyHours = new Label();
            cmbStatus = new ComboBox();
            lblStatus = new Label();
            groupBoxWorkingDays = new GroupBox();
            chkSaturday = new CheckBox();
            chkFriday = new CheckBox();
            chkThursday = new CheckBox();
            chkWednesday = new CheckBox();
            chkTuesday = new CheckBox();
            chkMonday = new CheckBox();
            chkSunday = new CheckBox();
            dateTimePickerEnd = new DateTimePicker();
            dateTimePickerStart = new DateTimePicker();
            lblEndDate = new Label();
            lblStartDate = new Label();
            txtDescription = new TextBox();
            lblDescription = new Label();
            txtWorkPlace = new TextBox();
            lblWorkPlace = new Label();
            groupBoxActions = new GroupBox();
            btnClear = new Button();
            btnCreatePeriods = new Button();
            groupBoxSavedGroups = new GroupBox();
            dataGridViewGroups = new DataGridView();
            btnDeleteGroup = new Button();
            btnLoadGroup = new Button();
            btnSaveGroup = new Button();
            txtGroupName = new TextBox();
            lblGroupName = new Label();
            groupBoxEmployees.SuspendLayout();
            groupBoxWorkPeriod.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDownDailyHours).BeginInit();
            groupBoxWorkingDays.SuspendLayout();
            groupBoxActions.SuspendLayout();
            groupBoxSavedGroups.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewGroups).BeginInit();
            SuspendLayout();
            // 
            // groupBoxEmployees
            // 
            groupBoxEmployees.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxEmployees.Controls.Add(lblVacationCount);
            groupBoxEmployees.Controls.Add(lblWorkCount);
            groupBoxEmployees.Controls.Add(cmbWorkStatus);
            groupBoxEmployees.Controls.Add(cmbVacationStatus);
            groupBoxEmployees.Controls.Add(lblWorkStatus);
            groupBoxEmployees.Controls.Add(lblVacationStatus);
            groupBoxEmployees.Controls.Add(btnRemoveFromVacation);
            groupBoxEmployees.Controls.Add(btnAddToVacation);
            groupBoxEmployees.Controls.Add(btnRemoveFromWork);
            groupBoxEmployees.Controls.Add(btnAddToWork);
            groupBoxEmployees.Controls.Add(listBoxVacationEmployees);
            groupBoxEmployees.Controls.Add(listBoxWorkEmployees);
            groupBoxEmployees.Controls.Add(listBoxAllEmployees);
            groupBoxEmployees.Controls.Add(lblVacationEmployees);
            groupBoxEmployees.Controls.Add(lblWorkEmployees);
            groupBoxEmployees.Controls.Add(lblAllEmployees);
            groupBoxEmployees.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            groupBoxEmployees.Location = new Point(12, 12);
            groupBoxEmployees.Name = "groupBoxEmployees";
            groupBoxEmployees.RightToLeft = RightToLeft.Yes;
            groupBoxEmployees.Size = new Size(1160, 310);
            groupBoxEmployees.TabIndex = 0;
            groupBoxEmployees.TabStop = false;
            groupBoxEmployees.Text = "تقسيم الموظفين";
            // 
            // lblVacationCount
            // 
            lblVacationCount.AutoSize = true;
            lblVacationCount.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            lblVacationCount.ForeColor = Color.Red;
            lblVacationCount.Location = new Point(20, 238);
            lblVacationCount.Name = "lblVacationCount";
            lblVacationCount.Size = new Size(106, 15);
            lblVacationCount.TabIndex = 11;
            lblVacationCount.Text = "موظفين في إجازة: 0";
            // 
            // lblWorkCount
            // 
            lblWorkCount.AutoSize = true;
            lblWorkCount.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            lblWorkCount.ForeColor = Color.Green;
            lblWorkCount.Location = new Point(408, 238);
            lblWorkCount.Name = "lblWorkCount";
            lblWorkCount.Size = new Size(112, 15);
            lblWorkCount.TabIndex = 10;
            lblWorkCount.Text = "موظفين في العمل: 0";
            // 
            // cmbWorkStatus
            // 
            cmbWorkStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbWorkStatus.Font = new Font("Segoe UI", 9F);
            cmbWorkStatus.FormattingEnabled = true;
            cmbWorkStatus.Items.AddRange(new object[] { "نشط", "مكتمل", "ملغي", "لم يبدأ", "حضور", "غياب", "إجازة", "هروب" });
            cmbWorkStatus.Location = new Point(400, 278);
            cmbWorkStatus.Name = "cmbWorkStatus";
            cmbWorkStatus.Size = new Size(120, 23);
            cmbWorkStatus.TabIndex = 12;
            // 
            // cmbVacationStatus
            // 
            cmbVacationStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbVacationStatus.Font = new Font("Segoe UI", 9F);
            cmbVacationStatus.FormattingEnabled = true;
            cmbVacationStatus.Items.AddRange(new object[] { "نشط", "مكتمل", "ملغي", "لم يبدأ", "حضور", "غياب", "إجازة", "هروب" });
            cmbVacationStatus.Location = new Point(24, 278);
            cmbVacationStatus.Name = "cmbVacationStatus";
            cmbVacationStatus.Size = new Size(120, 23);
            cmbVacationStatus.TabIndex = 14;
            // 
            // lblWorkStatus
            // 
            lblWorkStatus.AutoSize = true;
            lblWorkStatus.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            lblWorkStatus.Location = new Point(527, 281);
            lblWorkStatus.Name = "lblWorkStatus";
            lblWorkStatus.Size = new Size(62, 15);
            lblWorkStatus.TabIndex = 13;
            lblWorkStatus.Text = "حالة العمل:";
            // 
            // lblVacationStatus
            // 
            lblVacationStatus.AutoSize = true;
            lblVacationStatus.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            lblVacationStatus.Location = new Point(150, 281);
            lblVacationStatus.Name = "lblVacationStatus";
            lblVacationStatus.Size = new Size(63, 15);
            lblVacationStatus.TabIndex = 15;
            lblVacationStatus.Text = "حالة الإجازة:";
            // 
            // btnRemoveFromVacation
            // 
            btnRemoveFromVacation.BackColor = Color.FromArgb(220, 53, 69);
            btnRemoveFromVacation.FlatStyle = FlatStyle.Flat;
            btnRemoveFromVacation.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnRemoveFromVacation.ForeColor = Color.White;
            btnRemoveFromVacation.Location = new Point(160, 230);
            btnRemoveFromVacation.Name = "btnRemoveFromVacation";
            btnRemoveFromVacation.Size = new Size(100, 30);
            btnRemoveFromVacation.TabIndex = 9;
            btnRemoveFromVacation.Text = "إزالة من الإجازة";
            btnRemoveFromVacation.UseVisualStyleBackColor = false;
            btnRemoveFromVacation.Click += btnRemoveFromVacation_Click;
            // 
            // btnAddToVacation
            // 
            btnAddToVacation.BackColor = Color.FromArgb(255, 193, 7);
            btnAddToVacation.FlatStyle = FlatStyle.Flat;
            btnAddToVacation.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnAddToVacation.ForeColor = Color.Black;
            btnAddToVacation.Location = new Point(280, 230);
            btnAddToVacation.Name = "btnAddToVacation";
            btnAddToVacation.Size = new Size(100, 30);
            btnAddToVacation.TabIndex = 8;
            btnAddToVacation.Text = "إضافة للإجازة";
            btnAddToVacation.UseVisualStyleBackColor = false;
            btnAddToVacation.Click += btnAddToVacation_Click;
            // 
            // btnRemoveFromWork
            // 
            btnRemoveFromWork.BackColor = Color.FromArgb(220, 53, 69);
            btnRemoveFromWork.FlatStyle = FlatStyle.Flat;
            btnRemoveFromWork.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnRemoveFromWork.ForeColor = Color.White;
            btnRemoveFromWork.Location = new Point(540, 230);
            btnRemoveFromWork.Name = "btnRemoveFromWork";
            btnRemoveFromWork.Size = new Size(100, 30);
            btnRemoveFromWork.TabIndex = 7;
            btnRemoveFromWork.Text = "إزالة من العمل";
            btnRemoveFromWork.UseVisualStyleBackColor = false;
            btnRemoveFromWork.Click += btnRemoveFromWork_Click;
            // 
            // btnAddToWork
            // 
            btnAddToWork.BackColor = Color.FromArgb(40, 167, 69);
            btnAddToWork.FlatStyle = FlatStyle.Flat;
            btnAddToWork.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnAddToWork.ForeColor = Color.White;
            btnAddToWork.Location = new Point(660, 230);
            btnAddToWork.Name = "btnAddToWork";
            btnAddToWork.Size = new Size(100, 30);
            btnAddToWork.TabIndex = 6;
            btnAddToWork.Text = "إضافة للعمل";
            btnAddToWork.UseVisualStyleBackColor = false;
            btnAddToWork.Click += btnAddToWork_Click;
            // 
            // listBoxVacationEmployees
            // 
            listBoxVacationEmployees.Font = new Font("Segoe UI", 10F);
            listBoxVacationEmployees.FormattingEnabled = true;
            listBoxVacationEmployees.ItemHeight = 17;
            listBoxVacationEmployees.Location = new Point(20, 50);
            listBoxVacationEmployees.Name = "listBoxVacationEmployees";
            listBoxVacationEmployees.SelectionMode = SelectionMode.MultiExtended;
            listBoxVacationEmployees.Size = new Size(360, 174);
            listBoxVacationEmployees.TabIndex = 5;
            // 
            // listBoxWorkEmployees
            // 
            listBoxWorkEmployees.Font = new Font("Segoe UI", 10F);
            listBoxWorkEmployees.FormattingEnabled = true;
            listBoxWorkEmployees.ItemHeight = 17;
            listBoxWorkEmployees.Location = new Point(400, 50);
            listBoxWorkEmployees.Name = "listBoxWorkEmployees";
            listBoxWorkEmployees.SelectionMode = SelectionMode.MultiExtended;
            listBoxWorkEmployees.Size = new Size(360, 174);
            listBoxWorkEmployees.TabIndex = 4;
            // 
            // listBoxAllEmployees
            // 
            listBoxAllEmployees.Font = new Font("Segoe UI", 10F);
            listBoxAllEmployees.FormattingEnabled = true;
            listBoxAllEmployees.ItemHeight = 17;
            listBoxAllEmployees.Location = new Point(780, 50);
            listBoxAllEmployees.Name = "listBoxAllEmployees";
            listBoxAllEmployees.SelectionMode = SelectionMode.MultiExtended;
            listBoxAllEmployees.Size = new Size(360, 174);
            listBoxAllEmployees.TabIndex = 3;
            // 
            // lblVacationEmployees
            // 
            lblVacationEmployees.AutoSize = true;
            lblVacationEmployees.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblVacationEmployees.ForeColor = Color.Red;
            lblVacationEmployees.Location = new Point(280, 30);
            lblVacationEmployees.Name = "lblVacationEmployees";
            lblVacationEmployees.Size = new Size(112, 19);
            lblVacationEmployees.TabIndex = 2;
            lblVacationEmployees.Text = "موظفين في إجازة";
            // 
            // lblWorkEmployees
            // 
            lblWorkEmployees.AutoSize = true;
            lblWorkEmployees.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblWorkEmployees.ForeColor = Color.Green;
            lblWorkEmployees.Location = new Point(660, 30);
            lblWorkEmployees.Name = "lblWorkEmployees";
            lblWorkEmployees.Size = new Size(118, 19);
            lblWorkEmployees.TabIndex = 1;
            lblWorkEmployees.Text = "موظفين في العمل";
            // 
            // lblAllEmployees
            // 
            lblAllEmployees.AutoSize = true;
            lblAllEmployees.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblAllEmployees.Location = new Point(1040, 30);
            lblAllEmployees.Name = "lblAllEmployees";
            lblAllEmployees.Size = new Size(99, 19);
            lblAllEmployees.TabIndex = 0;
            lblAllEmployees.Text = "جميع الموظفين";
            // 
            // groupBoxWorkPeriod
            // 
            groupBoxWorkPeriod.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxWorkPeriod.Controls.Add(numericUpDownDailyHours);
            groupBoxWorkPeriod.Controls.Add(lblDailyHours);
            groupBoxWorkPeriod.Controls.Add(cmbStatus);
            groupBoxWorkPeriod.Controls.Add(lblStatus);
            groupBoxWorkPeriod.Controls.Add(groupBoxWorkingDays);
            groupBoxWorkPeriod.Controls.Add(dateTimePickerEnd);
            groupBoxWorkPeriod.Controls.Add(dateTimePickerStart);
            groupBoxWorkPeriod.Controls.Add(lblEndDate);
            groupBoxWorkPeriod.Controls.Add(lblStartDate);
            groupBoxWorkPeriod.Controls.Add(txtDescription);
            groupBoxWorkPeriod.Controls.Add(lblDescription);
            groupBoxWorkPeriod.Controls.Add(txtWorkPlace);
            groupBoxWorkPeriod.Controls.Add(lblWorkPlace);
            groupBoxWorkPeriod.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            groupBoxWorkPeriod.Location = new Point(12, 320);
            groupBoxWorkPeriod.Name = "groupBoxWorkPeriod";
            groupBoxWorkPeriod.RightToLeft = RightToLeft.Yes;
            groupBoxWorkPeriod.Size = new Size(1160, 200);
            groupBoxWorkPeriod.TabIndex = 1;
            groupBoxWorkPeriod.TabStop = false;
            groupBoxWorkPeriod.Text = "تفاصيل فترة العمل";
            // 
            // numericUpDownDailyHours
            // 
            numericUpDownDailyHours.DecimalPlaces = 1;
            numericUpDownDailyHours.Font = new Font("Segoe UI", 10F);
            numericUpDownDailyHours.Location = new Point(580, 160);
            numericUpDownDailyHours.Maximum = new decimal(new int[] { 24, 0, 0, 0 });
            numericUpDownDailyHours.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numericUpDownDailyHours.Name = "numericUpDownDailyHours";
            numericUpDownDailyHours.Size = new Size(120, 25);
            numericUpDownDailyHours.TabIndex = 12;
            numericUpDownDailyHours.Value = new decimal(new int[] { 8, 0, 0, 0 });
            // 
            // lblDailyHours
            // 
            lblDailyHours.AutoSize = true;
            lblDailyHours.Font = new Font("Segoe UI", 10F);
            lblDailyHours.Location = new Point(720, 162);
            lblDailyHours.Name = "lblDailyHours";
            lblDailyHours.Size = new Size(136, 19);
            lblDailyHours.TabIndex = 11;
            lblDailyHours.Text = "ساعات العمل اليومية:";
            // 
            // cmbStatus
            // 
            cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbStatus.Font = new Font("Segoe UI", 10F);
            cmbStatus.FormattingEnabled = true;
            cmbStatus.Items.AddRange(new object[] { "نشط", "مكتمل", "ملغي", "لم يبدأ", "حضور", "غياب", "إجازة" });
            cmbStatus.Location = new Point(880, 160);
            cmbStatus.Name = "cmbStatus";
            cmbStatus.Size = new Size(150, 25);
            cmbStatus.TabIndex = 10;
            // 
            // lblStatus
            // 
            lblStatus.AutoSize = true;
            lblStatus.Font = new Font("Segoe UI", 10F);
            lblStatus.Location = new Point(1050, 163);
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(45, 19);
            lblStatus.TabIndex = 9;
            lblStatus.Text = "الحالة:";
            // 
            // groupBoxWorkingDays
            // 
            groupBoxWorkingDays.Controls.Add(chkSaturday);
            groupBoxWorkingDays.Controls.Add(chkFriday);
            groupBoxWorkingDays.Controls.Add(chkThursday);
            groupBoxWorkingDays.Controls.Add(chkWednesday);
            groupBoxWorkingDays.Controls.Add(chkTuesday);
            groupBoxWorkingDays.Controls.Add(chkMonday);
            groupBoxWorkingDays.Controls.Add(chkSunday);
            groupBoxWorkingDays.Font = new Font("Segoe UI", 10F);
            groupBoxWorkingDays.Location = new Point(20, 120);
            groupBoxWorkingDays.Name = "groupBoxWorkingDays";
            groupBoxWorkingDays.RightToLeft = RightToLeft.Yes;
            groupBoxWorkingDays.Size = new Size(500, 70);
            groupBoxWorkingDays.TabIndex = 8;
            groupBoxWorkingDays.TabStop = false;
            groupBoxWorkingDays.Text = "أيام العمل";
            // 
            // chkSaturday
            // 
            chkSaturday.AutoSize = true;
            chkSaturday.Font = new Font("Segoe UI", 9F);
            chkSaturday.Location = new Point(20, 25);
            chkSaturday.Name = "chkSaturday";
            chkSaturday.RightToLeft = RightToLeft.Yes;
            chkSaturday.Size = new Size(58, 19);
            chkSaturday.TabIndex = 6;
            chkSaturday.Text = "السبت";
            chkSaturday.UseVisualStyleBackColor = true;
            // 
            // chkFriday
            // 
            chkFriday.AutoSize = true;
            chkFriday.Font = new Font("Segoe UI", 9F);
            chkFriday.Location = new Point(80, 25);
            chkFriday.Name = "chkFriday";
            chkFriday.RightToLeft = RightToLeft.Yes;
            chkFriday.Size = new Size(60, 19);
            chkFriday.TabIndex = 5;
            chkFriday.Text = "الجمعة";
            chkFriday.UseVisualStyleBackColor = true;
            // 
            // chkThursday
            // 
            chkThursday.AutoSize = true;
            chkThursday.Checked = true;
            chkThursday.CheckState = CheckState.Checked;
            chkThursday.Font = new Font("Segoe UI", 9F);
            chkThursday.Location = new Point(150, 25);
            chkThursday.Name = "chkThursday";
            chkThursday.RightToLeft = RightToLeft.Yes;
            chkThursday.Size = new Size(65, 19);
            chkThursday.TabIndex = 4;
            chkThursday.Text = "الخميس";
            chkThursday.UseVisualStyleBackColor = true;
            // 
            // chkWednesday
            // 
            chkWednesday.AutoSize = true;
            chkWednesday.Checked = true;
            chkWednesday.CheckState = CheckState.Checked;
            chkWednesday.Font = new Font("Segoe UI", 9F);
            chkWednesday.Location = new Point(220, 25);
            chkWednesday.Name = "chkWednesday";
            chkWednesday.RightToLeft = RightToLeft.Yes;
            chkWednesday.Size = new Size(60, 19);
            chkWednesday.TabIndex = 3;
            chkWednesday.Text = "الأربعاء";
            chkWednesday.UseVisualStyleBackColor = true;
            // 
            // chkTuesday
            // 
            chkTuesday.AutoSize = true;
            chkTuesday.Checked = true;
            chkTuesday.CheckState = CheckState.Checked;
            chkTuesday.Font = new Font("Segoe UI", 9F);
            chkTuesday.Location = new Point(290, 25);
            chkTuesday.Name = "chkTuesday";
            chkTuesday.RightToLeft = RightToLeft.Yes;
            chkTuesday.Size = new Size(56, 19);
            chkTuesday.TabIndex = 2;
            chkTuesday.Text = "الثلاثاء";
            chkTuesday.UseVisualStyleBackColor = true;
            // 
            // chkMonday
            // 
            chkMonday.AutoSize = true;
            chkMonday.Checked = true;
            chkMonday.CheckState = CheckState.Checked;
            chkMonday.Font = new Font("Segoe UI", 9F);
            chkMonday.Location = new Point(360, 25);
            chkMonday.Name = "chkMonday";
            chkMonday.RightToLeft = RightToLeft.Yes;
            chkMonday.Size = new Size(56, 19);
            chkMonday.TabIndex = 1;
            chkMonday.Text = "الاثنين";
            chkMonday.UseVisualStyleBackColor = true;
            // 
            // chkSunday
            // 
            chkSunday.AutoSize = true;
            chkSunday.Checked = true;
            chkSunday.CheckState = CheckState.Checked;
            chkSunday.Font = new Font("Segoe UI", 9F);
            chkSunday.Location = new Point(430, 25);
            chkSunday.Name = "chkSunday";
            chkSunday.RightToLeft = RightToLeft.Yes;
            chkSunday.Size = new Size(49, 19);
            chkSunday.TabIndex = 0;
            chkSunday.Text = "الأحد";
            chkSunday.UseVisualStyleBackColor = true;
            // 
            // dateTimePickerEnd
            // 
            dateTimePickerEnd.Font = new Font("Segoe UI", 10F);
            dateTimePickerEnd.Format = DateTimePickerFormat.Short;
            dateTimePickerEnd.Location = new Point(580, 80);
            dateTimePickerEnd.Name = "dateTimePickerEnd";
            dateTimePickerEnd.Size = new Size(200, 25);
            dateTimePickerEnd.TabIndex = 7;
            // 
            // dateTimePickerStart
            // 
            dateTimePickerStart.Font = new Font("Segoe UI", 10F);
            dateTimePickerStart.Format = DateTimePickerFormat.Short;
            dateTimePickerStart.Location = new Point(880, 80);
            dateTimePickerStart.Name = "dateTimePickerStart";
            dateTimePickerStart.Size = new Size(200, 25);
            dateTimePickerStart.TabIndex = 6;
            // 
            // lblEndDate
            // 
            lblEndDate.AutoSize = true;
            lblEndDate.Font = new Font("Segoe UI", 10F);
            lblEndDate.Location = new Point(800, 83);
            lblEndDate.Name = "lblEndDate";
            lblEndDate.Size = new Size(82, 19);
            lblEndDate.TabIndex = 5;
            lblEndDate.Text = "تاريخ النهاية:";
            // 
            // lblStartDate
            // 
            lblStartDate.AutoSize = true;
            lblStartDate.Font = new Font("Segoe UI", 10F);
            lblStartDate.Location = new Point(1100, 83);
            lblStartDate.Name = "lblStartDate";
            lblStartDate.Size = new Size(82, 19);
            lblStartDate.TabIndex = 4;
            lblStartDate.Text = "تاريخ البداية:";
            // 
            // txtDescription
            // 
            txtDescription.Font = new Font("Segoe UI", 10F);
            txtDescription.Location = new Point(20, 40);
            txtDescription.Multiline = true;
            txtDescription.Name = "txtDescription";
            txtDescription.Size = new Size(450, 25);
            txtDescription.TabIndex = 3;
            // 
            // lblDescription
            // 
            lblDescription.AutoSize = true;
            lblDescription.Font = new Font("Segoe UI", 10F);
            lblDescription.Location = new Point(480, 43);
            lblDescription.Name = "lblDescription";
            lblDescription.Size = new Size(56, 19);
            lblDescription.TabIndex = 2;
            lblDescription.Text = "الوصف:";
            // 
            // txtWorkPlace
            // 
            txtWorkPlace.Font = new Font("Segoe UI", 10F);
            txtWorkPlace.Location = new Point(580, 40);
            txtWorkPlace.Name = "txtWorkPlace";
            txtWorkPlace.Size = new Size(450, 25);
            txtWorkPlace.TabIndex = 1;
            // 
            // lblWorkPlace
            // 
            lblWorkPlace.AutoSize = true;
            lblWorkPlace.Font = new Font("Segoe UI", 10F);
            lblWorkPlace.Location = new Point(1050, 43);
            lblWorkPlace.Name = "lblWorkPlace";
            lblWorkPlace.Size = new Size(78, 19);
            lblWorkPlace.TabIndex = 0;
            lblWorkPlace.Text = "مكان العمل:";
            // 
            // groupBoxActions
            // 
            groupBoxActions.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxActions.Controls.Add(btnClear);
            groupBoxActions.Controls.Add(btnCreatePeriods);
            groupBoxActions.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            groupBoxActions.Location = new Point(12, 530);
            groupBoxActions.Name = "groupBoxActions";
            groupBoxActions.RightToLeft = RightToLeft.Yes;
            groupBoxActions.Size = new Size(1160, 80);
            groupBoxActions.TabIndex = 2;
            groupBoxActions.TabStop = false;
            groupBoxActions.Text = "العمليات";
            // 
            // btnClear
            // 
            btnClear.BackColor = Color.FromArgb(108, 117, 125);
            btnClear.FlatStyle = FlatStyle.Flat;
            btnClear.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnClear.ForeColor = Color.White;
            btnClear.Location = new Point(400, 30);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(150, 35);
            btnClear.TabIndex = 1;
            btnClear.Text = "مسح الحقول";
            btnClear.UseVisualStyleBackColor = false;
            btnClear.Click += btnClear_Click;
            // 
            // btnCreatePeriods
            // 
            btnCreatePeriods.BackColor = Color.FromArgb(40, 167, 69);
            btnCreatePeriods.FlatStyle = FlatStyle.Flat;
            btnCreatePeriods.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnCreatePeriods.ForeColor = Color.White;
            btnCreatePeriods.Location = new Point(580, 30);
            btnCreatePeriods.Name = "btnCreatePeriods";
            btnCreatePeriods.Size = new Size(150, 35);
            btnCreatePeriods.TabIndex = 0;
            btnCreatePeriods.Text = "إنشاء فترات العمل";
            btnCreatePeriods.UseVisualStyleBackColor = false;
            btnCreatePeriods.Click += btnCreatePeriods_Click;
            // 
            // groupBoxSavedGroups
            // 
            groupBoxSavedGroups.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxSavedGroups.Controls.Add(dataGridViewGroups);
            groupBoxSavedGroups.Controls.Add(btnDeleteGroup);
            groupBoxSavedGroups.Controls.Add(btnLoadGroup);
            groupBoxSavedGroups.Controls.Add(btnSaveGroup);
            groupBoxSavedGroups.Controls.Add(txtGroupName);
            groupBoxSavedGroups.Controls.Add(lblGroupName);
            groupBoxSavedGroups.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            groupBoxSavedGroups.Location = new Point(12, 620);
            groupBoxSavedGroups.Name = "groupBoxSavedGroups";
            groupBoxSavedGroups.RightToLeft = RightToLeft.Yes;
            groupBoxSavedGroups.Size = new Size(1160, 250);
            groupBoxSavedGroups.TabIndex = 3;
            groupBoxSavedGroups.TabStop = false;
            groupBoxSavedGroups.Text = "المجموعات المحفوظة";
            // 
            // dataGridViewGroups
            // 
            dataGridViewGroups.AllowUserToAddRows = false;
            dataGridViewGroups.AllowUserToDeleteRows = false;
            dataGridViewGroups.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewGroups.BackgroundColor = Color.White;
            dataGridViewGroups.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewGroups.Location = new Point(20, 70);
            dataGridViewGroups.MultiSelect = false;
            dataGridViewGroups.Name = "dataGridViewGroups";
            dataGridViewGroups.ReadOnly = true;
            dataGridViewGroups.RightToLeft = RightToLeft.Yes;
            dataGridViewGroups.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewGroups.Size = new Size(1120, 170);
            dataGridViewGroups.TabIndex = 5;
            dataGridViewGroups.SelectionChanged += dataGridViewGroups_SelectionChanged;
            // 
            // btnDeleteGroup
            // 
            btnDeleteGroup.BackColor = Color.FromArgb(220, 53, 69);
            btnDeleteGroup.FlatStyle = FlatStyle.Flat;
            btnDeleteGroup.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnDeleteGroup.ForeColor = Color.White;
            btnDeleteGroup.Location = new Point(260, 30);
            btnDeleteGroup.Name = "btnDeleteGroup";
            btnDeleteGroup.Size = new Size(120, 30);
            btnDeleteGroup.TabIndex = 4;
            btnDeleteGroup.Text = "حذف المجموعة";
            btnDeleteGroup.UseVisualStyleBackColor = false;
            btnDeleteGroup.Click += btnDeleteGroup_Click;
            // 
            // btnLoadGroup
            // 
            btnLoadGroup.BackColor = Color.FromArgb(0, 123, 255);
            btnLoadGroup.FlatStyle = FlatStyle.Flat;
            btnLoadGroup.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnLoadGroup.ForeColor = Color.White;
            btnLoadGroup.Location = new Point(400, 30);
            btnLoadGroup.Name = "btnLoadGroup";
            btnLoadGroup.Size = new Size(120, 30);
            btnLoadGroup.TabIndex = 3;
            btnLoadGroup.Text = "تحميل المجموعة";
            btnLoadGroup.UseVisualStyleBackColor = false;
            btnLoadGroup.Click += btnLoadGroup_Click;
            // 
            // btnSaveGroup
            // 
            btnSaveGroup.BackColor = Color.FromArgb(40, 167, 69);
            btnSaveGroup.FlatStyle = FlatStyle.Flat;
            btnSaveGroup.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            btnSaveGroup.ForeColor = Color.White;
            btnSaveGroup.Location = new Point(540, 30);
            btnSaveGroup.Name = "btnSaveGroup";
            btnSaveGroup.Size = new Size(120, 30);
            btnSaveGroup.TabIndex = 2;
            btnSaveGroup.Text = "حفظ المجموعة";
            btnSaveGroup.UseVisualStyleBackColor = false;
            btnSaveGroup.Click += btnSaveGroup_Click;
            // 
            // txtGroupName
            // 
            txtGroupName.Font = new Font("Segoe UI", 10F);
            txtGroupName.Location = new Point(680, 32);
            txtGroupName.Name = "txtGroupName";
            txtGroupName.Size = new Size(300, 25);
            txtGroupName.TabIndex = 1;
            // 
            // lblGroupName
            // 
            lblGroupName.AutoSize = true;
            lblGroupName.Font = new Font("Segoe UI", 10F);
            lblGroupName.Location = new Point(1000, 35);
            lblGroupName.Name = "lblGroupName";
            lblGroupName.Size = new Size(95, 19);
            lblGroupName.TabIndex = 0;
            lblGroupName.Text = "اسم المجموعة:";
            // 
            // GroupWorkPeriodForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.White;
            ClientSize = new Size(1184, 880);
            Controls.Add(groupBoxSavedGroups);
            Controls.Add(groupBoxActions);
            Controls.Add(groupBoxWorkPeriod);
            Controls.Add(groupBoxEmployees);
            Name = "GroupWorkPeriodForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            Text = "إدارة مجموعات فترات العمل";
            Load += GroupWorkPeriodForm_Load;
            groupBoxEmployees.ResumeLayout(false);
            groupBoxEmployees.PerformLayout();
            groupBoxWorkPeriod.ResumeLayout(false);
            groupBoxWorkPeriod.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDownDailyHours).EndInit();
            groupBoxWorkingDays.ResumeLayout(false);
            groupBoxWorkingDays.PerformLayout();
            groupBoxActions.ResumeLayout(false);
            groupBoxSavedGroups.ResumeLayout(false);
            groupBoxSavedGroups.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewGroups).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private System.Windows.Forms.GroupBox groupBoxEmployees;
        private System.Windows.Forms.ListBox listBoxAllEmployees;
        private System.Windows.Forms.Label lblAllEmployees;
        private System.Windows.Forms.ListBox listBoxVacationEmployees;
        private System.Windows.Forms.ListBox listBoxWorkEmployees;
        private System.Windows.Forms.Label lblVacationEmployees;
        private System.Windows.Forms.Label lblWorkEmployees;
        private System.Windows.Forms.Button btnAddToWork;
        private System.Windows.Forms.Button btnRemoveFromWork;
        private System.Windows.Forms.Button btnAddToVacation;
        private System.Windows.Forms.Button btnRemoveFromVacation;
        private System.Windows.Forms.Label lblWorkCount;
        private System.Windows.Forms.Label lblVacationCount;
        private System.Windows.Forms.ComboBox cmbWorkStatus;
        private System.Windows.Forms.ComboBox cmbVacationStatus;
        private System.Windows.Forms.Label lblWorkStatus;
        private System.Windows.Forms.Label lblVacationStatus;
        private System.Windows.Forms.GroupBox groupBoxWorkPeriod;
        private System.Windows.Forms.TextBox txtWorkPlace;
        private System.Windows.Forms.Label lblWorkPlace;
        private System.Windows.Forms.TextBox txtDescription;
        private System.Windows.Forms.Label lblDescription;
        private System.Windows.Forms.DateTimePicker dateTimePickerStart;
        private System.Windows.Forms.Label lblStartDate;
        private System.Windows.Forms.DateTimePicker dateTimePickerEnd;
        private System.Windows.Forms.Label lblEndDate;
        private System.Windows.Forms.GroupBox groupBoxWorkingDays;
        private System.Windows.Forms.CheckBox chkSaturday;
        private System.Windows.Forms.CheckBox chkFriday;
        private System.Windows.Forms.CheckBox chkThursday;
        private System.Windows.Forms.CheckBox chkWednesday;
        private System.Windows.Forms.CheckBox chkTuesday;
        private System.Windows.Forms.CheckBox chkMonday;
        private System.Windows.Forms.CheckBox chkSunday;
        private System.Windows.Forms.ComboBox cmbStatus;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.NumericUpDown numericUpDownDailyHours;
        private System.Windows.Forms.Label lblDailyHours;
        private System.Windows.Forms.GroupBox groupBoxActions;
        private System.Windows.Forms.Button btnCreatePeriods;
        private System.Windows.Forms.Button btnClear;
        private System.Windows.Forms.GroupBox groupBoxSavedGroups;
        private System.Windows.Forms.DataGridView dataGridViewGroups;
        private System.Windows.Forms.Button btnSaveGroup;
        private System.Windows.Forms.Button btnLoadGroup;
        private System.Windows.Forms.Button btnDeleteGroup;
        private System.Windows.Forms.TextBox txtGroupName;
        private System.Windows.Forms.Label lblGroupName;
    }
}
