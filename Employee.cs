using System;
using System.Data;
using System.IO;
using System.Text;

namespace EmployeeManagementSystem
{
    public class Employee
    {
        public int EmployeeCode { get; set; }
        public string? Name { get; set; }
        public string? MotherName { get; set; }
        public DateTime DateOfBirth { get; set; }
        public string? Province { get; set; }
        public string? Nationality { get; set; }
        public string? IdentityNumber { get; set; }
        public DateTime StartDate { get; set; }
        public string? AdministrativeOrder { get; set; }
        public string? StatisticalNumber { get; set; }
        public string? KeyCardNumber { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Category { get; set; }
        public string? BadgeNumber { get; set; }
        public DateTime BadgeExpiryDate { get; set; }
        public string? PhotoPath { get; set; }
        public string? MaritalStatus { get; set; }      
        public string? WifeName { get; set; }           
        public string? ElectoralNumber { get; set; }    
        public string? EducationLevel { get; set; }     

        public string GetHtmlReport()
        {
            StringBuilder html = new StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير الموظف</title>
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            padding: 20px;
            direction: rtl;
            position: relative;
        }
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 10px;
            background-color: #45678a;
            color: white;
            position: relative;
        }
        .logged-user {
            position: absolute;
            top: 10px;
            left: 20px;
            font-size: 14px;
            color: white;
        }
        .content-wrapper {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            margin-top: 20px;
        }
        .employee-details {
            flex: 2;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .photo-section {
            flex: 1;
            text-align: center;
        }
        .photo-section img {
            max-width: 200px;
            max-height: 250px;
            border: 2px solid #45678a;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .detail-row {
            display: flex;
            margin-bottom: 10px;
            align-items: center;
        }
        .label {
            font-weight: bold;
            min-width: 150px;
            color: #45678a;
        }
        .value {
            flex: 1;
            padding: 5px 10px;
            background-color: white;
            border-radius: 3px;
            border: 1px solid #ddd;
        }
        @media print {
            body {
                margin: 0;
                padding: 10px;
            }
            .report-header {
                margin-bottom: 20px;
            }
            .content-wrapper {
                break-inside: avoid;
            }
            .employee-details, .photo-section {
                break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class=""report-header"">
        <div class=""logged-user"">المستخدم: " + System.Security.Principal.WindowsIdentity.GetCurrent().Name + @"</div>
        <h1>معلومات الموظف</h1>
    </div>
    <div class=""content-wrapper"">");

            // إضافة البيانات الشخصية على اليمين
            html.Append(@"<div class=""employee-details"">");
            AddDetailRow(html, "كود الموظف", EmployeeCode.ToString());
            AddDetailRow(html, "الاسم", Name ?? "");
            AddDetailRow(html, "اسم الأم", MotherName ?? "");
            AddDetailRow(html, "الصنف", Category ?? "");
            AddDetailRow(html, "المحافظة", Province ?? "");
            AddDetailRow(html, "الجنسية", Nationality ?? "");
            AddDetailRow(html, "رقم الهوية", IdentityNumber ?? "");
            AddDetailRow(html, "رقم الأمر الإداري", AdministrativeOrder ?? "");
            AddDetailRow(html, "الرقم الإحصائي", StatisticalNumber ?? "");
            AddDetailRow(html, "رقم الكي كارد", KeyCardNumber ?? "");
            AddDetailRow(html, "رقم الهاتف", PhoneNumber ?? "");
            AddDetailRow(html, "رقم الباج", BadgeNumber ?? "");
            AddDetailRow(html, "تاريخ الميلاد", DateOfBirth.ToString("dd/MM/yyyy"));
            AddDetailRow(html, "تاريخ المباشرة", StartDate.ToString("dd/MM/yyyy"));
            AddDetailRow(html, "تاريخ انتهاء الباج", BadgeExpiryDate.ToString("dd/MM/yyyy"));
            html.Append("</div>");

            // إضافة الصورة على اليسار
            html.Append(@"<div class=""photo-section"">");
            if (!string.IsNullOrEmpty(PhotoPath) && File.Exists(PhotoPath))
            {
                try
                {
                    string base64Image = Convert.ToBase64String(File.ReadAllBytes(PhotoPath));
                    string imageFormat = Path.GetExtension(PhotoPath).TrimStart('.').ToLower();
                    html.AppendFormat(@"<img src=""data:image/{0};base64,{1}"" alt=""صورة الموظف"">",
                        imageFormat, base64Image);
                }
                catch
                {
                    html.Append("<p>لا يمكن عرض الصورة</p>");
                }
            }
            else
            {
                html.Append("<p>لا توجد صورة</p>");
            }
            html.Append("</div>");

            html.Append(@"</div>
</body>
</html>");

            return html.ToString();
        }

        private void AddDetailRow(StringBuilder html, string label, string value)
        {
            html.AppendFormat(@"
        <div class=""detail-row"">
            <div class=""label"">{0}:</div>
            <div class=""value"">{1}</div>
        </div>", label, value);
        }
    }
}