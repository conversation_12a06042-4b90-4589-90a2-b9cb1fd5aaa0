using System.Data.SQLite;

namespace EmployeeManagementSystem 
{
    public static class UpdateVacations
    {
        public static void UpdateVacationEmployeeIds()
        {
            try
            {
                using (var connection = new SQLiteConnection(DatabaseHelper.ConnectionString))
                {
                    connection.Open();
                    string sql = @"
                        UPDATE Vacations 
                        SET EmployeeId = (
                            SELECT EmployeeCode 
                            FROM Employees 
                            WHERE Employees.Name = Vacations.EmployeeName
                        ) 
                        WHERE EmployeeId IS NULL;";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        int rowsAffected = command.ExecuteNonQuery();
                        DatabaseHelper.LogMessage($"تم تحديث {rowsAffected} إجازة بنجاح");
                    }
                }
            }
            catch (Exception ex)
            {
                DatabaseHelper.LogMessage($"خطأ في تحديث الإجازات: {ex.Message}");
                throw;
            }
        }
    }
}
