{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|c:\\users\\<USER>\\source\\repos\\hrmsdb\\workperiodform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|solutionrelative:workperiodform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|C:\\Users\\<USER>\\source\\repos\\HRMSDB\\attendanceform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|solutionrelative:attendanceform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|C:\\Users\\<USER>\\source\\repos\\HRMSDB\\attendancereportform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|solutionrelative:attendancereportform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|C:\\Users\\<USER>\\source\\repos\\HRMSDB\\groupworkperiodform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|solutionrelative:groupworkperiodform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 3, "Title": "GroupWorkPeriodForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\GroupWorkPeriodForm.cs", "RelativeDocumentMoniker": "GroupWorkPeriodForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\GroupWorkPeriodForm.cs [Design]", "RelativeToolTip": "GroupWorkPeriodForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T08:46:05.692Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 0, "Title": "WorkPeriodForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\WorkPeriodForm.cs", "RelativeDocumentMoniker": "WorkPeriodForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\WorkPeriodForm.cs [Design]", "RelativeToolTip": "WorkPeriodForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T08:37:04.188Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 2, "Title": "AttendanceReportForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\AttendanceReportForm.cs", "RelativeDocumentMoniker": "AttendanceReportForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\AttendanceReportForm.cs [Design]", "RelativeToolTip": "AttendanceReportForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T08:24:47.001Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 1, "Title": "AttendanceForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\AttendanceForm.cs", "RelativeDocumentMoniker": "AttendanceForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\AttendanceForm.cs [Design]", "RelativeToolTip": "AttendanceForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T07:44:54.031Z", "EditorCaption": " [Design]"}]}]}]}