{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|c:\\users\\<USER>\\source\\repos\\hrmsdb\\attendanceform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|solutionrelative:attendanceform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|c:\\users\\<USER>\\source\\repos\\hrmsdb\\mainform.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|solutionrelative:mainform.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|c:\\users\\<USER>\\source\\repos\\hrmsdb\\groupworkperiodform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|solutionrelative:groupworkperiodform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|c:\\users\\<USER>\\source\\repos\\hrmsdb\\workperiodform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{7B2099DD-198E-5C04-7A7A-C64E7BEA8B4D}|EmployeeManagementSystem.csproj|solutionrelative:workperiodform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "AttendanceForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\AttendanceForm.cs", "RelativeDocumentMoniker": "AttendanceForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\AttendanceForm.cs [Design]*", "RelativeToolTip": "AttendanceForm.cs [Design]*", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T18:42:19.579Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 3, "Title": "WorkPeriodForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\WorkPeriodForm.cs", "RelativeDocumentMoniker": "WorkPeriodForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\WorkPeriodForm.cs [Design]", "RelativeToolTip": "WorkPeriodForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T18:35:13.527Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 2, "Title": "GroupWorkPeriodForm.cs [Design]", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\GroupWorkPeriodForm.cs", "RelativeDocumentMoniker": "GroupWorkPeriodForm.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\GroupWorkPeriodForm.cs [Design]", "RelativeToolTip": "GroupWorkPeriodForm.cs [Design]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T18:32:21.271Z", "EditorCaption": " [Design]"}, {"$type": "Document", "DocumentIndex": 1, "Title": "MainForm.Designer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\MainForm.Designer.cs", "RelativeDocumentMoniker": "MainForm.Designer.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\HRMSDB\\MainForm.Designer.cs", "RelativeToolTip": "MainForm.Designer.cs", "ViewState": "AgIAALkAAAAAAAAAAADgv8cAAABKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-18T17:03:53.175Z", "EditorCaption": ""}]}]}]}