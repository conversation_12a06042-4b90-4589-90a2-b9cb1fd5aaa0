using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public class SimpleEmployeeStatusForm : Form
    {
        private int workPeriodId;
        private int employeeCode;
        private string employeeName;
        private DateTime startDate;
        private DateTime endDate;
        
        private Label lblTitle;
        private DataGridView dataGridViewDays;
        private Button btnSave;
        private Button btnClose;

        public SimpleEmployeeStatusForm(int workPeriodId, int employeeCode, string employeeName, DateTime startDate, DateTime endDate)
        {
            this.workPeriodId = workPeriodId;
            this.employeeCode = employeeCode;
            this.employeeName = employeeName;
            this.startDate = startDate;
            this.endDate = endDate;
            
            SetupForm();
            LoadDailyStatus();
        }

        private void SetupForm()
        {
            // إعداد النموذج
            this.Size = new Size(700, 600);
            this.Text = $"تعديل الحالة اليومية - {employeeName}";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Font = new Font("Cairo", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // العنوان
            lblTitle = new Label();
            lblTitle.Text = $"تعديل الحالة اليومية للموظف: {employeeName}";
            lblTitle.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblTitle.ForeColor = Color.DarkBlue;
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            lblTitle.Location = new Point(10, 10);
            lblTitle.Size = new Size(660, 40);

            // جدول الأيام
            dataGridViewDays = new DataGridView();
            dataGridViewDays.Location = new Point(10, 60);
            dataGridViewDays.Size = new Size(660, 450);
            dataGridViewDays.AllowUserToAddRows = false;
            dataGridViewDays.AllowUserToDeleteRows = false;
            dataGridViewDays.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewDays.MultiSelect = false;
            dataGridViewDays.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewDays.RowHeadersVisible = false;
            dataGridViewDays.BackgroundColor = Color.White;

            // زر الحفظ
            btnSave = new Button();
            btnSave.Text = "حفظ التغييرات";
            btnSave.Location = new Point(10, 520);
            btnSave.Size = new Size(120, 35);
            btnSave.BackColor = Color.Green;
            btnSave.ForeColor = Color.White;
            btnSave.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnSave.Click += BtnSave_Click;

            // زر الإغلاق
            btnClose = new Button();
            btnClose.Text = "إغلاق";
            btnClose.Location = new Point(140, 520);
            btnClose.Size = new Size(100, 35);
            btnClose.BackColor = Color.Gray;
            btnClose.ForeColor = Color.White;
            btnClose.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnClose.Click += (s, e) => this.Close();

            // إضافة العناصر للنموذج
            this.Controls.Add(lblTitle);
            this.Controls.Add(dataGridViewDays);
            this.Controls.Add(btnSave);
            this.Controls.Add(btnClose);
        }

        private void LoadDailyStatus()
        {
            try
            {
                // إنشاء جدول للعرض
                var displayTable = new DataTable();
                displayTable.Columns.Add("التاريخ", typeof(string));
                displayTable.Columns.Add("اليوم", typeof(string));
                displayTable.Columns.Add("الحالة", typeof(string));
                displayTable.Columns.Add("الملاحظات", typeof(string));

                // إضافة الأيام
                for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    var statusData = GetDailyStatus(date);

                    var newRow = displayTable.NewRow();
                    newRow["التاريخ"] = date.ToString("dd/MM/yyyy");
                    newRow["اليوم"] = GetDayName(date.DayOfWeek);
                    newRow["الحالة"] = statusData.Status;
                    newRow["الملاحظات"] = statusData.Notes;

                    displayTable.Rows.Add(newRow);
                }

                dataGridViewDays.DataSource = displayTable;

                // التأكد من وجود الأعمدة قبل التعديل
                if (dataGridViewDays.Columns.Count >= 4)
                {
                    // إضافة عمود ComboBox للحالة
                    var statusColumn = new DataGridViewComboBoxColumn();
                    statusColumn.Name = "الحالة_جديد";
                    statusColumn.HeaderText = "الحالة";
                    statusColumn.DataPropertyName = "الحالة";
                    statusColumn.Items.AddRange(new[] { "حضور", "غياب", "إجازة" });
                    statusColumn.Width = 100;

                    // استبدال العمود العادي بـ ComboBox
                    dataGridViewDays.Columns.RemoveAt(2);
                    dataGridViewDays.Columns.Insert(2, statusColumn);

                    // تنسيق الأعمدة
                    if (dataGridViewDays.Columns["التاريخ"] != null)
                        dataGridViewDays.Columns["التاريخ"].Width = 100;

                    if (dataGridViewDays.Columns["اليوم"] != null)
                        dataGridViewDays.Columns["اليوم"].Width = 80;

                    if (dataGridViewDays.Columns["الملاحظات"] != null)
                        dataGridViewDays.Columns["الملاحظات"].Width = 200;

                    // تنسيق عام للأعمدة
                    foreach (DataGridViewColumn column in dataGridViewDays.Columns)
                    {
                        if (column != null)
                        {
                            column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                            column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                            column.HeaderCell.Style.Font = new Font("Cairo", 9F, FontStyle.Bold);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private (string Status, string Notes) GetDailyStatus(DateTime date)
        {
            try
            {
                using (var connection = new System.Data.SQLite.SQLiteConnection(DatabaseHelper.ConnectionString))
                {
                    connection.Open();
                    string sql = @"SELECT Status, Notes FROM DailyWorkStatus 
                                 WHERE WorkPeriodId = @WorkPeriodId 
                                 AND EmployeeCode = @EmployeeCode 
                                 AND Date = @Date";
                    
                    using (var command = new System.Data.SQLite.SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                        command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                        command.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return (reader.GetString("Status"), reader.IsDBNull("Notes") ? "" : reader.GetString("Notes"));
                            }
                        }
                    }
                }
                return ("حضور", ""); // افتراضي
            }
            catch
            {
                return ("حضور", ""); // افتراضي في حالة الخطأ
            }
        }

        private string GetDayName(DayOfWeek dayOfWeek)
        {
            switch (dayOfWeek)
            {
                case DayOfWeek.Sunday: return "الأحد";
                case DayOfWeek.Monday: return "الاثنين";
                case DayOfWeek.Tuesday: return "الثلاثاء";
                case DayOfWeek.Wednesday: return "الأربعاء";
                case DayOfWeek.Thursday: return "الخميس";
                case DayOfWeek.Friday: return "الجمعة";
                case DayOfWeek.Saturday: return "السبت";
                default: return "";
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                int updatedCount = 0;

                foreach (DataGridViewRow row in dataGridViewDays.Rows)
                {
                    if (row.IsNewRow) continue;

                    // التحقق من وجود الخلايا قبل الوصول إليها
                    if (row.Cells["التاريخ"]?.Value == null) continue;

                    string dateStr = row.Cells["التاريخ"].Value.ToString();
                    DateTime date = DateTime.ParseExact(dateStr, "dd/MM/yyyy", null);

                    // البحث عن عمود الحالة (قد يكون الاسم مختلف)
                    string status = "حضور";
                    if (row.Cells["الحالة_جديد"]?.Value != null)
                        status = row.Cells["الحالة_جديد"].Value.ToString();
                    else if (row.Cells["الحالة"]?.Value != null)
                        status = row.Cells["الحالة"].Value.ToString();

                    string notes = row.Cells["الملاحظات"]?.Value?.ToString() ?? "";

                    // تحديث قاعدة البيانات
                    DatabaseHelper.UpdateDailyWorkStatus(workPeriodId, employeeCode, date, status, notes);
                    updatedCount++;
                }

                MessageBox.Show($"تم تحديث {updatedCount} يوم بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
