using System;
using System.Windows.Forms;
using System.Drawing;
using System.IO;
using System.Data.SQLite;
using EmployeeManagementSystem.Properties;
using System.Diagnostics;

namespace EmployeeManagementSystem
{
    public partial class SettingsForm : Form
    {
        private string logoPath = "";
        private readonly string dbPath;

        public SettingsForm()
        {
            InitializeComponent();
            dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "EmployeeDB.sqlite");
            LoadSettings();
            //cmbTheme.SelectedIndex = 0; // افتراضي كقيمة افتراضية

            ThemeManager.ApplyThemeToForm(this);
        }

        private void LoadSettings()
        {
            try
            {
                txtOrganizationName.Text = Settings.Default.CompanyName;
                txtDescription.Text = Settings.Default.CompanyDes;
                var settings = DatabaseHelper.GetSettings();
                if (settings.Rows.Count > 0)
                {
                    var row = settings.Rows[0];
                    txtOrganizationName.Text = row["CompanyName"]?.ToString() ?? "";
                    if (row["CompanyLogo"] != DBNull.Value)
                    {
                        logoPath = row["CompanyLogo"]?.ToString() ?? "";
                        if (!string.IsNullOrEmpty(logoPath) && File.Exists(logoPath))
                        {
                            using (var stream = new FileStream(logoPath, FileMode.Open, FileAccess.Read))
                            {
                                pictureBoxLogo.Image = Image.FromStream(stream);
                            }
                        }
                    }

                    string? theme = row["Theme"]?.ToString();
                    if (!string.IsNullOrEmpty(theme))
                    {
                        switch (theme.ToLower())
                        {
                            case "light":
                                cmbTheme.SelectedIndex = 1;
                                break;
                            case "dark":
                                cmbTheme.SelectedIndex = 2;
                                break;
                            default:
                                cmbTheme.SelectedIndex = 0;
                                break;
                        }
                    }
                    else
                    {
                        cmbTheme.SelectedIndex = 0;

                    }
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء تحميل الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SaveSettings()
        {
            try
            {
                Settings.Default.CompanyName = txtOrganizationName.Text;
                Settings.Default.CompanyDes = txtDescription.Text;
                Settings.Default.Save();
                string theme = "default";
                switch (cmbTheme.SelectedIndex)
                {
                    case 1:
                        theme = "light";
                        break;
                    case 2:
                        theme = "dark";
                        break;
                }

                DatabaseHelper.UpdateSettings(
                    txtOrganizationName.Text,
                    logoPath,
                    theme,
                    "ar" // اللغة العربية كقيمة افتراضية
                );
                ToastHelper.ShowSettingsToast();
                MessageBox.Show("تم حفظ الإعدادات بنجاح", "نجاح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                ApplyTheme(theme);
                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء حفظ الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ApplyTheme(string theme)
        {
            // تطبيق الثيم بشكل عام
            ThemeManager.ApplyTheme(theme);

            // تطبيق الثيم على النموذج الحالي
            ThemeManager.ApplyThemeToForm(this);

            // تطبيق الثيم على النموذج الرئيسي إذا كان موجوداً
            if (Application.OpenForms["MainForm"] is Form mainForm)
            {
                ThemeManager.ApplyThemeToForm(mainForm);
            }

            // تحديث جميع النماذج المفتوحة
            foreach (Form form in Application.OpenForms)
            {
                if (form != this && form != Application.OpenForms["MainForm"])
                {
                    ThemeManager.ApplyThemeToForm(form);
                }
            }
        }

        private void btnSelectLogo_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "صور|*.jpg;*.jpeg;*.png;*.bmp";
                openFileDialog.Title = "اختر شعار المؤسسة";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string selectedFile = openFileDialog.FileName;
                    string destinationPath = Path.Combine(Application.StartupPath, "images",
                        $"logo_{Path.GetExtension(selectedFile)}");

                    Directory.CreateDirectory(Path.Combine(Application.StartupPath, "images"));
                    File.Copy(selectedFile, destinationPath, true);

                    logoPath = destinationPath;
                    using (var stream = new FileStream(logoPath, FileMode.Open, FileAccess.Read))
                    {
                        pictureBoxLogo.Image = Image.FromStream(stream);
                    }
                }
            }
        }

        private void btnBackup_Click(object sender, EventArgs e)
        {
            try
            {
                using (SaveFileDialog saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.Filter = "SQLite Database|*.sqlite";
                    saveFileDialog.Title = "حفظ النسخة الاحتياطية";
                    saveFileDialog.FileName = $"EmployeeDB_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.sqlite";
                    saveFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        // إغلاق جميع الاتصالات مع قاعدة البيانات
                        GC.Collect();
                        GC.WaitForPendingFinalizers();

                        try
                        {
                            File.Copy(dbPath, saveFileDialog.FileName, true);
                            MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نجاح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        catch (IOException)
                        {
                            // محاولة إغلاق جميع الاتصالات وإعادة المحاولة
                            System.Threading.Thread.Sleep(1000);
                            File.Copy(dbPath, saveFileDialog.FileName, true);
                            MessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح", "نجاح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnRestore_Click(object sender, EventArgs e)
        {
            try
            {
                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "SQLite Database|*.sqlite";
                    openFileDialog.Title = "استعادة النسخة الاحتياطية";
                    openFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        if (MessageBox.Show("سيتم استبدال قاعدة البيانات الحالية. هل أنت متأكد؟", "تأكيد",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                        {
                            // إغلاق جميع النوافذ المفتوحة
                            foreach (Form form in Application.OpenForms.Cast<Form>().ToList())
                            {
                                if (form != this)
                                {
                                    form.Close();
                                }
                            }

                            // تنظيف الذاكرة وإغلاق الاتصالات
                            GC.Collect();
                            GC.WaitForPendingFinalizers();

                            try
                            {

                                File.Copy(openFileDialog.FileName, DatabaseHelper.DbPath, true);

                                MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تشغيل البرنامج.", "نجاح",
                                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                                // إعادة تشغيل البرنامج
                                string appPath = Application.ExecutablePath;
                                System.Diagnostics.ProcessStartInfo startInfo = new System.Diagnostics.ProcessStartInfo();
                                startInfo.FileName = appPath;
                                startInfo.UseShellExecute = true;

                                System.Diagnostics.Process.Start(startInfo);
                                Application.Exit();
                            }
                            catch (IOException)
                            {
                                // محاولة إضافية في حالة وجود مشكلة في الوصول للملف
                                System.Threading.Thread.Sleep(1000);
                                File.Copy(openFileDialog.FileName, dbPath, true);

                                MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح. سيتم إعادة تشغيل البرنامج.", "نجاح",
                                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                                // إعادة تشغيل البرنامج
                                string appPath = Application.ExecutablePath;
                                System.Diagnostics.ProcessStartInfo startInfo = new System.Diagnostics.ProcessStartInfo();
                                startInfo.FileName = appPath;
                                startInfo.UseShellExecute = true;

                                System.Diagnostics.Process.Start(startInfo);
                                Application.Exit();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء استعادة النسخة الاحتياطية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtOrganizationName.Text))
            {
                MessageBox.Show("الرجاء إدخال اسم المؤسسة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            SaveSettings();
           
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void btnClear_Click(object sender, EventArgs e)
        {

            txtOrganizationName.Text = "اسم المؤسسة";
            txtDescription.Text = "وصف المؤسسة";
            pictureBoxLogo.Image = null;
            logoPath = "";
            cmbTheme.SelectedIndex = 0;
        }

        private void btn_update_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من الاتصال بالإنترنت
                if (!System.Net.NetworkInformation.NetworkInterface.GetIsNetworkAvailable())
                {
                    MessageBox.Show("لا يوجد اتصال بالإنترنت. يرجى التحقق من اتصال الشبكة والمحاولة مرة أخرى.",
                                    "خطأ في الاتصال", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // تحديد المسار الكامل لبرنامج التحديث
                string updaterPath = Path.Combine(Application.StartupPath, "HRMSDBUpdater.exe");

                // التأكد من وجود ملف التحديث
                if (File.Exists(updaterPath))
                {
                    // نافذة التحميل أثناء البحث عن التحديثات
                    using (Form loadingForm = new Form
                    {
                        Text = "جاري... التحقق من التحديثات",
                        Size = new System.Drawing.Size(400, 100),
                        StartPosition = FormStartPosition.CenterScreen,
                        FormBorderStyle = FormBorderStyle.FixedDialog,
                        MaximizeBox = false,
                        MinimizeBox = false,
                        TopMost = true
                    })
                    {
                        Label lblMessage = new Label
                        {
                            Text = "جاري... البحث عن تحديثات، يرجى الانتظار",
                            AutoSize = false,
                            Dock = DockStyle.Fill,
                            TextAlign = System.Drawing.ContentAlignment.MiddleCenter,
                            Font = new System.Drawing.Font("Arial", 12, System.Drawing.FontStyle.Bold)
                        };

                        loadingForm.Controls.Add(lblMessage);
                        loadingForm.Show();
                        loadingForm.Refresh();

                        // محاكاة البحث عن تحديثات
                        System.Threading.Thread.Sleep(2000);

                        // تحقق إذا كان هناك تحديث (منطق التحقق يمكن تغييره حسب الحاجة)
                        bool isUpdateAvailable = true; // استبدل هذا بمنطق التحقق الفعلي

                        // إغلاق نافذة التحميل
                        loadingForm.Close();

                        if (isUpdateAvailable)
                        {
                            // إعداد معلومات تشغيل ArshfUpdater.exe
                            ProcessStartInfo startInfo = new ProcessStartInfo
                            {
                                FileName = updaterPath,
                                UseShellExecute = true,
                                WindowStyle = ProcessWindowStyle.Normal // لضمان ظهور النافذة في المقدمة
                            };

                            // تشغيل التحديث
                            Process.Start(startInfo);

                            // إخفاء نموذج الإعدادات الحالي (اختياري)
                            this.Hide();
                        }
                        else
                        {
                            // رسالة عند عدم وجود تحديثات جديدة
                            MessageBox.Show("برنامجك محدث بالفعل. لا توجد تحديثات جديدة.",
                                            "التحديث", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
                else
                {
                    // رسالة خطأ عند عدم العثور على ملف التحديث
                    MessageBox.Show("لم يتم العثور على برنامج التحديث! تأكد من وجود الملف 'HRMSDBUpdater.exe' في مجلد المشروع.",
                                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                // التعامل مع الأخطاء
                MessageBox.Show($"حدث خطأ أثناء محاولة تشغيل برنامج التحديث:\n{ex.Message}",
                                "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnBackupDocuments_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog folderDialog = new FolderBrowserDialog())
            {
                folderDialog.Description = "اختر مجلد لحفظ نسخة من الملفات";
                if (folderDialog.ShowDialog() == DialogResult.OK)
                {
                    string sourcePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "documents");
                    string targetPath = Path.Combine(folderDialog.SelectedPath, $"documents_backup_{DateTime.Now:yyyyMMdd_HHmmss}");

                    if (Directory.Exists(sourcePath))
                    {
                        CopyDirectory(sourcePath, targetPath);
                        MessageBox.Show("تم نسخ المجلد بنجاح", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("مجلد الملفات غير موجود", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }
        private void CopyDirectory(string sourceDir, string targetDir)
        {
            Directory.CreateDirectory(targetDir);
            foreach (string file in Directory.GetFiles(sourceDir))
            {
                string destFile = Path.Combine(targetDir, Path.GetFileName(file));
                File.Copy(file, destFile, true);
            }

            foreach (string folder in Directory.GetDirectories(sourceDir))
            {
                string destFolder = Path.Combine(targetDir, Path.GetFileName(folder));
                CopyDirectory(folder, destFolder);
            }
        }

        private void btnRestoreDocuments_Click(object sender, EventArgs e)
        {
            try
            {
                using (FolderBrowserDialog folderDialog = new FolderBrowserDialog())
                {
                    folderDialog.Description = "اختر مجلد النسخة الاحتياطية للملفات";

                    if (folderDialog.ShowDialog() == DialogResult.OK)
                    {
                        string sourcePath = folderDialog.SelectedPath;
                        string targetPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "documents");

                        if (MessageBox.Show("سيتم استبدال مجلد الملفات الحالي. هل أنت متأكد؟", "تأكيد",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                        {
                            // إغلاق أي ملفات مفتوحة في المسار (إن وجدت)
                            GC.Collect();
                            GC.WaitForPendingFinalizers();

                            // حذف المجلد الحالي إن وجد
                            if (Directory.Exists(targetPath))
                                Directory.Delete(targetPath, true);

                            // نسخ المجلد من النسخة الاحتياطية إلى المسار الأصلي
                            CopyDirectory(sourcePath, targetPath);

                            MessageBox.Show("تم استعادة مجلد الملفات بنجاح!", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء استعادة مجلد الملفات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

    }
}