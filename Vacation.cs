using System;
using System.Text;

namespace EmployeeManagementSystem
{
    public class Vacation
    {
        public int VacationId { get; set; }
        public int EmployeeId { get; set; }
        public string? EmployeeName { get; set; }
        public string? VacationType { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int DaysCount { get; set; }
        public string? Reason { get; set; }

        public string GetHtmlReport()
        {
            StringBuilder html = new StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير الإجازة</title>
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            padding: 20px;
            direction: rtl;
            background-color: #f5f5f5;
        }
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #45678a;
            color: white;
            border-radius: 10px;
        }
        .vacation-details {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .detail-row {
            display: flex;
            margin-bottom: 15px;
            align-items: center;
        }
        .label {
            font-weight: bold;
            min-width: 150px;
            color: #45678a;
        }
        .value {
            flex: 1;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        .signature-box {
            text-align: center;
            flex: 1;
            margin: 0 20px;
            padding: 20px;
            border: 2px solid #45678a;
            border-radius: 10px;
        }
        @media print {
            body {
                background-color: white;
            }
            .report-header {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .signature-section {
                break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class=""report-header"">
        <h1>تقرير إجازة موظف</h1>
        <p>التاريخ: " + DateTime.Now.ToString("dd/MM/yyyy") + @"</p>
    </div>
    
    <div class=""vacation-details"">");

            AddDetailRow(html, "اسم الموظف", EmployeeName ?? "");
            AddDetailRow(html, "نوع الإجازة", VacationType ?? "");
            AddDetailRow(html, "تاريخ بداية الإجازة", StartDate.ToString("dd/MM/yyyy"));
            AddDetailRow(html, "تاريخ نهاية الإجازة", EndDate.ToString("dd/MM/yyyy"));
            AddDetailRow(html, "عدد أيام الإجازة", DaysCount.ToString());
            AddDetailRow(html, "سبب الإجازة", Reason ?? "");

            html.Append(@"</div>

    <div class=""signature-section"">
        <div class=""signature-box"">
            <h3>توقيع الموظف</h3>
            <div style=""height: 60px;""></div>
            <p>التاريخ: ________________</p>
        </div>
        <div class=""signature-box"">
            <h3>توقيع المدير المباشر</h3>
            <div style=""height: 60px;""></div>
            <p>التاريخ: ________________</p>
        </div>
        <div class=""signature-box"">
            <h3>تصديق شؤون الموظفين</h3>
            <div style=""height: 60px;""></div>
            <p>التاريخ: ________________</p>
        </div>
    </div>
</body>
</html>");

            return html.ToString();
        }

        private void AddDetailRow(StringBuilder html, string label, string value)
        {
            html.AppendFormat(@"
        <div class=""detail-row"">
            <div class=""label"">{0}:</div>
            <div class=""value"">{1}</div>
        </div>", label, value);
        }
    }
}