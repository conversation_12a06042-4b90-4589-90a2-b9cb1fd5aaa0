﻿namespace EmployeeManagementSystem
{
    partial class CameraForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            pictureBox1 = new PictureBox();
            btnCapture = new Button();
            btnStopCamera = new Button();
            toolTip1 = new ToolTip(components);
            ((System.ComponentModel.ISupportInitialize)pictureBox1).BeginInit();
            SuspendLayout();
            // 
            // pictureBox1
            // 
            pictureBox1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            pictureBox1.BackgroundImageLayout = ImageLayout.None;
            pictureBox1.Location = new Point(12, 12);
            pictureBox1.Name = "pictureBox1";
            pictureBox1.Size = new Size(533, 426);
            pictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBox1.TabIndex = 0;
            pictureBox1.TabStop = false;
            // 
            // btnCapture
            // 
            btnCapture.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            btnCapture.BackColor = Color.FromArgb(45, 66, 91);
            btnCapture.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnCapture.ForeColor = Color.White;
            btnCapture.Image = Properties.Resources.camera_32px;
            btnCapture.ImageAlign = ContentAlignment.MiddleLeft;
            btnCapture.Location = new Point(203, 444);
            btnCapture.Name = "btnCapture";
            btnCapture.RightToLeft = RightToLeft.No;
            btnCapture.Size = new Size(342, 45);
            btnCapture.TabIndex = 1;
            btnCapture.Text = "التقاط صورة";
            toolTip1.SetToolTip(btnCapture, "التقاط صورة");
            btnCapture.UseVisualStyleBackColor = false;
            btnCapture.Click += BtnCapture_Click;
            // 
            // btnStopCamera
            // 
            btnStopCamera.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            btnStopCamera.BackColor = Color.FromArgb(45, 66, 91);
            btnStopCamera.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnStopCamera.ForeColor = Color.White;
            btnStopCamera.Image = Properties.Resources.stop_32px;
            btnStopCamera.ImageAlign = ContentAlignment.MiddleLeft;
            btnStopCamera.Location = new Point(12, 444);
            btnStopCamera.Name = "btnStopCamera";
            btnStopCamera.Size = new Size(185, 45);
            btnStopCamera.TabIndex = 1;
            btnStopCamera.Text = "ايقاف الكامرة";
            toolTip1.SetToolTip(btnStopCamera, "ايقاف الكامرة");
            btnStopCamera.UseVisualStyleBackColor = false;
            btnStopCamera.Click += BtnStopCamera_Click;
            // 
            // CameraForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(557, 497);
            Controls.Add(btnStopCamera);
            Controls.Add(btnCapture);
            Controls.Add(pictureBox1);
            FormBorderStyle = FormBorderStyle.FixedSingle;
            MaximizeBox = false;
            Name = "CameraForm";
            RightToLeft = RightToLeft.No;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "الكامرة";
            FormClosing += CameraForm_FormClosing;
            Load += CameraForm_Load;
            ((System.ComponentModel.ISupportInitialize)pictureBox1).EndInit();
            ResumeLayout(false);
        }

        #endregion

        private PictureBox pictureBox1;
        private Button btnCapture;
        private Button btnStopCamera;
        private ToolTip toolTip1;
    }
}