﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class AboutForm : Form
    {

        public AboutForm()
        {
            InitializeComponent();
            this.label3.Text = "Copyright © " + DateTime.Now.Year.ToString();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            var psi = new ProcessStartInfo
            {
                FileName = "http://www.youtube.com/@gxr9087",
                UseShellExecute = true // هذا السطر مهم جدًا
            };
            Process.Start(psi);
        }

        private void AboutForm_Load(object sender, EventArgs e)
        {
            string appVersion = GetApplicationVersion();
            labelVersion.Text = $"Version {appVersion}";
        }

        private string GetApplicationVersion()
        {
            return System.Reflection.Assembly.GetExecutingAssembly()?.GetName()?.Version?.ToString() ?? "غير معروف";
        }



    }
}
