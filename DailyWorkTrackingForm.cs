using System;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class DailyWorkTrackingForm : Form
    {
        private int workPeriodId;
        private DateTime startDate;
        private DateTime endDate;
        private string projectName;
        private DataTable employeesData;
        
        // Controls
        private Label lblTitle;
        private Label lblPeriodInfo;
        private DataGridView dataGridViewTracking;
        private Button btnSave;
        private Button btnClose;
        private Button btnPreviousMonth;
        private Button btnNextMonth;
        private Label lblCurrentMonth;
        private DateTime currentDisplayMonth;

        public DailyWorkTrackingForm(int workPeriodId, DateTime startDate, DateTime endDate, string projectName)
        {
            this.workPeriodId = workPeriodId;
            this.startDate = startDate;
            this.endDate = endDate;
            this.projectName = projectName;
            this.currentDisplayMonth = startDate;
            
            InitializeComponent();
            LoadEmployeesData();
            SetupDataGridView();
            LoadTrackingData();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(1200, 700);
            this.Text = "التتبع اليومي لفترة العمل";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Font = new Font("Cairo", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // العنوان
            lblTitle = new Label
            {
                Text = "التتبع اليومي لحالة الموظفين",
                Font = new Font("Cairo", 14F, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 40
            };

            // معلومات الفترة
            lblPeriodInfo = new Label
            {
                Text = $"مكان العمل: {projectName} | الفترة: من {startDate:dd/MM/yyyy} إلى {endDate:dd/MM/yyyy}",
                Font = new Font("Cairo", 10F),
                ForeColor = Color.DarkGreen,
                TextAlign = ContentAlignment.MiddleCenter,
                Height = 30,
                Top = 40
            };

            // التنقل بين الشهور
            var navigationPanel = new Panel
            {
                Height = 40,
                Top = 70,
                Dock = DockStyle.Top
            };

            btnPreviousMonth = new Button
            {
                Text = "الشهر السابق",
                Size = new Size(100, 30),
                Location = new Point(10, 5),
                BackColor = Color.LightBlue
            };
            btnPreviousMonth.Click += BtnPreviousMonth_Click;

            btnNextMonth = new Button
            {
                Text = "الشهر التالي",
                Size = new Size(100, 30),
                Location = new Point(120, 5),
                BackColor = Color.LightBlue
            };
            btnNextMonth.Click += BtnNextMonth_Click;

            lblCurrentMonth = new Label
            {
                Text = currentDisplayMonth.ToString("MMMM yyyy"),
                Font = new Font("Cairo", 12F, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Size = new Size(200, 30),
                Location = new Point(400, 5),
                ForeColor = Color.DarkBlue
            };

            navigationPanel.Controls.AddRange(new Control[] { btnPreviousMonth, btnNextMonth, lblCurrentMonth });

            // جدول التتبع
            dataGridViewTracking = new DataGridView
            {
                Top = 110,
                Height = 450,
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.CellSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                BackgroundColor = Color.White,
                GridColor = Color.LightGray
            };
            dataGridViewTracking.CellClick += DataGridViewTracking_CellClick;

            // الأزرار
            var buttonPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom
            };

            btnSave = new Button
            {
                Text = "حفظ التغييرات",
                Size = new Size(120, 35),
                Location = new Point(20, 10),
                BackColor = Color.Green,
                ForeColor = Color.White,
                Font = new Font("Cairo", 10F, FontStyle.Bold)
            };
            btnSave.Click += BtnSave_Click;

            btnClose = new Button
            {
                Text = "إغلاق",
                Size = new Size(100, 35),
                Location = new Point(150, 10),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                Font = new Font("Cairo", 10F, FontStyle.Bold)
            };
            btnClose.Click += (s, e) => this.Close();

            buttonPanel.Controls.AddRange(new Control[] { btnSave, btnClose });

            // إضافة العناصر للنموذج
            this.Controls.AddRange(new Control[] { lblTitle, lblPeriodInfo, navigationPanel, dataGridViewTracking, buttonPanel });
        }

        private void LoadEmployeesData()
        {
            try
            {
                // الحصول على موظفي فترة العمل
                employeesData = DatabaseHelper.GetWorkPeriodEmployees(workPeriodId);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetupDataGridView()
        {
            try
            {
                var table = new DataTable();
                
                // إضافة عمود اسم الموظف
                table.Columns.Add("الموظف", typeof(string));
                
                // إضافة أعمدة الأيام للشهر الحالي
                DateTime monthStart = new DateTime(currentDisplayMonth.Year, currentDisplayMonth.Month, 1);
                DateTime monthEnd = monthStart.AddMonths(1).AddDays(-1);
                
                // تحديد نطاق الأيام (تقاطع الشهر مع فترة العمل)
                DateTime displayStart = monthStart > startDate ? monthStart : startDate;
                DateTime displayEnd = monthEnd < endDate ? monthEnd : endDate;
                
                for (DateTime date = displayStart; date <= displayEnd; date = date.AddDays(1))
                {
                    table.Columns.Add(date.Day.ToString(), typeof(string));
                }
                
                dataGridViewTracking.DataSource = table;
                
                // تنسيق الأعمدة
                foreach (DataGridViewColumn column in dataGridViewTracking.Columns)
                {
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.HeaderCell.Style.Font = new Font("Cairo", 9F, FontStyle.Bold);
                    
                    if (column.Name != "الموظف")
                    {
                        column.Width = 40;
                        column.DefaultCellStyle.Font = new Font("Cairo", 8F);
                    }
                    else
                    {
                        column.Width = 150;
                        column.DefaultCellStyle.Font = new Font("Cairo", 9F, FontStyle.Bold);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعداد الجدول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadTrackingData()
        {
            try
            {
                var table = (DataTable)dataGridViewTracking.DataSource;
                table.Clear();
                
                if (employeesData == null || employeesData.Rows.Count == 0)
                    return;
                
                DateTime monthStart = new DateTime(currentDisplayMonth.Year, currentDisplayMonth.Month, 1);
                DateTime monthEnd = monthStart.AddMonths(1).AddDays(-1);
                DateTime displayStart = monthStart > startDate ? monthStart : startDate;
                DateTime displayEnd = monthEnd < endDate ? monthEnd : endDate;
                
                foreach (DataRow empRow in employeesData.Rows)
                {
                    int employeeCode = Convert.ToInt32(empRow["EmployeeCode"]);
                    string employeeName = empRow["EmployeeName"].ToString();
                    
                    var newRow = table.NewRow();
                    newRow["الموظف"] = employeeName;
                    
                    // تحميل حالة كل يوم
                    for (DateTime date = displayStart; date <= displayEnd; date = date.AddDays(1))
                    {
                        string status = GetEmployeeStatusForDate(employeeCode, date);
                        newRow[date.Day.ToString()] = GetStatusDisplay(status);
                    }
                    
                    table.Rows.Add(newRow);
                }
                
                // تلوين الخلايا
                ColorizeDataGridView();
                
                // تحديث عنوان الشهر
                lblCurrentMonth.Text = currentDisplayMonth.ToString("MMMM yyyy");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات التتبع: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string GetEmployeeStatusForDate(int employeeCode, DateTime date)
        {
            try
            {
                // البحث في قاعدة البيانات عن حالة الموظف في هذا التاريخ
                using (var connection = new System.Data.SQLite.SQLiteConnection(DatabaseHelper.ConnectionString))
                {
                    connection.Open();
                    string sql = @"SELECT Status FROM DailyWorkStatus 
                                 WHERE WorkPeriodId = @WorkPeriodId 
                                 AND EmployeeCode = @EmployeeCode 
                                 AND Date = @Date";
                    
                    using (var command = new System.Data.SQLite.SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                        command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                        command.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));
                        
                        var result = command.ExecuteScalar();
                        return result?.ToString() ?? "حضور"; // افتراضي
                    }
                }
            }
            catch
            {
                return "حضور"; // افتراضي في حالة الخطأ
            }
        }

        private string GetStatusDisplay(string status)
        {
            switch (status)
            {
                case "حضور": return "ح";
                case "غياب": return "غ";
                case "إجازة": return "ج";
                default: return "ح";
            }
        }

        private void ColorizeDataGridView()
        {
            foreach (DataGridViewRow row in dataGridViewTracking.Rows)
            {
                foreach (DataGridViewCell cell in row.Cells)
                {
                    if (cell.ColumnIndex == 0) continue; // تجاهل عمود الاسم
                    
                    string value = cell.Value?.ToString() ?? "";
                    switch (value)
                    {
                        case "ح": // حضور
                            cell.Style.BackColor = Color.LightGreen;
                            cell.Style.ForeColor = Color.DarkGreen;
                            break;
                        case "غ": // غياب
                            cell.Style.BackColor = Color.LightCoral;
                            cell.Style.ForeColor = Color.DarkRed;
                            break;
                        case "ج": // إجازة
                            cell.Style.BackColor = Color.LightBlue;
                            cell.Style.ForeColor = Color.DarkBlue;
                            break;
                    }
                }
            }
        }

        private void DataGridViewTracking_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex < 0 || e.ColumnIndex <= 0) return;

                var column = dataGridViewTracking.Columns[e.ColumnIndex];
                if (column.Name == "الموظف") return;

                // الحصول على التاريخ والموظف
                int day = int.Parse(column.Name);
                DateTime selectedDate = new DateTime(currentDisplayMonth.Year, currentDisplayMonth.Month, day);

                // التأكد من أن التاريخ ضمن فترة العمل
                if (selectedDate < startDate || selectedDate > endDate) return;

                string employeeName = dataGridViewTracking.Rows[e.RowIndex].Cells["الموظف"].Value.ToString();
                int employeeCode = GetEmployeeCodeByName(employeeName);

                // عرض نافذة تغيير الحالة
                ShowStatusChangeDialog(employeeCode, employeeName, selectedDate, e.RowIndex, e.ColumnIndex);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد الخلية: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowStatusChangeDialog(int employeeCode, string employeeName, DateTime date, int rowIndex, int columnIndex)
        {
            var statusForm = new Form
            {
                Text = "تغيير حالة الموظف",
                Size = new Size(400, 300),
                StartPosition = FormStartPosition.CenterParent,
                Font = new Font("Cairo", 10F),
                RightToLeft = RightToLeft.Yes,
                RightToLeftLayout = true,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var lblInfo = new Label
            {
                Text = $"الموظف: {employeeName}\nالتاريخ: {date:dd/MM/yyyy}",
                Location = new Point(20, 20),
                Size = new Size(350, 50),
                Font = new Font("Cairo", 11F, FontStyle.Bold)
            };

            var lblStatus = new Label
            {
                Text = "الحالة:",
                Location = new Point(20, 80),
                Size = new Size(60, 25)
            };

            var cmbStatus = new ComboBox
            {
                Location = new Point(90, 80),
                Size = new Size(120, 25),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbStatus.Items.AddRange(new[] { "حضور", "غياب", "إجازة" });

            // تحديد الحالة الحالية
            string currentStatus = GetEmployeeStatusForDate(employeeCode, date);
            cmbStatus.SelectedItem = currentStatus;

            var lblNotes = new Label
            {
                Text = "الملاحظات:",
                Location = new Point(20, 120),
                Size = new Size(80, 25)
            };

            var txtNotes = new TextBox
            {
                Location = new Point(20, 150),
                Size = new Size(350, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };

            var btnSave = new Button
            {
                Text = "حفظ",
                Location = new Point(220, 220),
                Size = new Size(80, 30),
                BackColor = Color.Green,
                ForeColor = Color.White,
                Font = new Font("Cairo", 10F, FontStyle.Bold)
            };

            var btnCancel = new Button
            {
                Text = "إلغاء",
                Location = new Point(310, 220),
                Size = new Size(80, 30),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                Font = new Font("Cairo", 10F, FontStyle.Bold)
            };

            btnSave.Click += (s, e) =>
            {
                try
                {
                    string newStatus = cmbStatus.SelectedItem.ToString();
                    string notes = txtNotes.Text.Trim();

                    // تحديث قاعدة البيانات
                    DatabaseHelper.UpdateDailyWorkStatus(workPeriodId, employeeCode, date, newStatus, notes);

                    // تحديث الجدول
                    dataGridViewTracking.Rows[rowIndex].Cells[columnIndex].Value = GetStatusDisplay(newStatus);
                    ColorizeDataGridView();

                    statusForm.Close();

                    MessageBox.Show("تم تحديث الحالة بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حفظ الحالة: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            btnCancel.Click += (s, e) => statusForm.Close();

            statusForm.Controls.AddRange(new Control[] { lblInfo, lblStatus, cmbStatus, lblNotes, txtNotes, btnSave, btnCancel });
            statusForm.ShowDialog();
        }

        private int GetEmployeeCodeByName(string employeeName)
        {
            foreach (DataRow row in employeesData.Rows)
            {
                if (row["EmployeeName"].ToString() == employeeName)
                {
                    return Convert.ToInt32(row["EmployeeCode"]);
                }
            }
            return 0;
        }

        private void BtnPreviousMonth_Click(object sender, EventArgs e)
        {
            currentDisplayMonth = currentDisplayMonth.AddMonths(-1);
            SetupDataGridView();
            LoadTrackingData();
        }

        private void BtnNextMonth_Click(object sender, EventArgs e)
        {
            currentDisplayMonth = currentDisplayMonth.AddMonths(1);
            SetupDataGridView();
            LoadTrackingData();
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            MessageBox.Show("تم حفظ جميع التغييرات", "نجح",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
