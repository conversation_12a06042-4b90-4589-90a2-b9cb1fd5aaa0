using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class DailyWorkTrackingForm : Form
    {
        private int workPeriodId;
        private DateTime startDate;
        private DateTime endDate;
        private string projectName;

        // Controls
        private Label lblTitle;
        private Label lblPeriodInfo;
        private DataGridView dataGridViewEmployees;
        private Button btnSave;
        private Button btnClose;
        private Button btnEditStatus;

        public DailyWorkTrackingForm(int workPeriodId, DateTime startDate, DateTime endDate, string projectName)
        {
            this.workPeriodId = workPeriodId;
            this.startDate = startDate;
            this.endDate = endDate;
            this.projectName = projectName;

            InitializeComponent();
            LoadEmployeesData();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(800, 600);
            this.Text = "التتبع اليومي لفترة العمل";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Font = new Font("Cairo", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // العنوان
            lblTitle = new Label
            {
                Text = "التتبع اليومي لحالة الموظفين",
                Font = new Font("Cairo", 14F, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 40
            };

            // معلومات الفترة
            lblPeriodInfo = new Label
            {
                Text = $"مكان العمل: {projectName} | الفترة: من {startDate:dd/MM/yyyy} إلى {endDate:dd/MM/yyyy}",
                Font = new Font("Cairo", 10F),
                ForeColor = Color.DarkGreen,
                TextAlign = ContentAlignment.MiddleCenter,
                Height = 30,
                Top = 40
            };

            // جدول الموظفين
            dataGridViewEmployees = new DataGridView
            {
                Top = 80,
                Height = 400,
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                BackgroundColor = Color.White,
                GridColor = Color.LightGray
            };

            // الأزرار
            var buttonPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Bottom
            };

            btnEditStatus = new Button
            {
                Text = "تعديل الحالة اليومية",
                Size = new Size(150, 35),
                Location = new Point(20, 15),
                BackColor = Color.Orange,
                ForeColor = Color.White,
                Font = new Font("Cairo", 10F, FontStyle.Bold)
            };
            btnEditStatus.Click += BtnEditStatus_Click;

            btnSave = new Button
            {
                Text = "حفظ التغييرات",
                Size = new Size(120, 35),
                Location = new Point(180, 15),
                BackColor = Color.Green,
                ForeColor = Color.White,
                Font = new Font("Cairo", 10F, FontStyle.Bold)
            };
            btnSave.Click += BtnSave_Click;

            btnClose = new Button
            {
                Text = "إغلاق",
                Size = new Size(100, 35),
                Location = new Point(310, 15),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                Font = new Font("Cairo", 10F, FontStyle.Bold)
            };
            btnClose.Click += (s, e) => this.Close();

            buttonPanel.Controls.AddRange(new Control[] { btnEditStatus, btnSave, btnClose });

            // إضافة العناصر للنموذج
            this.Controls.AddRange(new Control[] { lblTitle, lblPeriodInfo, dataGridViewEmployees, buttonPanel });
        }

        private void LoadEmployeesData()
        {
            try
            {
                // الحصول على موظفي فترة العمل
                var employeesData = DatabaseHelper.GetWorkPeriodEmployees(workPeriodId);

                // إنشاء جدول للعرض
                var displayTable = new DataTable();
                displayTable.Columns.Add("كود الموظف", typeof(int));
                displayTable.Columns.Add("اسم الموظف", typeof(string));
                displayTable.Columns.Add("إجمالي الأيام", typeof(int));
                displayTable.Columns.Add("أيام الحضور", typeof(int));
                displayTable.Columns.Add("أيام الغياب", typeof(int));
                displayTable.Columns.Add("أيام الإجازة", typeof(int));
                displayTable.Columns.Add("معدل الحضور %", typeof(string));

                foreach (DataRow empRow in employeesData.Rows)
                {
                    int employeeCode = Convert.ToInt32(empRow["EmployeeCode"]);
                    string employeeName = empRow["EmployeeName"].ToString();

                    // الحصول على ملخص التتبع اليومي
                    var (totalDays, presentDays, absentDays, vacationDays, attendanceRate) =
                        DatabaseHelper.GetDailyWorkStatusSummary(workPeriodId, employeeCode);

                    var newRow = displayTable.NewRow();
                    newRow["كود الموظف"] = employeeCode;
                    newRow["اسم الموظف"] = employeeName;
                    newRow["إجمالي الأيام"] = totalDays;
                    newRow["أيام الحضور"] = presentDays;
                    newRow["أيام الغياب"] = absentDays;
                    newRow["أيام الإجازة"] = vacationDays;
                    newRow["معدل الحضور %"] = $"{attendanceRate:F1}%";

                    displayTable.Rows.Add(newRow);
                }

                dataGridViewEmployees.DataSource = displayTable;

                // تنسيق الأعمدة
                foreach (DataGridViewColumn column in dataGridViewEmployees.Columns)
                {
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.HeaderCell.Style.Font = new Font("Cairo", 9F, FontStyle.Bold);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnEditStatus_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewEmployees.SelectedRows.Count == 0)
                {
                    MessageBox.Show("الرجاء اختيار موظف لتعديل حالته", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = dataGridViewEmployees.SelectedRows[0];
                int employeeCode = Convert.ToInt32(selectedRow.Cells["كود الموظف"].Value);
                string employeeName = selectedRow.Cells["اسم الموظف"].Value.ToString();

                // فتح نموذج تعديل الحالة اليومية
                var editForm = new EmployeeDailyStatusForm(workPeriodId, employeeCode, employeeName, startDate, endDate);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    // تحديث البيانات
                    LoadEmployeesData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تعديل الحالة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            MessageBox.Show("تم حفظ جميع التغييرات", "نجح",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
