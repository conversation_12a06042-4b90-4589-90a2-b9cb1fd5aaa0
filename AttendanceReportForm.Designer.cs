namespace EmployeeManagementSystem
{
    partial class AttendanceReportForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            groupBoxFilters = new GroupBox();
            btnGenerateReport = new Button();
            btnExportExcel = new Button();
            btnPrintReport = new Button();
            cmbReportType = new ComboBox();
            lblReportType = new Label();
            cmbEmployee = new ComboBox();
            lblEmployee = new Label();
            dateTimePickerEnd = new DateTimePicker();
            dateTimePickerStart = new DateTimePicker();
            lblEndDate = new Label();
            lblStartDate = new Label();
            lblReportTitle = new Label();
            lblEmployeeTitle = new Label();
            lblCreationDate = new Label();
            groupBoxSummary = new GroupBox();
            lblTotalAbsent = new Label();
            lblTotalLate = new Label();
            lblTotalPresent = new Label();
            lblTotalDays = new Label();
            lblAverageWorkingHours = new Label();
            lblTotalOvertimeHours = new Label();
            lblTotalWorkingHours = new Label();
            dataGridViewReport = new DataGridView();
            groupBoxReportData = new GroupBox();
            progressBar = new ProgressBar();
            lblProgress = new Label();
            groupBoxFilters.SuspendLayout();
            groupBoxSummary.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewReport).BeginInit();
            groupBoxReportData.SuspendLayout();
            SuspendLayout();
            // 
            // groupBoxFilters
            // 
            groupBoxFilters.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxFilters.Controls.Add(btnGenerateReport);
            groupBoxFilters.Controls.Add(btnExportExcel);
            groupBoxFilters.Controls.Add(btnPrintReport);
            groupBoxFilters.Controls.Add(cmbReportType);
            groupBoxFilters.Controls.Add(lblReportType);
            groupBoxFilters.Controls.Add(cmbEmployee);
            groupBoxFilters.Controls.Add(lblEmployee);
            groupBoxFilters.Controls.Add(dateTimePickerEnd);
            groupBoxFilters.Controls.Add(dateTimePickerStart);
            groupBoxFilters.Controls.Add(lblEndDate);
            groupBoxFilters.Controls.Add(lblStartDate);
            groupBoxFilters.Font = new Font("Cairo", 9.75F);
            groupBoxFilters.Location = new Point(12, 75);
            groupBoxFilters.Name = "groupBoxFilters";
            groupBoxFilters.RightToLeft = RightToLeft.Yes;
            groupBoxFilters.Size = new Size(1160, 120);
            groupBoxFilters.TabIndex = 0;
            groupBoxFilters.TabStop = false;
            groupBoxFilters.Text = "خيارات التقرير";
            // 
            // btnGenerateReport
            // 
            btnGenerateReport.BackColor = Color.FromArgb(0, 123, 255);
            btnGenerateReport.FlatStyle = FlatStyle.Flat;
            btnGenerateReport.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnGenerateReport.ForeColor = Color.White;
            btnGenerateReport.Location = new Point(20, 70);
            btnGenerateReport.Name = "btnGenerateReport";
            btnGenerateReport.Size = new Size(120, 35);
            btnGenerateReport.TabIndex = 10;
            btnGenerateReport.Text = "إنشاء التقرير";
            btnGenerateReport.UseVisualStyleBackColor = false;
            btnGenerateReport.Click += btnGenerateReport_Click;
            // 
            // btnExportExcel
            // 
            btnExportExcel.BackColor = Color.FromArgb(40, 167, 69);
            btnExportExcel.FlatStyle = FlatStyle.Flat;
            btnExportExcel.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnExportExcel.ForeColor = Color.White;
            btnExportExcel.Location = new Point(160, 70);
            btnExportExcel.Name = "btnExportExcel";
            btnExportExcel.Size = new Size(120, 35);
            btnExportExcel.TabIndex = 9;
            btnExportExcel.Text = "تصدير Excel";
            btnExportExcel.UseVisualStyleBackColor = false;
            btnExportExcel.Click += btnExportExcel_Click;
            // 
            // btnPrintReport
            // 
            btnPrintReport.BackColor = Color.FromArgb(108, 117, 125);
            btnPrintReport.FlatStyle = FlatStyle.Flat;
            btnPrintReport.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnPrintReport.ForeColor = Color.White;
            btnPrintReport.Location = new Point(300, 70);
            btnPrintReport.Name = "btnPrintReport";
            btnPrintReport.Size = new Size(120, 35);
            btnPrintReport.TabIndex = 8;
            btnPrintReport.Text = "طباعة";
            btnPrintReport.UseVisualStyleBackColor = false;
            btnPrintReport.Click += btnPrintReport_Click;
            // 
            // cmbReportType
            // 
            cmbReportType.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbReportType.Font = new Font("Cairo", 9.75F);
            cmbReportType.FormattingEnabled = true;
            cmbReportType.Items.AddRange(new object[] { "تقرير شامل", "الحضور فقط", "الغياب فقط", "المتأخرين فقط", "الساعات الإضافية", "تقرير فترات العمل" });
            cmbReportType.Location = new Point(450, 75);
            cmbReportType.Name = "cmbReportType";
            cmbReportType.Size = new Size(200, 32);
            cmbReportType.TabIndex = 7;
            // 
            // lblReportType
            // 
            lblReportType.AutoSize = true;
            lblReportType.Font = new Font("Cairo", 9.75F);
            lblReportType.Location = new Point(659, 78);
            lblReportType.Name = "lblReportType";
            lblReportType.Size = new Size(68, 24);
            lblReportType.TabIndex = 6;
            lblReportType.Text = "نوع التقرير:";
            // 
            // cmbEmployee
            // 
            cmbEmployee.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbEmployee.Font = new Font("Cairo", 9.75F);
            cmbEmployee.FormattingEnabled = true;
            cmbEmployee.Location = new Point(780, 75);
            cmbEmployee.Name = "cmbEmployee";
            cmbEmployee.Size = new Size(250, 32);
            cmbEmployee.TabIndex = 5;
            // 
            // lblEmployee
            // 
            lblEmployee.AutoSize = true;
            lblEmployee.Font = new Font("Cairo", 9.75F);
            lblEmployee.Location = new Point(1038, 78);
            lblEmployee.Name = "lblEmployee";
            lblEmployee.Size = new Size(59, 24);
            lblEmployee.TabIndex = 4;
            lblEmployee.Text = "الموظف:";
            // 
            // dateTimePickerEnd
            // 
            dateTimePickerEnd.Font = new Font("Cairo", 9.75F);
            dateTimePickerEnd.Format = DateTimePickerFormat.Short;
            dateTimePickerEnd.Location = new Point(450, 35);
            dateTimePickerEnd.Name = "dateTimePickerEnd";
            dateTimePickerEnd.Size = new Size(200, 32);
            dateTimePickerEnd.TabIndex = 3;
            // 
            // dateTimePickerStart
            // 
            dateTimePickerStart.Font = new Font("Cairo", 9.75F);
            dateTimePickerStart.Format = DateTimePickerFormat.Short;
            dateTimePickerStart.Location = new Point(780, 35);
            dateTimePickerStart.Name = "dateTimePickerStart";
            dateTimePickerStart.Size = new Size(250, 32);
            dateTimePickerStart.TabIndex = 2;
            // 
            // lblEndDate
            // 
            lblEndDate.AutoSize = true;
            lblEndDate.Font = new Font("Cairo", 9.75F);
            lblEndDate.Location = new Point(659, 38);
            lblEndDate.Name = "lblEndDate";
            lblEndDate.Size = new Size(77, 24);
            lblEndDate.TabIndex = 1;
            lblEndDate.Text = "تاريخ النهاية:";
            // 
            // lblStartDate
            // 
            lblStartDate.AutoSize = true;
            lblStartDate.Font = new Font("Cairo", 9.75F);
            lblStartDate.Location = new Point(1038, 38);
            lblStartDate.Name = "lblStartDate";
            lblStartDate.Size = new Size(74, 24);
            lblStartDate.TabIndex = 0;
            lblStartDate.Text = "تاريخ البداية:";
            // 
            // lblReportTitle
            // 
            lblReportTitle.Anchor = AnchorStyles.Top;
            lblReportTitle.Font = new Font("Cairo", 16F, FontStyle.Bold);
            lblReportTitle.ForeColor = Color.FromArgb(52, 58, 64);
            lblReportTitle.Location = new Point(12, 9);
            lblReportTitle.Name = "lblReportTitle";
            lblReportTitle.Size = new Size(1160, 35);
            lblReportTitle.TabIndex = 10;
            lblReportTitle.Text = "تقرير الحضور والغياب";
            lblReportTitle.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // lblEmployeeTitle
            // 
            lblEmployeeTitle.Anchor = AnchorStyles.Top;
            lblEmployeeTitle.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblEmployeeTitle.ForeColor = Color.FromArgb(52, 58, 64);
            lblEmployeeTitle.Location = new Point(12, 44);
            lblEmployeeTitle.Name = "lblEmployeeTitle";
            lblEmployeeTitle.Size = new Size(1160, 25);
            lblEmployeeTitle.TabIndex = 11;
            lblEmployeeTitle.TextAlign = ContentAlignment.MiddleCenter;
            lblEmployeeTitle.Visible = false;
            // 
            // lblCreationDate
            // 
            lblCreationDate.Anchor = AnchorStyles.Bottom;
            lblCreationDate.Font = new Font("Cairo", 10F);
            lblCreationDate.ForeColor = Color.FromArgb(108, 117, 125);
            lblCreationDate.Location = new Point(12, 610);
            lblCreationDate.Name = "lblCreationDate";
            lblCreationDate.Size = new Size(1160, 25);
            lblCreationDate.TabIndex = 12;
            lblCreationDate.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // groupBoxSummary
            // 
            groupBoxSummary.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxSummary.Controls.Add(lblTotalAbsent);
            groupBoxSummary.Controls.Add(lblTotalLate);
            groupBoxSummary.Controls.Add(lblTotalPresent);
            groupBoxSummary.Controls.Add(lblTotalDays);
            groupBoxSummary.Controls.Add(lblAverageWorkingHours);
            groupBoxSummary.Controls.Add(lblTotalOvertimeHours);
            groupBoxSummary.Controls.Add(lblTotalWorkingHours);
            groupBoxSummary.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxSummary.Location = new Point(12, 199);
            groupBoxSummary.Name = "groupBoxSummary";
            groupBoxSummary.RightToLeft = RightToLeft.Yes;
            groupBoxSummary.Size = new Size(1160, 100);
            groupBoxSummary.TabIndex = 1;
            groupBoxSummary.TabStop = false;
            groupBoxSummary.Text = "ملخص التقرير";
            // 
            // lblTotalAbsent
            // 
            lblTotalAbsent.AutoSize = true;
            lblTotalAbsent.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblTotalAbsent.ForeColor = Color.Red;
            lblTotalAbsent.Location = new Point(20, 65);
            lblTotalAbsent.Name = "lblTotalAbsent";
            lblTotalAbsent.Size = new Size(85, 24);
            lblTotalAbsent.TabIndex = 6;
            lblTotalAbsent.Text = "أيام الغياب: 0";
            // 
            // lblTotalLate
            // 
            lblTotalLate.AutoSize = true;
            lblTotalLate.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblTotalLate.ForeColor = Color.Orange;
            lblTotalLate.Location = new Point(20, 35);
            lblTotalLate.Name = "lblTotalLate";
            lblTotalLate.Size = new Size(84, 24);
            lblTotalLate.TabIndex = 5;
            lblTotalLate.Text = "أيام التأخير: 0";
            // 
            // lblTotalPresent
            // 
            lblTotalPresent.AutoSize = true;
            lblTotalPresent.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblTotalPresent.ForeColor = Color.Green;
            lblTotalPresent.Location = new Point(200, 65);
            lblTotalPresent.Name = "lblTotalPresent";
            lblTotalPresent.Size = new Size(86, 24);
            lblTotalPresent.TabIndex = 4;
            lblTotalPresent.Text = "أيام الحضور: 0";
            // 
            // lblTotalDays
            // 
            lblTotalDays.AutoSize = true;
            lblTotalDays.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblTotalDays.Location = new Point(200, 35);
            lblTotalDays.Name = "lblTotalDays";
            lblTotalDays.Size = new Size(96, 24);
            lblTotalDays.TabIndex = 3;
            lblTotalDays.Text = "إجمالي الأيام: 0";
            // 
            // lblAverageWorkingHours
            // 
            lblAverageWorkingHours.AutoSize = true;
            lblAverageWorkingHours.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblAverageWorkingHours.Location = new Point(400, 65);
            lblAverageWorkingHours.Name = "lblAverageWorkingHours";
            lblAverageWorkingHours.Size = new Size(149, 24);
            lblAverageWorkingHours.TabIndex = 2;
            lblAverageWorkingHours.Text = "متوسط ساعات العمل: 0";
            // 
            // lblTotalOvertimeHours
            // 
            lblTotalOvertimeHours.AutoSize = true;
            lblTotalOvertimeHours.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblTotalOvertimeHours.ForeColor = Color.Blue;
            lblTotalOvertimeHours.Location = new Point(400, 35);
            lblTotalOvertimeHours.Name = "lblTotalOvertimeHours";
            lblTotalOvertimeHours.Size = new Size(132, 24);
            lblTotalOvertimeHours.TabIndex = 1;
            lblTotalOvertimeHours.Text = "الساعات الإضافية: 0.0";
            // 
            // lblTotalWorkingHours
            // 
            lblTotalWorkingHours.AutoSize = true;
            lblTotalWorkingHours.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblTotalWorkingHours.Location = new Point(650, 35);
            lblTotalWorkingHours.Name = "lblTotalWorkingHours";
            lblTotalWorkingHours.Size = new Size(145, 24);
            lblTotalWorkingHours.TabIndex = 0;
            lblTotalWorkingHours.Text = "إجمالي ساعات العمل: 0";
            // 
            // dataGridViewReport
            // 
            dataGridViewReport.AllowUserToAddRows = false;
            dataGridViewReport.AllowUserToDeleteRows = false;
            dataGridViewReport.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridViewReport.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewReport.BackgroundColor = Color.White;
            dataGridViewReport.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewReport.Location = new Point(20, 30);
            dataGridViewReport.Name = "dataGridViewReport";
            dataGridViewReport.ReadOnly = true;
            dataGridViewReport.RightToLeft = RightToLeft.Yes;
            dataGridViewReport.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewReport.Size = new Size(1120, 295);
            dataGridViewReport.TabIndex = 0;
            dataGridViewReport.DataError += dataGridViewReport_DataError;
            // 
            // groupBoxReportData
            // 
            groupBoxReportData.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxReportData.Controls.Add(dataGridViewReport);
            groupBoxReportData.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxReportData.Location = new Point(12, 305);
            groupBoxReportData.Name = "groupBoxReportData";
            groupBoxReportData.RightToLeft = RightToLeft.Yes;
            groupBoxReportData.Size = new Size(1160, 345);
            groupBoxReportData.TabIndex = 2;
            groupBoxReportData.TabStop = false;
            groupBoxReportData.Text = "بيانات التقرير";
            // 
            // progressBar
            // 
            progressBar.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            progressBar.Location = new Point(12, 659);
            progressBar.Name = "progressBar";
            progressBar.Size = new Size(1000, 23);
            progressBar.TabIndex = 3;
            progressBar.Visible = false;
            // 
            // lblProgress
            // 
            lblProgress.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            lblProgress.AutoSize = true;
            lblProgress.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblProgress.Location = new Point(1020, 658);
            lblProgress.Name = "lblProgress";
            lblProgress.RightToLeft = RightToLeft.Yes;
            lblProgress.Size = new Size(113, 24);
            lblProgress.TabIndex = 4;
            lblProgress.Text = "جاري إنشاء التقرير...";
            lblProgress.Visible = false;
            // 
            // AttendanceReportForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.White;
            ClientSize = new Size(1184, 691);
            Controls.Add(lblProgress);
            Controls.Add(progressBar);
            Controls.Add(groupBoxReportData);
            Controls.Add(groupBoxSummary);
            Controls.Add(groupBoxFilters);
            Controls.Add(lblReportTitle);
            Controls.Add(lblEmployeeTitle);
            Controls.Add(lblCreationDate);
            Name = "AttendanceReportForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            Text = "تقارير الحضور والغياب";
            Load += AttendanceReportForm_Load;
            groupBoxFilters.ResumeLayout(false);
            groupBoxFilters.PerformLayout();
            groupBoxSummary.ResumeLayout(false);
            groupBoxSummary.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewReport).EndInit();
            groupBoxReportData.ResumeLayout(false);
            ResumeLayout(false);
            PerformLayout();

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBoxFilters;
        private System.Windows.Forms.Label lblStartDate;
        private System.Windows.Forms.Label lblEndDate;
        private System.Windows.Forms.DateTimePicker dateTimePickerStart;
        private System.Windows.Forms.DateTimePicker dateTimePickerEnd;
        private System.Windows.Forms.ComboBox cmbEmployee;
        private System.Windows.Forms.Label lblEmployee;
        private System.Windows.Forms.ComboBox cmbReportType;
        private System.Windows.Forms.Label lblReportType;
        private System.Windows.Forms.Button btnPrintReport;
        private System.Windows.Forms.Button btnExportExcel;

        private System.Windows.Forms.Label lblReportTitle;
        private System.Windows.Forms.Label lblEmployeeTitle;
        private System.Windows.Forms.Label lblCreationDate;
        private System.Windows.Forms.Button btnGenerateReport;
        private System.Windows.Forms.GroupBox groupBoxSummary;
        private System.Windows.Forms.Label lblTotalWorkingHours;
        private System.Windows.Forms.Label lblTotalOvertimeHours;
        private System.Windows.Forms.Label lblAverageWorkingHours;
        private System.Windows.Forms.Label lblTotalDays;
        private System.Windows.Forms.Label lblTotalPresent;
        private System.Windows.Forms.Label lblTotalLate;
        private System.Windows.Forms.Label lblTotalAbsent;
        private System.Windows.Forms.DataGridView dataGridViewReport;
        private System.Windows.Forms.GroupBox groupBoxReportData;
        private System.Windows.Forms.ProgressBar progressBar;
        private System.Windows.Forms.Label lblProgress;
    }
}
