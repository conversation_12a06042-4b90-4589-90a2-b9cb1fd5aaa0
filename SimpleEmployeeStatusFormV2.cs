using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public class SimpleEmployeeStatusFormV2 : Form
    {
        private int workPeriodId;
        private int employeeCode;
        private string employeeName;
        private DateTime startDate;
        private DateTime endDate;
        
        private Label lblTitle;
        private ListBox listBoxDays;
        private ComboBox cmbStatus;
        private TextBox txtNotes;
        private Label lblStatus;
        private Label lblNotes;
        private Label lblSelectedDay;
        private Button btnUpdate;
        private Button btnSave;
        private Button btnClose;
        
        private DataTable dailyData;

        public SimpleEmployeeStatusFormV2(int workPeriodId, int employeeCode, string employeeName, DateTime startDate, DateTime endDate)
        {
            this.workPeriodId = workPeriodId;
            this.employeeCode = employeeCode;
            this.employeeName = employeeName;
            this.startDate = startDate;
            this.endDate = endDate;
            
            SetupForm();
            LoadDailyStatus();
        }

        private void SetupForm()
        {
            // إعداد النموذج
            this.Size = new Size(600, 500);
            this.Text = $"تعديل الحالة اليومية - {employeeName}";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Font = new Font("Cairo", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // العنوان
            lblTitle = new Label();
            lblTitle.Text = $"تعديل الحالة اليومية للموظف: {employeeName}";
            lblTitle.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblTitle.ForeColor = Color.DarkBlue;
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            lblTitle.Location = new Point(10, 10);
            lblTitle.Size = new Size(560, 40);

            // قائمة الأيام
            listBoxDays = new ListBox();
            listBoxDays.Location = new Point(10, 60);
            listBoxDays.Size = new Size(300, 300);
            listBoxDays.Font = new Font("Cairo", 9F);
            listBoxDays.SelectedIndexChanged += ListBoxDays_SelectedIndexChanged;

            // تسمية اليوم المحدد
            lblSelectedDay = new Label();
            lblSelectedDay.Text = "اختر يوم من القائمة";
            lblSelectedDay.Location = new Point(320, 60);
            lblSelectedDay.Size = new Size(250, 30);
            lblSelectedDay.Font = new Font("Cairo", 10F, FontStyle.Bold);
            lblSelectedDay.ForeColor = Color.DarkGreen;

            // تسمية الحالة
            lblStatus = new Label();
            lblStatus.Text = "الحالة:";
            lblStatus.Location = new Point(320, 100);
            lblStatus.Size = new Size(60, 25);

            // قائمة الحالة
            cmbStatus = new ComboBox();
            cmbStatus.Location = new Point(320, 130);
            cmbStatus.Size = new Size(120, 25);
            cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbStatus.Items.AddRange(new[] { "حضور", "غياب", "إجازة" });

            // تسمية الملاحظات
            lblNotes = new Label();
            lblNotes.Text = "الملاحظات:";
            lblNotes.Location = new Point(320, 170);
            lblNotes.Size = new Size(80, 25);

            // مربع الملاحظات
            txtNotes = new TextBox();
            txtNotes.Location = new Point(320, 200);
            txtNotes.Size = new Size(250, 60);
            txtNotes.Multiline = true;
            txtNotes.ScrollBars = ScrollBars.Vertical;

            // زر التحديث
            btnUpdate = new Button();
            btnUpdate.Text = "تحديث اليوم";
            btnUpdate.Location = new Point(320, 270);
            btnUpdate.Size = new Size(100, 35);
            btnUpdate.BackColor = Color.Blue;
            btnUpdate.ForeColor = Color.White;
            btnUpdate.Font = new Font("Cairo", 9F, FontStyle.Bold);
            btnUpdate.Click += BtnUpdate_Click;

            // زر الحفظ
            btnSave = new Button();
            btnSave.Text = "حفظ الكل";
            btnSave.Location = new Point(10, 400);
            btnSave.Size = new Size(120, 35);
            btnSave.BackColor = Color.Green;
            btnSave.ForeColor = Color.White;
            btnSave.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnSave.Click += BtnSave_Click;

            // زر الإغلاق
            btnClose = new Button();
            btnClose.Text = "إغلاق";
            btnClose.Location = new Point(140, 400);
            btnClose.Size = new Size(100, 35);
            btnClose.BackColor = Color.Gray;
            btnClose.ForeColor = Color.White;
            btnClose.Font = new Font("Cairo", 10F, FontStyle.Bold);
            btnClose.Click += (s, e) => this.Close();

            // إضافة العناصر للنموذج
            this.Controls.Add(lblTitle);
            this.Controls.Add(listBoxDays);
            this.Controls.Add(lblSelectedDay);
            this.Controls.Add(lblStatus);
            this.Controls.Add(cmbStatus);
            this.Controls.Add(lblNotes);
            this.Controls.Add(txtNotes);
            this.Controls.Add(btnUpdate);
            this.Controls.Add(btnSave);
            this.Controls.Add(btnClose);
        }

        private void LoadDailyStatus()
        {
            try
            {
                // إنشاء جدول للبيانات
                dailyData = new DataTable();
                dailyData.Columns.Add("التاريخ", typeof(DateTime));
                dailyData.Columns.Add("اليوم", typeof(string));
                dailyData.Columns.Add("الحالة", typeof(string));
                dailyData.Columns.Add("الملاحظات", typeof(string));
                dailyData.Columns.Add("العرض", typeof(string));

                // إضافة الأيام
                for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    var statusData = GetDailyStatus(date);
                    string dayName = GetDayName(date.DayOfWeek);
                    string displayText = $"{date:dd/MM/yyyy} - {dayName} - {statusData.Status}";
                    
                    var newRow = dailyData.NewRow();
                    newRow["التاريخ"] = date;
                    newRow["اليوم"] = dayName;
                    newRow["الحالة"] = statusData.Status;
                    newRow["الملاحظات"] = statusData.Notes;
                    newRow["العرض"] = displayText;
                    
                    dailyData.Rows.Add(newRow);
                }

                // تحديث قائمة الأيام
                UpdateDaysList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateDaysList()
        {
            listBoxDays.Items.Clear();
            foreach (DataRow row in dailyData.Rows)
            {
                listBoxDays.Items.Add(row["العرض"].ToString());
            }
        }

        private (string Status, string Notes) GetDailyStatus(DateTime date)
        {
            try
            {
                using (var connection = new System.Data.SQLite.SQLiteConnection(DatabaseHelper.ConnectionString))
                {
                    connection.Open();
                    string sql = @"SELECT Status, Notes FROM DailyWorkStatus 
                                 WHERE WorkPeriodId = @WorkPeriodId 
                                 AND EmployeeCode = @EmployeeCode 
                                 AND Date = @Date";
                    
                    using (var command = new System.Data.SQLite.SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                        command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                        command.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return (reader.GetString("Status"), reader.IsDBNull("Notes") ? "" : reader.GetString("Notes"));
                            }
                        }
                    }
                }
                return ("حضور", ""); // افتراضي
            }
            catch
            {
                return ("حضور", ""); // افتراضي في حالة الخطأ
            }
        }

        private string GetDayName(DayOfWeek dayOfWeek)
        {
            switch (dayOfWeek)
            {
                case DayOfWeek.Sunday: return "الأحد";
                case DayOfWeek.Monday: return "الاثنين";
                case DayOfWeek.Tuesday: return "الثلاثاء";
                case DayOfWeek.Wednesday: return "الأربعاء";
                case DayOfWeek.Thursday: return "الخميس";
                case DayOfWeek.Friday: return "الجمعة";
                case DayOfWeek.Saturday: return "السبت";
                default: return "";
            }
        }

        private void ListBoxDays_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxDays.SelectedIndex >= 0 && listBoxDays.SelectedIndex < dailyData.Rows.Count)
            {
                var selectedRow = dailyData.Rows[listBoxDays.SelectedIndex];
                DateTime selectedDate = (DateTime)selectedRow["التاريخ"];
                
                lblSelectedDay.Text = $"اليوم المحدد: {selectedDate:dd/MM/yyyy} - {selectedRow["اليوم"]}";
                cmbStatus.SelectedItem = selectedRow["الحالة"].ToString();
                txtNotes.Text = selectedRow["الملاحظات"].ToString();
            }
        }

        private void BtnUpdate_Click(object sender, EventArgs e)
        {
            if (listBoxDays.SelectedIndex >= 0 && cmbStatus.SelectedItem != null)
            {
                var selectedRow = dailyData.Rows[listBoxDays.SelectedIndex];
                selectedRow["الحالة"] = cmbStatus.SelectedItem.ToString();
                selectedRow["الملاحظات"] = txtNotes.Text;
                
                // تحديث النص المعروض
                DateTime date = (DateTime)selectedRow["التاريخ"];
                string dayName = selectedRow["اليوم"].ToString();
                string status = selectedRow["الحالة"].ToString();
                selectedRow["العرض"] = $"{date:dd/MM/yyyy} - {dayName} - {status}";
                
                UpdateDaysList();
                listBoxDays.SelectedIndex = listBoxDays.SelectedIndex; // الحفاظ على التحديد
                
                MessageBox.Show("تم تحديث اليوم محلياً. اضغط 'حفظ الكل' لحفظ التغييرات", "تم التحديث",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                int updatedCount = 0;
                
                foreach (DataRow row in dailyData.Rows)
                {
                    DateTime date = (DateTime)row["التاريخ"];
                    string status = row["الحالة"].ToString();
                    string notes = row["الملاحظات"].ToString();
                    
                    // تحديث قاعدة البيانات
                    DatabaseHelper.UpdateDailyWorkStatus(workPeriodId, employeeCode, date, status, notes);
                    updatedCount++;
                }
                
                MessageBox.Show($"تم تحديث {updatedCount} يوم بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
