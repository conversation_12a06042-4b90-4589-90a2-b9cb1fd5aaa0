namespace EmployeeManagementSystem
{
    partial class DocumentManagementForm
    {
        private System.ComponentModel.IContainer components = null;
        private System.Windows.Forms.DataGridView dgvDocuments;
        private System.Windows.Forms.Button btnAdd;
        private System.Windows.Forms.Button btnDelete;
        private System.Windows.Forms.Button btnView;
        private System.Windows.Forms.TextBox txtDescription;
        private System.Windows.Forms.Label lblDescription;
        private System.Windows.Forms.ComboBox cmbEmployeeFiles;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            dgvDocuments = new DataGridView();
            btnAdd = new Button();
            btnDelete = new Button();
            btnView = new Button();
            txtDescription = new TextBox();
            lblDescription = new Label();
            cmbEmployeeFiles = new ComboBox();
            label1 = new Label();
            btn_Scan = new Button();
            button1 = new Button();
            listOfScanner = new ComboBox();
            numericUpDown1 = new NumericUpDown();
            toolTip1 = new ToolTip(components);
            lbl_NoDocuments = new Label();
            ((System.ComponentModel.ISupportInitialize)dgvDocuments).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDown1).BeginInit();
            SuspendLayout();
            // 
            // dgvDocuments
            // 
            dgvDocuments.AllowUserToAddRows = false;
            dgvDocuments.AllowUserToDeleteRows = false;
            dgvDocuments.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dgvDocuments.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvDocuments.BackgroundColor = Color.White;
            dgvDocuments.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle1.Alignment = DataGridViewContentAlignment.MiddleRight;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(45, 66, 91);
            dataGridViewCellStyle1.Font = new Font("Segoe UI", 10.2F);
            dataGridViewCellStyle1.ForeColor = Color.White;
            dataGridViewCellStyle1.SelectionBackColor = Color.FromArgb(45, 66, 91);
            dataGridViewCellStyle1.SelectionForeColor = Color.White;
            dataGridViewCellStyle1.WrapMode = DataGridViewTriState.True;
            dgvDocuments.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            dgvDocuments.ColumnHeadersHeight = 40;
            dgvDocuments.EnableHeadersVisualStyles = false;
            dgvDocuments.GridColor = Color.FromArgb(45, 66, 91);
            dgvDocuments.Location = new Point(12, 56);
            dgvDocuments.Margin = new Padding(3, 2, 3, 2);
            dgvDocuments.Name = "dgvDocuments";
            dgvDocuments.ReadOnly = true;
            dgvDocuments.RowHeadersVisible = false;
            dgvDocuments.RowHeadersWidth = 51;
            dgvDocuments.RowTemplate.Height = 29;
            dgvDocuments.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvDocuments.Size = new Size(880, 362);
            dgvDocuments.TabIndex = 0;
            dgvDocuments.SelectionChanged += dgvDocuments_SelectionChanged;
            // 
            // btnAdd
            // 
            btnAdd.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnAdd.BackColor = Color.FromArgb(45, 66, 91);
            btnAdd.FlatAppearance.BorderSize = 0;
            btnAdd.FlatStyle = FlatStyle.Flat;
            btnAdd.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnAdd.ForeColor = Color.White;
            btnAdd.Image = Properties.Resources.add_file_32px;
            btnAdd.ImageAlign = ContentAlignment.MiddleLeft;
            btnAdd.Location = new Point(580, 13);
            btnAdd.Margin = new Padding(3, 2, 3, 2);
            btnAdd.Name = "btnAdd";
            btnAdd.Size = new Size(116, 39);
            btnAdd.TabIndex = 2;
            btnAdd.Text = "إضافة ";
            btnAdd.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnAdd, "إضافة ملفات");
            btnAdd.UseVisualStyleBackColor = false;
            btnAdd.Click += btnAdd_Click;
            // 
            // btnDelete
            // 
            btnDelete.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnDelete.BackColor = Color.FromArgb(192, 0, 0);
            btnDelete.FlatAppearance.BorderSize = 0;
            btnDelete.FlatStyle = FlatStyle.Flat;
            btnDelete.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnDelete.ForeColor = Color.White;
            btnDelete.Image = Properties.Resources.remove_32px;
            btnDelete.ImageAlign = ContentAlignment.MiddleLeft;
            btnDelete.Location = new Point(661, 426);
            btnDelete.Margin = new Padding(3, 2, 3, 2);
            btnDelete.Name = "btnDelete";
            btnDelete.Size = new Size(111, 38);
            btnDelete.TabIndex = 3;
            btnDelete.Text = "حذف";
            btnDelete.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnDelete, "حذف ملف");
            btnDelete.UseVisualStyleBackColor = false;
            btnDelete.Click += btnDelete_Click;
            // 
            // btnView
            // 
            btnView.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnView.BackColor = Color.FromArgb(45, 66, 91);
            btnView.FlatAppearance.BorderSize = 0;
            btnView.FlatStyle = FlatStyle.Flat;
            btnView.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnView.ForeColor = Color.White;
            btnView.Image = Properties.Resources.picture_32px;
            btnView.ImageAlign = ContentAlignment.MiddleLeft;
            btnView.Location = new Point(778, 426);
            btnView.Margin = new Padding(3, 2, 3, 2);
            btnView.Name = "btnView";
            btnView.Size = new Size(115, 38);
            btnView.TabIndex = 4;
            btnView.Text = "عرض";
            btnView.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnView, "عرض الملف");
            btnView.UseVisualStyleBackColor = false;
            btnView.Click += btnView_Click;
            // 
            // txtDescription
            // 
            txtDescription.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtDescription.Location = new Point(14, 13);
            txtDescription.Margin = new Padding(3, 2, 3, 2);
            txtDescription.Name = "txtDescription";
            txtDescription.PlaceholderText = "وصف الوثيقة";
            txtDescription.RightToLeft = RightToLeft.Yes;
            txtDescription.Size = new Size(442, 37);
            txtDescription.TabIndex = 1;
            toolTip1.SetToolTip(txtDescription, "اضافة وصف الوثيقة او الملف");
            // 
            // lblDescription
            // 
            lblDescription.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            lblDescription.AutoSize = true;
            lblDescription.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblDescription.Location = new Point(460, 16);
            lblDescription.Name = "lblDescription";
            lblDescription.Size = new Size(114, 30);
            lblDescription.TabIndex = 5;
            lblDescription.Text = "وصف الوثيقة:";
            // 
            // cmbEmployeeFiles
            // 
            cmbEmployeeFiles.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            cmbEmployeeFiles.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbEmployeeFiles.Font = new Font("Cairo", 12F, FontStyle.Bold);
            cmbEmployeeFiles.Location = new Point(268, 426);
            cmbEmployeeFiles.Name = "cmbEmployeeFiles";
            cmbEmployeeFiles.Size = new Size(188, 38);
            cmbEmployeeFiles.TabIndex = 6;
            toolTip1.SetToolTip(cmbEmployeeFiles, "اختيار الملف ");
            // 
            // label1
            // 
            label1.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label1.AutoSize = true;
            label1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            label1.Location = new Point(466, 429);
            label1.Name = "label1";
            label1.Size = new Size(189, 30);
            label1.TabIndex = 7;
            label1.Text = "قتح في مستعرض خارجي";
            // 
            // btn_Scan
            // 
            btn_Scan.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btn_Scan.BackColor = Color.FromArgb(45, 66, 91);
            btn_Scan.FlatAppearance.BorderSize = 0;
            btn_Scan.FlatStyle = FlatStyle.Flat;
            btn_Scan.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btn_Scan.ForeColor = Color.White;
            btn_Scan.Image = Properties.Resources.scanner_32px;
            btn_Scan.ImageAlign = ContentAlignment.MiddleLeft;
            btn_Scan.Location = new Point(760, 13);
            btn_Scan.Margin = new Padding(3, 2, 3, 2);
            btn_Scan.Name = "btn_Scan";
            btn_Scan.Size = new Size(132, 39);
            btn_Scan.TabIndex = 2;
            btn_Scan.Text = "الماسح ";
            btn_Scan.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btn_Scan, "تشغيل الماسح الضوئي");
            btn_Scan.UseVisualStyleBackColor = false;
            btn_Scan.Click += btn_Scan_Click;
            // 
            // button1
            // 
            button1.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            button1.BackColor = Color.FromArgb(45, 66, 91);
            button1.FlatAppearance.BorderSize = 0;
            button1.FlatStyle = FlatStyle.Flat;
            button1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            button1.ForeColor = Color.White;
            button1.Image = Properties.Resources.available_updates_32px;
            button1.Location = new Point(194, 426);
            button1.Margin = new Padding(3, 2, 3, 2);
            button1.Name = "button1";
            button1.Size = new Size(68, 38);
            button1.TabIndex = 4;
            toolTip1.SetToolTip(button1, "تحديث اجهزة الماسح الضوئي");
            button1.UseVisualStyleBackColor = false;
            button1.Click += button1_Click;
            // 
            // listOfScanner
            // 
            listOfScanner.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            listOfScanner.DropDownStyle = ComboBoxStyle.DropDownList;
            listOfScanner.Font = new Font("Cairo", 12F, FontStyle.Bold);
            listOfScanner.Location = new Point(12, 426);
            listOfScanner.Name = "listOfScanner";
            listOfScanner.Size = new Size(176, 38);
            listOfScanner.TabIndex = 6;
            toolTip1.SetToolTip(listOfScanner, "اختيار جهاز الماسح الضوئي");
            // 
            // numericUpDown1
            // 
            numericUpDown1.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            numericUpDown1.Font = new Font("Segoe UI", 18F);
            numericUpDown1.Location = new Point(701, 13);
            numericUpDown1.Name = "numericUpDown1";
            numericUpDown1.Size = new Size(54, 39);
            numericUpDown1.TabIndex = 8;
            toolTip1.SetToolTip(numericUpDown1, "اختيار عدد النسخ");
            numericUpDown1.Value = new decimal(new int[] { 1, 0, 0, 0 });
            numericUpDown1.ValueChanged += numericUpDown1_ValueChanged;
            // 
            // lbl_NoDocuments
            // 
            lbl_NoDocuments.Anchor = AnchorStyles.None;
            lbl_NoDocuments.AutoSize = true;
            lbl_NoDocuments.Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lbl_NoDocuments.Location = new Point(423, 220);
            lbl_NoDocuments.Name = "lbl_NoDocuments";
            lbl_NoDocuments.Size = new Size(54, 30);
            lbl_NoDocuments.TabIndex = 35;
            lbl_NoDocuments.Text = "label6";
            lbl_NoDocuments.Visible = false;
            // 
            // DocumentManagementForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.White;
            ClientSize = new Size(901, 471);
            Controls.Add(lbl_NoDocuments);
            Controls.Add(lblDescription);
            Controls.Add(txtDescription);
            Controls.Add(button1);
            Controls.Add(dgvDocuments);
            Controls.Add(listOfScanner);
            Controls.Add(cmbEmployeeFiles);
            Controls.Add(numericUpDown1);
            Controls.Add(btn_Scan);
            Controls.Add(btnAdd);
            Controls.Add(label1);
            Controls.Add(btnView);
            Controls.Add(btnDelete);
            Margin = new Padding(3, 2, 3, 2);
            MinimumSize = new Size(527, 310);
            Name = "DocumentManagementForm";
            StartPosition = FormStartPosition.CenterParent;
            Text = "إدارة الوثائق";
            Load += DocumentManagementForm_Load;
            ((System.ComponentModel.ISupportInitialize)dgvDocuments).EndInit();
            ((System.ComponentModel.ISupportInitialize)numericUpDown1).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }
        private Label label1;
        private Button btn_Scan;
        private Button button1;
        private ComboBox listOfScanner;
        private NumericUpDown numericUpDown1;
        private ToolTip toolTip1;
        private Label lbl_NoDocuments;
    }
}