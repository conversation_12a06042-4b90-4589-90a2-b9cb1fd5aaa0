﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBoxLogo.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAACm5SURBVHhe7d0J
        sJX1medxk9Qk6e5kenqmZquapXsmk1l6uis1a9VsNYnp2G2608aIW8etXbO4I4YkKmLUaCKCbGIURZBN
        ERVFQBEFI4u4IVfEu6CJIt57oynH5J73Pe/hP+8fnlNeLw9w7znn3f7P91f1qVhG5d7nfev8nrO97xGE
        EEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGE
        EEIIIYQQQgghhBBCCCGEEEIIIVnHOffxnn73ub6B5Gu9/Y0LewcaN/f2713WO5A8lf719tTbqXdTv0k5
        ADDOPxb6x0T/2Jg+RqaPlYN770v/+uae/sYF/rHUP6b6x1Z5mCWkHOl7z/3L9IQ9OT1Rp6f/uzk9aT9I
        aSc5AKB16WNrskkea0/qfdf9C3kYJiSf7N7tfnffs/uBxqxU37CTEwCQo76BRm9vf2NmuhD8pX9slodp
        QjqXXbvcp/v6k+N6BhpL0pOOl+4BoHw+SBeCxb2DybH+MVsevglpLbsG3b9Lt8sfpyfWwIgTDQBQXr9O
        l4E5u/rdF+ThnJDDx3/QpHew/uXegb0rlJMKAFApydN9/cm49LH9E/IwT8hH40+OvsHklPSEefXAEwgA
        UGU9A41X0mXgZL5NQD4Svx2mJ8iOkScMACA4Xb3vJN+Qh39iNb397t/0DO5dpZwgAICA9Q0k67p/5f6D
        1AGxEv91kfQEmJSqDT8hAACmxH2DjWld/e4zUg8k5PQMJsekB/2NEScBAMCqwcYuf30XqQkSWvZ9lz/d
        9NSDDwAwr2egcTcXFAos+77PP9B4aeTBBgBgOP9tgZ533X+U+iBVTs9gcmp6ULk+PwBgtH7bN5CcJTVC
        qpbubvepvv7GXcqBBQBgNG7vcu6TUiukCvGf6OTrfQCA9u1d2/0r93elXkiZs+N99w96+pON+oEEAGCs
        kmd79rh/JDVDyphdb7s/TA/WzgMPHgAArfO3Hu7pd5+TuiFliv/UZnqQdo88aAAAdELPQOOt7l+5P5ba
        IWXI63vcH6UHh/IHAGTKLwH+1WapH1Jkut92/zA9KNzBDwCQl+6+d9w/lhoiReTVAffZ9EBsHXFgAADI
        WPIs9xAoKP67mb39e1frBwYAgKztXeuvOSO1RPIKF/kBABStZ6DxM6klkkd6+5MztAMBAEDe+gaTU6Se
        SJbpHXCfTwf+/sgDAABAQT54fdD9e6kpkkX8LX3TQb84YvAAABSrv/EytxLOML2Djbnq4AEAKFjfQGOO
        1BXpZHoGk2O0gQMAUB7JX0ptkU7Ev6ySPvvfpQ8bAIDSeGPPHvd7Ul+k3fT2N65XhgwAQOn0DTQmS32R
        diKf+q+NHDAAACUV9Q24fys1RlqNv9KSMlwAAMqrf+8aqTHSSvr6k3HqYAEAKLmeweTrUmdkLHHOfaJn
        oPGKNlQAACpge9plH5daI6ONv7SiMkwAACqjZyA5UWqNjCZ+Y0oHxz3+AQBV15V22sek3sjh0jeQfE0Z
        IgAA1dOfHC31Rg6X3oHkKXWIAABUzt61Um/kUOnd4/5EHyAAANXU/Sv3x1Jz5GDpG2zcog0PAIAKu0lq
        jmj55S/d76RDenfE0AAAqLqB7m73Kak7MjJc+AcAEKruweSvpe7IyPQO7l2qDQ0AgADcI3VHhsffPjEd
        zgcjhgUAQCje9291S+2RZvjuPwAgdD39yV9I7ZFm0sHMGjkoAABC0jfYmCa1R5pJB9M3clAAAARmp9Qe
        8Xl9j/sjZUgAAASn+1fun0n9kd6B5G+0IQEAEJq+/uR4qT/S29+YoQ0JAIDQ8DmAYekdSDZpQwIAIDR9
        /ckzUn+2I/f+5/v/AAAr3k+772NSg3bTO+A+rwwHAIBg+Q+/Sw3aDRcAAgDYk3xVatBu0kFcdOBgAAAI
        V99g4ztSg3aTDuLmkYMBACBkPQONn0gN2k1v/95l2nAAAAjW4N6lUoN20zuQrFeHAwBAoPoGknVSg3aT
        DqJr5GAAAAjaYGOb1KDdpIN4+4DBAAAQsJ6BxltSg3aTDuLXIwcDAEDg3pUatJt0CL8dMRQAAEL3G6lB
        u0mHkIwYCgAAoUukBu1GGQoAAMGTGrQbbSgAAIROatButKEAABA6qUG70YYCAEDopAbtRhsKAAChkxq0
        G20oAACETmrQbrShAAAQOqlBu9GGAgBA6KQG7UYbCgAAoZMatBttKAAAhE5q0G60oQAAEDqpQbvRhgIA
        QOikBu1GGwoAAKGTGrQbbSgAAIROatButKEAABA6qUG70YYCAEDopAbtRhsKAAChkxq0G20oAACETmrQ
        brShAAAQOqlBu9GGAgBA6KQG7UYbCgAAoZMatBttKAAAhE5q0G60oQAAEDqpQbvRhgIAQOikBu1GGwoA
        AKGTGrQbbSgAAIROatButKEAABA6qUG70YYCAEDopAbtRhsKAAChkxq0G20oAACETmrQbrShAAAQOqlB
        u9GGAgBA6KQG7UYbCgAAoZMatBttKAAAhE5q0G60oQAAEDqpQbvRhgIAQOikBu1GGwoAAKGTGrQbbSgA
        AIROatButKEAABA6qUG70YYCAEDopAbtRhsKAAChkxq0G20oAACETmrQbrShAAAQOqlBu9GGAgBA6KQG
        7UYbCgAAoZMatBttKAAAhE5q0G60oQAAEDqpQbvRhgIAQOikBu1GGwoA27p2N9yTrybu3mcTN2tt3V29
        PHaXLIzdd+bF7uy5sfvmrZE7fuZ+/q/93/P/n/9n/D/r/x3/7/r/hv9vaX8GUDSpQbvRhgLAlh1vN9yq
        lxM3/fG6u2BB7I6bHrlv3NI5fkG4cWXslj+fuO1v6T8DkDepQbvRhgIgfL6IF25M3PhFsRvX4cI/FP9n
        XZb+mQs3sQygWFKDdqMNBUCYevob7uGXEjf5gdidMEMv6Dz5n8H/LI+kP5P/2bSfGciK1KDdaEMBEBZf
        riteTNwF82O1iMvg2/Nid++WxHW/o/8OQKdJDdqNNhQAYfBlumhT4s6ZW97iH8n/rIs2swgge1KDdqMN
        BUD1PbUzcRcuqE7xj3T+3bFbuyNRfzegE6QG7UYbCoDqevnNhpuyqt7xT/IX4bjUdSti99KbLALoPKlB
        u9GGAqCaHnohcafM0cu0yvzvtCL93bTfGWiV1KDdaEMBUC3+/XL/HX7/jFkr0BD4382/svHaHn0GwFhJ
        DdqNNhQA1fHs64m76J7qvtc/Vv539b+zNgtgLKQG7UYbCoBq8B/0OzXAl/wPx78lsI4PCKJNUoN2ow0F
        QPmt3p64k2frBWnBSbMit3IbSwBaJzVoN9pQAJTbg88npbiSX9H8zYiWbWUJQGukBu1GGwqA8vKFF8JX
        /DrFz+J+lgC0QGrQbrShACin1S/zzF8zLp3JynQ22syAg5EatBttKADKx99b37/vrRUgInfSTD4YiLGR
        GrQbbSgAysV/7S3EC/x0mv9GBF8RxGhJDdqNNhQA5eEv8nPxQjvf82+Xv04AFwvCaEgN2o02FADlcfOq
        ulp0OLhpa+rqLIHhpAbtRhsKgHJ4+KUk6Mv7ZsXPzN8XQZsp0CQ1aDfaUAAUb9ubDd73b4Ofnb8zojZb
        wJMatBttKACKd/0K3vdv1w2PxOpsAU9q0G60oQAo1todvPTfCf4iQXw1EAcjNWg32lAAFMd/6v/8u3n2
        3ynfmRfvm6k2a9gmNWg32lAAFGfhpkQtMrRu8WZeBcCBpAbtRhsKgGJ09zfcuXfy7L/TzpnLqwA4kNSg
        3WhDAVCM+57l2X9WuGsgRpIatBttKADy15P6Du/9Z+Zb82LX06/PHjZJDdqNNhQA+fMX/dGKC52zchuv
        AuBDUoN2ow0FQP6uXs6z/6xNfpDrAuBDUoN2ow0FQL66djfciTP10kLnnDAjctvf0o8B7JEatBttKADy
        tXAjL//nZRFfCYSQGrQbbSgA8jV+ES//5+WyxbwNgP2kBu1GGwqA/PiX/8dN18sKnTduRuReSWeuHQvY
        IjVoN9pQAOTnoRd5+T9v/hsX2rGALVKDdqMNBUB+pq6uqyWF7Nyypq4eC9giNWg32lAA5Oe7XPwnd+fP
        53MAYAFgAQAK5N//57a/+fO3CeZzAJAatBttKADy8eSrvP9flPU7+RyAdVKDdqMNBUA+lm5hASjKfdwc
        yDypQbvRhgIgH7PW8gHAosx+gg8CWic1aDfaUADkg+v/F2fyA3wQ0DqpQbvRhgIgHxcvZAEoyqXp7LVj
        AjukBu1GGwqAfHx7HgtAUfzXL7VjAjukBu1GGwqAfJx1BwtAUc5OZ68dE9ghNWg32lAA5OOUOXo5IXt+
        9toxgR1Sg3ajDQVAPo6fqZcTsnfCDBYA66QG7UYbCoB8sAAUhwUAUoN2ow0FQD54C6A4vAUAqUG70YYC
        IB98CLA4fAgQUoN2ow0FQD74GmBx+BogpAbtRhsKgHxwIaDicCEgSA3ajTYUAPngUsDF4VLAkBq0G20o
        APLBzYCKw82AIDVoN9pQAOSD2wEXh9sBQ2rQbrShAMjHk6+yABRl/U4WAOukBu1GGwqAfLyyu+GOU8oJ
        2TpuerRv9toxgR1Sg3ajDQVAfvzX0bSSQnbOn88HAMECwAIAFGzqaj4ImLdbHuMDgGABYAEACvbQi3wO
        IG+PvMT7/2ABYAEACta1u+HGTdeLCp03bgbv/2M/qUG70YYCIF+XLeJzAHmZsIT3/7Gf1KDdaEMBkK+F
        G3kbIC+LN/PyP/aTGrQbbSgA8uXfBjhxpl5Y6JwTZkRu+1v6MYA9UoN2ow0FQP78tem10kLnTH6Ql//x
        IalBu9GGAiB//pPpWmmhc1Zu4+V/fEhq0G60oQDIX0/qO1wUKDPfmhe7nn599rBJatButKEAKMZ9z/Iq
        QFbu5+Y/GEFq0G60oQAoRnf6DPXcO3kVoNPOmRvvm602c9glNWg32lAAFGfRZl4F6LQlW3j2jwNJDdqN
        NhQAxfHPVP3NarQiw9hdkM6SZ//QSA3ajTYUAMV68tVk3y1rtULD6PkZrtvBs3/opAbtRhsKgOL9+GFe
        BWjXjY/wvX8cnNSg3WhDAVC8l99suFPm6MWGw/Oz8zPUZgt4UoN2ow0FQDk8/FLijlPKDYfmZ7biBV76
        x6FJDdqNNhQA5TF1dV0tORzcLY/V1VkCw0kN2o02FADl0f1Ow128kM8DjNZF98TutT36LIHhpAbtRhsK
        gHJ59vXEncrnAQ7rtDmxey6dlTZDYCSpQbvRhgKgfJ7ambiTZ+vFh2jfbNa/Rvlj9KQG7UYbClBG/pnd
        wo2Jm7Kq7q5aHrtLF6YWxW7S/fG+v7dwU+KeeyPsAli9Pdl3T3utAC07fmbkHuVOfxgjqUG70YYClMX2
        txrujvX1Md0lz/+zc9N/x/+72n+z6vxNbbhI0If8LO5/jvLH2EkN2o02FKBo/tKtdz1dd9+8VX/QHw3/
        PfC7f14P8jKwDz7PKwGef+bPXf7QKqlBu9GGAhTJv4zvX9rXHvBbMT79b70Q4FsD/u0Ay58JOGlW5Fa+
        TPmjdVKDdqMNBSjKhtcS97c/6/xX3s64Pd7339b+zCpbvzPZ98l37XcO2em3xft+d20mwGhJDdqNNhSg
        CL6gs7z0rX87wd9kR/uzq8y/YnKJoesE+O/5+69FarMAxkJq0G60oQB5y7r8m0JdAvzFgm59oh70hwP9
        5X39tz24yA86RWrQbrShAHnKq/ybQl0CvIdezHeWefG/04r0d9N+Z6BVUoN2ow0FyEve5d8U8hLgv/7o
        nymH8GqAf9Z/3YrYbeOufsiA1KDdaEMB8lBU+TeFvAR4/kNy/v1y7XevgvPnx+6JHTzrR3akBu1GGwqQ
        taLLvyn0JcBfA2Hx5sSde2d1FoHz0p91yZYkyOs3oFykBu1GGwqQpbKUf1PoS4DXk5apfw/9gvRZtTaD
        MvBXcLzXF/87+u8AdJrUoN1oQwGyUrbyb7KwBHg9qZXbEjf5gdidOFOfRZ78zzD5wXjfz+R/Nu1nBrIi
        NWg32lCALJS1/JusLAFNXbsbbtHmxF22OHbjcryssP+z/J/p35rwP4P2swF5kBq0G20oQKeVvfybrC0B
        TTvebrhVLydu+uN1d8GCuOPfIDh7buxuXBm75c8nwd6kCdUjNWg32lCATqpK+TdZXQKGeyV9Zv7UzsTd
        tzVxs9bW971l4K82+N27431l7o+nvxGP5//a/z3///l/xv+z/t/x/67/JoL/b2l/BlA0qUG70YYCdErV
        yr+JJQAIn9Sg3WhDATqhquXfxBIAhE1q0G60oQDtqnr5N7EEAOGSGrQbbShAO0Ip/yaWAJt27mm4BzYl
        7ur5kTv7psiNm1RzR44fckdNGHLHpn99xk8id/WCyC37eeK6uFRxJUkN2o02FKBVoZV/E0uAHRvTc/iq
        eZE76vIh938vHh2/GEy8I3IbXuEcqRKpQbvRhgK0ItTyb2IJCNur6TP+G5bG+8pcK/nR+NKlQ27yPRHf
        fKgIqUG70YYCjFXo5d/EEhCmTT2JO/XHkVrqrTjhmprbwI2MSk9q0G60oQBjYaX8m1gCwrK+K3HHXFFT
        i7wdR08cco8+X1f/TJSD1KDdaEMBRsta+TexBITBP0v/6x92vvybvjJhaN8HCbU/G8WTGrQbbSjAaFgt
        /yaWgGrLuvyb/GcKFq/nlYAykhq0G20owOFYL/8mloBqyqv8m/yHA+98jCWgbKQG7UYbCnAolP9HsQRU
        S97l3/TFS4bcrIdj9WdCMaQG7UYbCnAwlL+OJaAaiir/4fxXDbWfDfmTGrQbbSiAhvI/NJaAcitD+Tf5
        awV09+s/J/IjNWg32lCAkSj/0WEJKKcylX+Tv3Jg9zv6z4t8SA3ajTYUYDjKf2xYAsqljOXfdNHs2r4r
        EGo/N7InNWg32lCAJsq/NSwB5fB0egzKWv5N502ruVfe0n9+ZEtq0G60oQAe5d8eloBiVaH8m/ydBbf9
        gnMlb1KDdqMNBaD8O4MloBhVKv+mU2+I3ItvcK7kSWrQbrShwDbKv7NYAvJVxfJvOulHNfdsL+dKXqQG
        7UYbCuyi/LPBEpCPKpd/0zcm1dwzOzlX8iA1aDfaUGAT5Z8tloBshVD+TV9Lf48nuzhXsiY1aDfaUGAP
        5Z8PloBshFT+TV+dWHNrXuD+AVmSGrQbbSiwhfLPF0tAZ4VY/k1HTRhyD23hXMmK1KDdaEOBHZR/MVgC
        OiPk8m/68vghd+8GXgnIgtSg3WhDgQ2Uf7FYAtpjofyb/O2E736CJaDTpAbtRhsKwkf5lwNLQGsslX+T
        v53wnJUsAZ0kNWg32lAQNsq/XFgCxsZi+Q938/3cTrhTpAbtRhsKwkX5lxNLwOhYL/+mG5ayBHSC1KDd
        aENBmCj/cmMJODTK/6MmzY9cd78+K4yO1KDdaENBeCj/amAJ0FH+ustvj9xr3E64ZVKDdqMNBWGh/KuF
        JeCjKP9Du3Bmze14W58dDk1q0G60oSAclH81sQTsR/mPzrlTa67rLX2GODipQbvRhoIwUP7VZn0JoPzH
        5oyfRG4btxMeE6lBu9GGguqj/MNgdQmg/Ftz0rU199wuloDRkhq0G20oqDbKPyzWlgDKvz3jJtXc5m6W
        gNGQGrQbbSioLso/TFaWAMq/M465oubWczvhw5IatBttKKgmyj9soS8BlH9n/dUPam7ddi4dfChSg3aj
        DQXVQ/nbEOoSQPln4+iJQ271CywBByM1aDfaUFAtlL8toS0BlH+2vjJhyD24mbcDNFKDdqMNBdVB+dsU
        yhJA+efjyPFDbskGXgkYSWrQbrShoBoof9uqvgRQ/vn60qVDbt7jLAHDSQ3ajTYUlB/lD6+qSwDlX4wv
        XjLkZj/CnQSbpAbtRhsKyo3yx3BVWwIo/+JxO+H9pAbtRhsKyovyh6YqSwDlXx7X3BO5HuO3E5YatBtt
        KCgnyh+HUvYlgPIvn4lzI9f9jn68LJAatBttKCgfyh+jUdYlgPIvr8tui9xre/TjFjqpQbvRhoJyofwx
        FmVbAij/8jt/Rs3teFs/fiGTGrQbbSgoD8ofrSjLEkD5V8c5N0eu6039OIZKatButKGgHCh/tKPoJYDy
        r57Tbojci2+U93MknSY1aDfaUFA8yh+dUNQSQPlX18nX1tzWXhtLgNSg3WhDQbEof3RS3kuAL39/O1qt
        XFANx02quY3p45B2fEMiNWg32lBQHMofWchrCaD8w+FfwXnqlbCXAKlBu9GGgmJQ/shS1ksA5R+er06s
        uTUvhnv/AKlBu9GGgvxR/shDVksA5R+uoy4fciu2hPlKgNSg3WhDQb4of+Sp00sA5R++L48fcvc9Hd4r
        AVKDdqMNBfmh/FGETi0BlL8d/nbCC54IawmQGrQbbSjIB+WPIrW7BFD+9vglYO5j4SwBUoN2ow0F2aP8
        UQatLgGUv11fvGTIzVwRxu2EpQbtRhsKskX5o0zGugRQ/vBuWFr9JUBq0G60oSA7lD/KaLRLAOWP4a5e
        ELnufv1cqQKpQbvRhoJsUP4os8MtAZQ/NN+7I10C3tHPmbKTGrQbbSjoPMofVXCwJYDyx6FcNKvmXt3z
        0XOmCqQG7UYbCjqL8keVjFwCKH+MxnlTa67rrQMf/8pMatButKGgcyh/VFFzCaD8MRZn/CRy2yp0O2Gp
        QbvRhoLOoPxRZSfPjtwxV1P+GJuTr6u553ZVYwmQGrQbbShoH+WPEBw7tea+/D2WAIzNiT+quWd7y78E
        SA3ajTYUtIfyR0hYAtCKYyfV3DM7y70ESA3ajTYUtI7yR4hYAtCKr/2w5p7sKu8SIDVoN9pQ0BrKHyFj
        CUArjp445Na8WM77B0gN2o02FIwd5Q8LWALQiqMmDLmHNpfvlQCpQbvRhoKxofxhCUsAWnHk+CF374Zy
        vRIgNWg32lAwepQ/LGIJQCv87YTnrS3PEiA1aDfaUDA6lD8sYwlAK/zthG9dWY47CUoN2o02FBwe5Q+w
        BKB1ZbidsNSg3WhDwaFR/sCHWALQKr8E9BR4O2GpQbvRhoKDo/yBA7EEoFWT5keuu6AlQGrQbrShQEf5
        AwfHEoBWXX575F4r4HbCUoN2ow0FB6L8gcNjCUCrLphZczve1h9/syI1aDfaUPBRlD8weiwBaNV56bnT
        9Zb+OJwFqUG70YaCD1H+wNixBKBVf/vTyG37RT5XDZQatBttKNiP8gdaxxKAVp10bc0915f9EiA1aDfa
        UED5A53AEoBWjZtUc5u6s10CpAbtRhuKdZQ/0DksAWjV16+quadfzW4JkBq0G20ollH+QOexBKBVf/WD
        mlv3cjb3D5AatBttKFZR/kB2WALQqr/43pBb9ULnlwCpQbvRhmIR5Q9kjyUArfrKhCH34ObOvh0gNWg3
        2lCsofyB/LAEoFVHjh9yizd07pUAqUG70YZiCeUP5I8lAK360qVD7q7HO7MESA3ajTYUKyh/oDgsAWjV
        Fy8ZcrMfaf92wlKDdqMNxQLKHygeSwDa4W8nrD2+j5bUoN1oQwkd5Q+UB0sA2nHdotj1tHg7YalBu9GG
        EjLKHygflgC044q7Itf9jv6YfyhSg3ajDSVUlD9QXiwBaMdFs2tu5x79sf9gpAbtRhtKiCh/oPxYAtCO
        86bV3CtjuJ2w1KDdaEMJDeUPVAdLANpxzpTIbX9T74KRpAbtRhtKSCh/oHpYAtCO026I3Eu/OPxVA6UG
        7UYbSigof6C6WALQjpN+VHNbew+9BEgN2o02lBBQ/kD1sQSgHeMm1dym7oMvAVKDdqMNpeoofyAcLAFo
        xzFX1Nz6Ln0JkBq0G20oVUb5A+FhCUA7vjqx5tYotxOWGrSbkQOpMsofCBdLANpx1IQht2LLR18JkBq0
        m+HDqDLKHwgfSwDa8eXxQ+7epz98JUBq0G6Gl2hVUf6AHSwBaIe/nfD8tfuXAKlBuxlZplVD+QP2sASg
        Hf52wrc9WmcB0Eq1Kih/wC6WALTDLwFSg3ajFWsVUP4AWALQDqlBu9HKtewofwBNLAFoldSg3WgFW2aU
        P4CRWALQCqlBu9FKtqwofwAHwxKAsZIatButaMuI8gdwOCwBGAupQbvRyrZsKH8Ao8USgNGSGrQbrXDL
        hPIHMFYsARgNqUG70Uq3LCh/AK1iCcDhSA3ajVa8ZUD5A2gXSwAORWrQbrTyLRrlD6BTWAJwMFKDdqMV
        cJEofwCdxhIAjdSg3WglXBTKH0BWWAIwktSg3WhFXATKH0DWWAIwnNSg3WhlnDfKH0BeWALQJDVoN1oh
        54nyB5A3lgB4UoN2o5VyXih/AEVhCYDUoN1oxZwHyh9A0VgCbJMatButnLNG+QMoC5YAu6QG7UYr6CxR
        /gDKhiXAJqlBu9FKOiuUP4CyYgmwR2rQbrSizgLlD6DsWAJskRq0G62sO43yB1AVLAF2SA3ajVbYnUT5
        A6galgAbpAbtRivtTqH8AVQVS0D4pAbtRivuTqD8AVQdS0DYpAbtRivvdlH+AELBEhAuqUG70Qq8HZQ/
        gNCwBIRJatButBJvFeUPIFQsAeGRGrQbrchbQfkDCB1LQFikBu1GK/OxovwBWMESEA6pQbvRCn0sKH8A
        1rAEhEFq0G60Uh8tyh+AVSwB1Sc1aDdasY8G5Q/AOpaAapMatBut3A+H8geA/VgCqktq0G60gj8Uyh8A
        PooloJqkBu1GK/mDofwBQMcSUD1Sg3ajFb2G8geAQ2MJqBapQbvRyn4kyh8ARocloDqkBu1GK/zhKH8A
        GBuWgGqQGrQbrfSbKH8AaA1LQPlJDdqNVvwe5Q8A7WEJKDepQbuh/AEgOywB5SU1aDeUPwBkiyWgnKQG
        7YbyB4DssQSUj9Sg3VD+AJAPloBykRq0G8ofAPLDElAeUoN2Q/kDQL5YAspBatBuKH8AyJ9fAo5kCSiU
        1KDdaCcmACB7vBJQLKlBu9FOSgBAPlgCiiM1aDfaCQkAyA9LQDGkBu1GOxkBAPliCcif1KDdaCciACB/
        LAH5khq0G+0kBAAUgyUgP1KDdqOdgACA4rAE5ENq0G60kw8AUCyWgOxJDdqNduIBAIrHEpAtqUG70U46
        AEA5sARkR2rQbrQTDgBQHiwB2ZAatBvtZAMAlAtLQOdJDdqNdqIBAMqHJaCzpAbtRjvJAADlxBLQOVKD
        dqOdYACA8mIJ6AypQbvRTi4AQLmxBLRPatButBMLAFB+LAHtkRq0G+2kAgBUA0tA66QG7UY7oQAA1cES
        0BqpQbvRTiYAQLWwBIyd1KDdaCcSAKB6WALGRmrQbrSTCABQTSwBoyc1aDfaCQQAqC6WgNGRGrQb7eQB
        AFQbS8DhSQ3ajXbiAACqjyXg0KQG7UY7aQAAYWAJODipQbvRThgAQDhYAnRSg3ajnSwAgLCwBBxIatBu
        tBMFABAeloCPkhq0G+0kAQCEiSXgQ1KDdqOdIACAcLEE7Cc1aDfayQEACBtLAAsACwAAGGV9CZAatBvt
        pAAA2GB5CZAatBvthAAA2GF1CZAatBvtZAAA2GJxCZAatBvtRAAA2GNtCZAatBvtJAAA2GRpCZAatBvt
        BAAA2GVlCZAatBvt4AMAbLOwBEgN2o124AEACH0JkBq0G+2gAwDghbwESA3ajXbAAQBoCnUJkBq0G+1g
        AwAwXIhLgNSg3WgHGgCAkUJbAqQG7UY7yAAAaEJaAqQG7UY7wAAAHEwoS4DUoN1oBxcAgEMJYQmQGrQb
        7cACAHA4VV8CpAbtRjuoAACMRpWXAKlBu9EOKAAAo1XVJUBq0G60gwkAwFhUcQmQGrQb7UACADBWVVsC
        pAbtRjuIAAC0okpLgNSg3Yybrh9EAABaUYUl4MjxLABHnDhTP4AAALSq7EvAn18+tFdq0G5Onh3t1Q4e
        AADtKPMS8LUf1hpSg3Zz+m1RQztwAAC0yy8BR5ZwCfjGlUOJ1KDdnHdnVNMOGgAAnVDGVwJOujb6rdSg
        3Vy4oP6edsAAAOiUsi0BZ02JBqUG7eayxdGb2sECAKCTyvR2wEWzol6pQbu5cnl9q3agAADotLK8EnD5
        HfHPpQbt5toHo2XaQQIAIAtlWAKumhfdIzVoN1NWR9doBwgAgKwU/XbAlOW1S6UG7Wbm4/UvagcHAIAs
        FflKwIKnoj+RGrSbpc59gosBAQCKUMQS8PUrhvY65z4mNWg758+P39cODAAAWcv77YBzbo7elfojVyyr
        v6gdFAAA8pDnKwE/vDPiGwDN/HRVfap2QAAAyEteS8DU+6Irpf7IzCfj/6odDAAA8pT1EuBvA7zy5+5f
        S/0Rn7PviGLtYAAAkKcsl4DTb6xxD4CRuXJZ/JJ2IAAAyFtWS8BV82obpPZIMzevjq7SDgIAAEXIYgmY
        tyY+S2qPNHPPBvcHXA8AAFAmnVwCjp9ca+za5T4ttUeG53tLo13aAQAAoCidWgIuvz3aLnVHRmbKmuha
        bfgAABSpE0vA3Mfib0ndkZFZus595vTb4oY2fAAAitTOEnDGjVHc5dwnpe6IlqsfiLdqgwcAoGitLgHX
        LamvkZojB8vPnkqOPk4ZOgAAZeCXgLHcO+DPLx9yD2x1/11qjhwq4xfVB7WhAwBQBmN5JWDi3OgNqTdy
        uExdE31fGzgAAGUxmiXgS5cOucUb4lOl3sjh4u+T/O158ZA2cAAAyuJwbwdcfGv8Hvf+H2OmrY6naMMG
        AKBMDvZKgH/2P/+JxiVSa2S0STemj3/37vg32rABACgTbQmYcFv0ru8yqTUylkx7PJqsDRoAgLIZ/nbA
        n40fcsufic+UOiOt5JJF9X5t0AAAlE3zlYDv3xG9LjVGWs3Ux+pHjZuuDxoAgLI59qeRW77J/WepMdJO
        rlwWb9SGDABA2Vy9JOaqf53K7avc3z/rjjjWBg0AQFmccksUc8vfDmf62ugHXCIYAFBWx94cuflPxOdI
        bZFO5qr76y9rQwcAoEjHpq5YGG2VuiKdzpzH3O9/6674t9rwAQAoypmzoqFf/tL9jtQVySI3PZocfeLs
        aK92AAAAyNtxU2pu6TPuv0lNkSxzwyPxNO0gAACQJ/++/8xH6tdLPZE8MvmB+jbtYAAAkItpkbt+WX2j
        1BLJKyu73acm3lvfrR4UAAAydsld8Ttdzn1SaonkmeXr3N+7eGH8a+3AAACQlbNujT7o6nefkToiReTO
        de4Pv313PKQdIAAAOu20mVG8/nX3T6WGSJGZt9796Tlzo7p2oAAA6JRvTo+SDb3u81I/pAyZ81T9K+fM
        jVkCAACZOGVGlKx9xf0nqR1SptyzxX3hggVcKAgA0FmnzYqidT3uc1I3pIxZtMX980sX1t/TDiAAAGN1
        9pz4gy1d7p9IzZAy5+EN7g8mLuUrggCA9lw6P97z6oD7rNQLqULW7XKfvuaB+CXuIAgAGKtjp0buumXx
        pq3O/R2pFVK1TFtdv+6bt3LvAADA6Bw/LXKzV9d/JDVCqpzbn6r/nwsWRB9oBxoAgCb/Yb8lG93/lPog
        IWTpOveZ61bUNx03XT/oAADD0mf919wXb9u92/2u1AYJLbPX1r937l1xpJ4AAABz0mf99flPx+dKTZCQ
        8+DT7rM3Plp/9OTZNfVkAACEb1xq8v3x+l273KelHoiV3LGu/r8uX1J/WzsxAABhOmFG5K5/OHljzcvu
        v0gdEKuZ+Vh01YULot9oJwoAIAzjpqfP+B+I313xQnyWPPwTcsQRzrmP3/Zk4+oJi+v/TztxAADVNC59
        xn/Vsvp7S7c2LvGP9fKwT8iBmb0uOf2H99V/6bdF7WQCAJTfSbMid+2K+u6Hno/PTIv/Y/IQT8jhM+Px
        +p9Nuj/eePptcUM7uQAA5XPRPXF95uPJ+rU73P+Qh3NCWsvdq93vTVkVXf39e+Pev+GqggBQOufOjRs/
        eijevnBT/O0u5z4pD9+EdC5LH3O/P2VN/fuTltefP+fOiOsJAEABjp8ZuQvviX574yPx0ws2xmfzVT6S
        e277efyFmx6Nb7pyWf35C+6O3j95Nq8QAECnnfmzuHHZ4vq716+In7nticaVj29z/0oehgkpR/wHTW5Z
        Vf/fU1ZFV173cLJk0v31LROWxL+4cEH83nl3xUOn/yxO/NsILAoArPOXZvePh6fOifaeeUeU+MfI8Yvq
        gz+4L+675qH6Mzc8Ei+cva5+6YPPuT/lQ3yEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEII
        IYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIIYQQQgghhBBCCCGEEEIIyT5HHPH/ASdyge86yNGa
        AAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="timer.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>99, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>50</value>
  </metadata>
</root>