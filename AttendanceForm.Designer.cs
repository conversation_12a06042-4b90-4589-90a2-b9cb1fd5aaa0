namespace EmployeeManagementSystem
{
    partial class AttendanceForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            groupBoxAttendance = new GroupBox();
            lblCurrentTime = new Label();
            lblCurrentDate = new Label();
            numericUpDownOvertimeHours = new NumericUpDown();
            btnCheckOut = new Button();
            btnCheckIn = new Button();
            btnManualEntry = new Button();
            txtNotes = new TextBox();
            lblOvertimeInput = new Label();
            lblNotes = new Label();
            cmbAttendanceStatus = new ComboBox();
            lblAttendanceStatus = new Label();
            cmbWorkPeriod = new ComboBox();
            lblWorkPeriod = new Label();
            cmbEmployee = new ComboBox();
            lblEmployee = new Label();
            groupBoxTodayAttendance = new GroupBox();
            lblOvertimeHours = new Label();
            lblWorkingHours = new Label();
            lblStatus = new Label();
            lblCheckOutTime = new Label();
            lblCheckInTime = new Label();
            lblEmployeeName = new Label();
            dataGridViewAttendance = new DataGridView();
            groupBoxAttendanceList = new GroupBox();
            btnRefresh = new Button();
            btnDelete = new Button();
            btnSearch = new Button();
            txtSearch = new TextBox();
            dateTimePickerEnd = new DateTimePicker();
            dateTimePickerStart = new DateTimePicker();
            lblEndDate = new Label();
            lblStartDate = new Label();
            timer1 = new System.Windows.Forms.Timer(components);
            groupBoxAttendance.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDownOvertimeHours).BeginInit();
            groupBoxTodayAttendance.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewAttendance).BeginInit();
            groupBoxAttendanceList.SuspendLayout();
            SuspendLayout();
            // 
            // groupBoxAttendance
            // 
            groupBoxAttendance.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxAttendance.Controls.Add(lblCurrentTime);
            groupBoxAttendance.Controls.Add(lblCurrentDate);
            groupBoxAttendance.Controls.Add(numericUpDownOvertimeHours);
            groupBoxAttendance.Controls.Add(btnCheckOut);
            groupBoxAttendance.Controls.Add(btnCheckIn);
            groupBoxAttendance.Controls.Add(btnManualEntry);
            groupBoxAttendance.Controls.Add(txtNotes);
            groupBoxAttendance.Controls.Add(lblOvertimeInput);
            groupBoxAttendance.Controls.Add(lblNotes);
            groupBoxAttendance.Controls.Add(cmbAttendanceStatus);
            groupBoxAttendance.Controls.Add(lblAttendanceStatus);
            groupBoxAttendance.Controls.Add(cmbWorkPeriod);
            groupBoxAttendance.Controls.Add(lblWorkPeriod);
            groupBoxAttendance.Controls.Add(cmbEmployee);
            groupBoxAttendance.Controls.Add(lblEmployee);
            groupBoxAttendance.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxAttendance.Location = new Point(12, 12);
            groupBoxAttendance.Name = "groupBoxAttendance";
            groupBoxAttendance.RightToLeft = RightToLeft.Yes;
            groupBoxAttendance.Size = new Size(1160, 220);
            groupBoxAttendance.TabIndex = 0;
            groupBoxAttendance.TabStop = false;
            groupBoxAttendance.Text = "تسجيل الحضور والانصراف";
            // 
            // lblCurrentTime
            // 
            lblCurrentTime.AutoSize = true;
            lblCurrentTime.Font = new Font("Segoe UI", 14F, FontStyle.Bold);
            lblCurrentTime.ForeColor = Color.Blue;
            lblCurrentTime.Location = new Point(40, 30);
            lblCurrentTime.Name = "lblCurrentTime";
            lblCurrentTime.Size = new Size(88, 25);
            lblCurrentTime.TabIndex = 7;
            lblCurrentTime.Text = "00:00:00";
            // 
            // lblCurrentDate
            // 
            lblCurrentDate.AutoSize = true;
            lblCurrentDate.Font = new Font("Segoe UI", 12F, FontStyle.Bold);
            lblCurrentDate.Location = new Point(40, 60);
            lblCurrentDate.Name = "lblCurrentDate";
            lblCurrentDate.Size = new Size(96, 21);
            lblCurrentDate.TabIndex = 6;
            lblCurrentDate.Text = "2024/01/01";
            // 
            // numericUpDownOvertimeHours
            // 
            numericUpDownOvertimeHours.DecimalPlaces = 1;
            numericUpDownOvertimeHours.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            numericUpDownOvertimeHours.Increment = new decimal(new int[] { 5, 0, 0, 65536 });
            numericUpDownOvertimeHours.Location = new Point(355, 176);
            numericUpDownOvertimeHours.Maximum = new decimal(new int[] { 12, 0, 0, 0 });
            numericUpDownOvertimeHours.Name = "numericUpDownOvertimeHours";
            numericUpDownOvertimeHours.Size = new Size(80, 32);
            numericUpDownOvertimeHours.TabIndex = 9;
            numericUpDownOvertimeHours.ValueChanged += numericUpDownOvertimeHours_ValueChanged;
            // 
            // btnCheckOut
            // 
            btnCheckOut.BackColor = Color.FromArgb(220, 53, 69);
            btnCheckOut.FlatStyle = FlatStyle.Flat;
            btnCheckOut.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnCheckOut.ForeColor = Color.White;
            btnCheckOut.Location = new Point(567, 172);
            btnCheckOut.Name = "btnCheckOut";
            btnCheckOut.Size = new Size(150, 40);
            btnCheckOut.TabIndex = 5;
            btnCheckOut.Text = "تسجيل الانصراف";
            btnCheckOut.UseVisualStyleBackColor = false;
            btnCheckOut.Click += btnCheckOut_Click;
            // 
            // btnCheckIn
            // 
            btnCheckIn.BackColor = Color.FromArgb(40, 167, 69);
            btnCheckIn.FlatStyle = FlatStyle.Flat;
            btnCheckIn.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnCheckIn.ForeColor = Color.White;
            btnCheckIn.Location = new Point(879, 173);
            btnCheckIn.Name = "btnCheckIn";
            btnCheckIn.Size = new Size(150, 40);
            btnCheckIn.TabIndex = 4;
            btnCheckIn.Text = "تسجيل الحضور";
            btnCheckIn.UseVisualStyleBackColor = false;
            btnCheckIn.Click += btnCheckIn_Click;
            // 
            // btnManualEntry
            // 
            btnManualEntry.BackColor = Color.FromArgb(255, 193, 7);
            btnManualEntry.FlatStyle = FlatStyle.Flat;
            btnManualEntry.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnManualEntry.ForeColor = Color.Black;
            btnManualEntry.Location = new Point(723, 173);
            btnManualEntry.Name = "btnManualEntry";
            btnManualEntry.Size = new Size(150, 40);
            btnManualEntry.TabIndex = 5;
            btnManualEntry.Text = "التسجيل اليدوي";
            btnManualEntry.UseVisualStyleBackColor = false;
            btnManualEntry.Click += btnManualEntry_Click;
            // 
            // txtNotes
            // 
            txtNotes.Font = new Font("Cairo", 9.75F);
            txtNotes.Location = new Point(567, 127);
            txtNotes.Multiline = true;
            txtNotes.Name = "txtNotes";
            txtNotes.Size = new Size(390, 40);
            txtNotes.TabIndex = 3;
            // 
            // lblOvertimeInput
            // 
            lblOvertimeInput.AutoSize = true;
            lblOvertimeInput.Font = new Font("Cairo SemiBold", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblOvertimeInput.Location = new Point(439, 179);
            lblOvertimeInput.Name = "lblOvertimeInput";
            lblOvertimeInput.Size = new Size(92, 24);
            lblOvertimeInput.TabIndex = 8;
            lblOvertimeInput.Text = "ساعات إضافية:";
            // 
            // lblNotes
            // 
            lblNotes.AutoSize = true;
            lblNotes.Font = new Font("Cairo SemiBold", 9.75F, FontStyle.Bold);
            lblNotes.Location = new Point(963, 132);
            lblNotes.Name = "lblNotes";
            lblNotes.Size = new Size(60, 24);
            lblNotes.TabIndex = 2;
            lblNotes.Text = "ملاحظات:";
            // 
            // cmbAttendanceStatus
            // 
            cmbAttendanceStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbAttendanceStatus.Font = new Font("Cairo", 9.75F);
            cmbAttendanceStatus.FormattingEnabled = true;
            cmbAttendanceStatus.Items.AddRange(new object[] { "حاضر", "غائب", "متأخر" });
            cmbAttendanceStatus.Location = new Point(567, 92);
            cmbAttendanceStatus.Name = "cmbAttendanceStatus";
            cmbAttendanceStatus.Size = new Size(390, 32);
            cmbAttendanceStatus.TabIndex = 7;
            cmbAttendanceStatus.SelectedIndexChanged += cmbAttendanceStatus_SelectedIndexChanged;
            // 
            // lblAttendanceStatus
            // 
            lblAttendanceStatus.AutoSize = true;
            lblAttendanceStatus.Font = new Font("Cairo SemiBold", 9.75F, FontStyle.Bold);
            lblAttendanceStatus.Location = new Point(963, 95);
            lblAttendanceStatus.Name = "lblAttendanceStatus";
            lblAttendanceStatus.Size = new Size(45, 24);
            lblAttendanceStatus.TabIndex = 6;
            lblAttendanceStatus.Text = "الحالة:";
            // 
            // cmbWorkPeriod
            // 
            cmbWorkPeriod.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbWorkPeriod.Font = new Font("Cairo", 9.75F);
            cmbWorkPeriod.FormattingEnabled = true;
            cmbWorkPeriod.Location = new Point(567, 58);
            cmbWorkPeriod.Name = "cmbWorkPeriod";
            cmbWorkPeriod.Size = new Size(390, 32);
            cmbWorkPeriod.TabIndex = 8;
            cmbWorkPeriod.SelectedIndexChanged += cmbWorkPeriod_SelectedIndexChanged;
            // 
            // lblWorkPeriod
            // 
            lblWorkPeriod.AutoSize = true;
            lblWorkPeriod.Font = new Font("Cairo SemiBold", 9.75F, FontStyle.Bold);
            lblWorkPeriod.Location = new Point(963, 63);
            lblWorkPeriod.Name = "lblWorkPeriod";
            lblWorkPeriod.Size = new Size(75, 24);
            lblWorkPeriod.TabIndex = 7;
            lblWorkPeriod.Text = "فترة العمل:";
            // 
            // cmbEmployee
            // 
            cmbEmployee.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbEmployee.Font = new Font("Cairo SemiBold", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            cmbEmployee.FormattingEnabled = true;
            cmbEmployee.Location = new Point(567, 24);
            cmbEmployee.Name = "cmbEmployee";
            cmbEmployee.Size = new Size(390, 32);
            cmbEmployee.TabIndex = 1;
            cmbEmployee.SelectedIndexChanged += cmbEmployee_SelectedIndexChanged;
            // 
            // lblEmployee
            // 
            lblEmployee.AutoSize = true;
            lblEmployee.Font = new Font("Cairo SemiBold", 9.75F, FontStyle.Bold);
            lblEmployee.Location = new Point(963, 29);
            lblEmployee.Name = "lblEmployee";
            lblEmployee.Size = new Size(61, 24);
            lblEmployee.TabIndex = 0;
            lblEmployee.Text = "الموظف:";
            // 
            // groupBoxTodayAttendance
            // 
            groupBoxTodayAttendance.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxTodayAttendance.Controls.Add(lblOvertimeHours);
            groupBoxTodayAttendance.Controls.Add(lblWorkingHours);
            groupBoxTodayAttendance.Controls.Add(lblStatus);
            groupBoxTodayAttendance.Controls.Add(lblCheckOutTime);
            groupBoxTodayAttendance.Controls.Add(lblCheckInTime);
            groupBoxTodayAttendance.Controls.Add(lblEmployeeName);
            groupBoxTodayAttendance.Font = new Font("Cairo", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxTodayAttendance.Location = new Point(12, 240);
            groupBoxTodayAttendance.Name = "groupBoxTodayAttendance";
            groupBoxTodayAttendance.RightToLeft = RightToLeft.Yes;
            groupBoxTodayAttendance.Size = new Size(1160, 120);
            groupBoxTodayAttendance.TabIndex = 1;
            groupBoxTodayAttendance.TabStop = false;
            groupBoxTodayAttendance.Text = "حضور اليوم";
            // 
            // lblOvertimeHours
            // 
            lblOvertimeHours.AutoSize = true;
            lblOvertimeHours.Font = new Font("Cairo", 9.75F);
            lblOvertimeHours.Location = new Point(20, 80);
            lblOvertimeHours.Name = "lblOvertimeHours";
            lblOvertimeHours.Size = new Size(111, 24);
            lblOvertimeHours.TabIndex = 5;
            lblOvertimeHours.Text = "الساعات الإضافية: -";
            // 
            // lblWorkingHours
            // 
            lblWorkingHours.AutoSize = true;
            lblWorkingHours.Font = new Font("Cairo", 9.75F);
            lblWorkingHours.Location = new Point(20, 49);
            lblWorkingHours.Name = "lblWorkingHours";
            lblWorkingHours.Size = new Size(92, 24);
            lblWorkingHours.TabIndex = 4;
            lblWorkingHours.Text = "ساعات العمل: -";
            // 
            // lblStatus
            // 
            lblStatus.AutoSize = true;
            lblStatus.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblStatus.Location = new Point(400, 80);
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(52, 24);
            lblStatus.TabIndex = 3;
            lblStatus.Text = "الحالة: -";
            // 
            // lblCheckOutTime
            // 
            lblCheckOutTime.AutoSize = true;
            lblCheckOutTime.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblCheckOutTime.Location = new Point(400, 49);
            lblCheckOutTime.Name = "lblCheckOutTime";
            lblCheckOutTime.Size = new Size(97, 24);
            lblCheckOutTime.TabIndex = 2;
            lblCheckOutTime.Text = "وقت الانصراف: -";
            // 
            // lblCheckInTime
            // 
            lblCheckInTime.AutoSize = true;
            lblCheckInTime.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblCheckInTime.Location = new Point(750, 68);
            lblCheckInTime.Name = "lblCheckInTime";
            lblCheckInTime.Size = new Size(89, 24);
            lblCheckInTime.TabIndex = 1;
            lblCheckInTime.Text = "وقت الحضور: -";
            // 
            // lblEmployeeName
            // 
            lblEmployeeName.AutoSize = true;
            lblEmployeeName.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblEmployeeName.ForeColor = Color.Blue;
            lblEmployeeName.Location = new Point(750, 25);
            lblEmployeeName.Name = "lblEmployeeName";
            lblEmployeeName.Size = new Size(124, 30);
            lblEmployeeName.TabIndex = 0;
            lblEmployeeName.Text = "اختر موظف أولاً";
            // 
            // dataGridViewAttendance
            // 
            dataGridViewAttendance.AllowUserToAddRows = false;
            dataGridViewAttendance.AllowUserToDeleteRows = false;
            dataGridViewAttendance.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridViewAttendance.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewAttendance.BackgroundColor = Color.White;
            dataGridViewAttendance.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewAttendance.Location = new Point(20, 75);
            dataGridViewAttendance.Name = "dataGridViewAttendance";
            dataGridViewAttendance.ReadOnly = true;
            dataGridViewAttendance.RightToLeft = RightToLeft.Yes;
            dataGridViewAttendance.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewAttendance.Size = new Size(1120, 325);
            dataGridViewAttendance.TabIndex = 6;
            dataGridViewAttendance.CellClick += dataGridViewAttendance_CellClick;
            dataGridViewAttendance.DataError += dataGridViewAttendance_DataError;
            // 
            // groupBoxAttendanceList
            // 
            groupBoxAttendanceList.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxAttendanceList.Controls.Add(btnRefresh);
            groupBoxAttendanceList.Controls.Add(btnDelete);
            groupBoxAttendanceList.Controls.Add(btnSearch);
            groupBoxAttendanceList.Controls.Add(txtSearch);
            groupBoxAttendanceList.Controls.Add(dateTimePickerEnd);
            groupBoxAttendanceList.Controls.Add(dateTimePickerStart);
            groupBoxAttendanceList.Controls.Add(lblEndDate);
            groupBoxAttendanceList.Controls.Add(lblStartDate);
            groupBoxAttendanceList.Controls.Add(dataGridViewAttendance);
            groupBoxAttendanceList.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxAttendanceList.Location = new Point(12, 370);
            groupBoxAttendanceList.Name = "groupBoxAttendanceList";
            groupBoxAttendanceList.RightToLeft = RightToLeft.Yes;
            groupBoxAttendanceList.Size = new Size(1160, 420);
            groupBoxAttendanceList.TabIndex = 2;
            groupBoxAttendanceList.TabStop = false;
            groupBoxAttendanceList.Text = "سجل الحضور";
            // 
            // btnRefresh
            // 
            btnRefresh.BackColor = Color.FromArgb(0, 123, 255);
            btnRefresh.FlatStyle = FlatStyle.Flat;
            btnRefresh.Font = new Font("Cairo", 9.75F);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.Location = new Point(20, 35);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(100, 34);
            btnRefresh.TabIndex = 8;
            btnRefresh.Text = "تحديث";
            btnRefresh.UseVisualStyleBackColor = false;
            btnRefresh.Click += btnRefresh_Click;
            // 
            // btnDelete
            // 
            btnDelete.BackColor = Color.FromArgb(220, 53, 69);
            btnDelete.FlatStyle = FlatStyle.Flat;
            btnDelete.Font = new Font("Cairo", 9.75F);
            btnDelete.ForeColor = Color.White;
            btnDelete.Location = new Point(126, 36);
            btnDelete.Name = "btnDelete";
            btnDelete.Size = new Size(100, 34);
            btnDelete.TabIndex = 9;
            btnDelete.Text = "حذف";
            btnDelete.UseVisualStyleBackColor = false;
            btnDelete.Click += btnDelete_Click;
            // 
            // btnSearch
            // 
            btnSearch.BackColor = Color.FromArgb(40, 167, 69);
            btnSearch.FlatStyle = FlatStyle.Flat;
            btnSearch.Font = new Font("Cairo", 9.75F);
            btnSearch.ForeColor = Color.White;
            btnSearch.Location = new Point(482, 36);
            btnSearch.Name = "btnSearch";
            btnSearch.Size = new Size(100, 34);
            btnSearch.TabIndex = 7;
            btnSearch.Text = "بحث";
            btnSearch.UseVisualStyleBackColor = false;
            btnSearch.Click += btnSearch_Click;
            // 
            // txtSearch
            // 
            txtSearch.Font = new Font("Cairo", 9.75F);
            txtSearch.Location = new Point(254, 37);
            txtSearch.Name = "txtSearch";
            txtSearch.PlaceholderText = "بحث اسم الموظف";
            txtSearch.Size = new Size(229, 32);
            txtSearch.TabIndex = 5;
            // 
            // dateTimePickerEnd
            // 
            dateTimePickerEnd.Font = new Font("Cairo", 9.75F);
            dateTimePickerEnd.Format = DateTimePickerFormat.Short;
            dateTimePickerEnd.Location = new Point(750, 37);
            dateTimePickerEnd.Name = "dateTimePickerEnd";
            dateTimePickerEnd.Size = new Size(150, 32);
            dateTimePickerEnd.TabIndex = 3;
            // 
            // dateTimePickerStart
            // 
            dateTimePickerStart.Font = new Font("Cairo", 9.75F);
            dateTimePickerStart.Format = DateTimePickerFormat.Short;
            dateTimePickerStart.Location = new Point(956, 37);
            dateTimePickerStart.Name = "dateTimePickerStart";
            dateTimePickerStart.Size = new Size(150, 32);
            dateTimePickerStart.TabIndex = 2;
            // 
            // lblEndDate
            // 
            lblEndDate.AutoSize = true;
            lblEndDate.Font = new Font("Cairo", 9.75F);
            lblEndDate.Location = new Point(905, 40);
            lblEndDate.Name = "lblEndDate";
            lblEndDate.Size = new Size(29, 24);
            lblEndDate.TabIndex = 1;
            lblEndDate.Text = "إلى";
            // 
            // lblStartDate
            // 
            lblStartDate.AutoSize = true;
            lblStartDate.Font = new Font("Cairo", 9.75F);
            lblStartDate.Location = new Point(1111, 40);
            lblStartDate.Name = "lblStartDate";
            lblStartDate.Size = new Size(27, 24);
            lblStartDate.TabIndex = 0;
            lblStartDate.Text = "من";
            // 
            // timer1
            // 
            timer1.Enabled = true;
            timer1.Interval = 1000;
            timer1.Tick += timer1_Tick;
            // 
            // AttendanceForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.White;
            ClientSize = new Size(1184, 761);
            Controls.Add(groupBoxAttendanceList);
            Controls.Add(groupBoxTodayAttendance);
            Controls.Add(groupBoxAttendance);
            Name = "AttendanceForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            Text = "إدارة الحضور والغياب";
            Load += AttendanceForm_Load;
            groupBoxAttendance.ResumeLayout(false);
            groupBoxAttendance.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDownOvertimeHours).EndInit();
            groupBoxTodayAttendance.ResumeLayout(false);
            groupBoxTodayAttendance.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewAttendance).EndInit();
            groupBoxAttendanceList.ResumeLayout(false);
            groupBoxAttendanceList.PerformLayout();
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBoxAttendance;
        private System.Windows.Forms.Label lblEmployee;
        private System.Windows.Forms.ComboBox cmbEmployee;
        private System.Windows.Forms.TextBox txtNotes;
        private System.Windows.Forms.Label lblNotes;
        private System.Windows.Forms.Button btnCheckOut;
        private System.Windows.Forms.Button btnCheckIn;
        private System.Windows.Forms.Label lblCurrentDate;
        private System.Windows.Forms.Label lblCurrentTime;
        private System.Windows.Forms.GroupBox groupBoxTodayAttendance;
        private System.Windows.Forms.Label lblEmployeeName;
        private System.Windows.Forms.Label lblCheckInTime;
        private System.Windows.Forms.Label lblCheckOutTime;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.Label lblWorkingHours;
        private System.Windows.Forms.Label lblOvertimeHours;
        private System.Windows.Forms.ComboBox cmbAttendanceStatus;
        private System.Windows.Forms.Label lblAttendanceStatus;
        private System.Windows.Forms.NumericUpDown numericUpDownOvertimeHours;
        private System.Windows.Forms.Label lblOvertimeInput;
        private System.Windows.Forms.Button btnManualEntry;
        private System.Windows.Forms.DataGridView dataGridViewAttendance;
        private System.Windows.Forms.GroupBox groupBoxAttendanceList;
        private System.Windows.Forms.Label lblStartDate;
        private System.Windows.Forms.Label lblEndDate;
        private System.Windows.Forms.DateTimePicker dateTimePickerStart;
        private System.Windows.Forms.DateTimePicker dateTimePickerEnd;
        private System.Windows.Forms.TextBox txtSearch;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.Button btnDelete;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.Timer timer1;
        private System.Windows.Forms.ComboBox cmbWorkPeriod;
        private System.Windows.Forms.Label lblWorkPeriod;
    }
}
