using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class EmployeeDailyStatusForm : Form
    {
        private int workPeriodId;
        private int employeeCode;
        private string employeeName;
        private DateTime startDate;
        private DateTime endDate;
        
        // Controls
        private Label lblTitle;
        private DataGridView dataGridViewDays;
        private Button btnSave;
        private Button btnClose;

        public EmployeeDailyStatusForm(int workPeriodId, int employeeCode, string employeeName, DateTime startDate, DateTime endDate)
        {
            this.workPeriodId = workPeriodId;
            this.employeeCode = employeeCode;
            this.employeeName = employeeName;
            this.startDate = startDate;
            this.endDate = endDate;
            
            InitializeComponent();
            LoadDailyStatus();
        }

        private void InitializeComponent()
        {
            this.Size = new Size(600, 500);
            this.Text = $"تعديل الحالة اليومية - {employeeName}";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Font = new Font("Cairo", 10F);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // العنوان
            lblTitle = new Label
            {
                Text = $"تعديل الحالة اليومية للموظف: {employeeName}",
                Font = new Font("Cairo", 12F, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Top,
                Height = 40
            };

            // جدول الأيام
            dataGridViewDays = new DataGridView
            {
                Top = 50,
                Height = 350,
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                BackgroundColor = Color.White,
                GridColor = Color.LightGray
            };

            // الأزرار
            var buttonPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Bottom
            };

            btnSave = new Button
            {
                Text = "حفظ التغييرات",
                Size = new Size(120, 35),
                Location = new Point(20, 15),
                BackColor = Color.Green,
                ForeColor = Color.White,
                Font = new Font("Cairo", 10F, FontStyle.Bold)
            };
            btnSave.Click += BtnSave_Click;

            btnClose = new Button
            {
                Text = "إغلاق",
                Size = new Size(100, 35),
                Location = new Point(150, 15),
                BackColor = Color.Gray,
                ForeColor = Color.White,
                Font = new Font("Cairo", 10F, FontStyle.Bold)
            };
            btnClose.Click += (s, e) => this.Close();

            buttonPanel.Controls.AddRange(new Control[] { btnSave, btnClose });

            // إضافة العناصر للنموذج
            this.Controls.AddRange(new Control[] { lblTitle, dataGridViewDays, buttonPanel });
        }

        private void LoadDailyStatus()
        {
            try
            {
                // إنشاء جدول للعرض
                var displayTable = new DataTable();
                displayTable.Columns.Add("التاريخ", typeof(string));
                displayTable.Columns.Add("اليوم", typeof(string));
                displayTable.Columns.Add("الحالة", typeof(string));
                displayTable.Columns.Add("الملاحظات", typeof(string));

                // إضافة الأيام
                for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    var statusData = GetDailyStatus(date);
                    
                    var newRow = displayTable.NewRow();
                    newRow["التاريخ"] = date.ToString("dd/MM/yyyy");
                    newRow["اليوم"] = GetDayName(date.DayOfWeek);
                    newRow["الحالة"] = statusData.Status;
                    newRow["الملاحظات"] = statusData.Notes;
                    
                    displayTable.Rows.Add(newRow);
                }

                dataGridViewDays.DataSource = displayTable;
                
                // إضافة عمود ComboBox للحالة
                var statusColumn = new DataGridViewComboBoxColumn
                {
                    Name = "الحالة",
                    HeaderText = "الحالة",
                    DataPropertyName = "الحالة",
                    DisplayMember = "الحالة",
                    ValueMember = "الحالة"
                };
                statusColumn.Items.AddRange(new[] { "حضور", "غياب", "إجازة" });
                
                // استبدال العمود العادي بـ ComboBox
                dataGridViewDays.Columns.RemoveAt(2);
                dataGridViewDays.Columns.Insert(2, statusColumn);
                
                // تنسيق الأعمدة
                dataGridViewDays.Columns["التاريخ"].Width = 100;
                dataGridViewDays.Columns["اليوم"].Width = 80;
                dataGridViewDays.Columns["الحالة"].Width = 100;
                dataGridViewDays.Columns["الملاحظات"].Width = 200;
                
                foreach (DataGridViewColumn column in dataGridViewDays.Columns)
                {
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.HeaderCell.Style.Font = new Font("Cairo", 9F, FontStyle.Bold);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private (string Status, string Notes) GetDailyStatus(DateTime date)
        {
            try
            {
                using (var connection = new System.Data.SQLite.SQLiteConnection(DatabaseHelper.ConnectionString))
                {
                    connection.Open();
                    string sql = @"SELECT Status, Notes FROM DailyWorkStatus 
                                 WHERE WorkPeriodId = @WorkPeriodId 
                                 AND EmployeeCode = @EmployeeCode 
                                 AND Date = @Date";
                    
                    using (var command = new System.Data.SQLite.SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                        command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                        command.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return (reader.GetString("Status"), reader.IsDBNull("Notes") ? "" : reader.GetString("Notes"));
                            }
                        }
                    }
                }
                return ("حضور", ""); // افتراضي
            }
            catch
            {
                return ("حضور", ""); // افتراضي في حالة الخطأ
            }
        }

        private string GetDayName(DayOfWeek dayOfWeek)
        {
            switch (dayOfWeek)
            {
                case DayOfWeek.Sunday: return "الأحد";
                case DayOfWeek.Monday: return "الاثنين";
                case DayOfWeek.Tuesday: return "الثلاثاء";
                case DayOfWeek.Wednesday: return "الأربعاء";
                case DayOfWeek.Thursday: return "الخميس";
                case DayOfWeek.Friday: return "الجمعة";
                case DayOfWeek.Saturday: return "السبت";
                default: return "";
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                int updatedCount = 0;
                
                foreach (DataGridViewRow row in dataGridViewDays.Rows)
                {
                    if (row.IsNewRow) continue;
                    
                    string dateStr = row.Cells["التاريخ"].Value.ToString();
                    DateTime date = DateTime.ParseExact(dateStr, "dd/MM/yyyy", null);
                    string status = row.Cells["الحالة"].Value?.ToString() ?? "حضور";
                    string notes = row.Cells["الملاحظات"].Value?.ToString() ?? "";
                    
                    // تحديث قاعدة البيانات
                    DatabaseHelper.UpdateDailyWorkStatus(workPeriodId, employeeCode, date, status, notes);
                    updatedCount++;
                }
                
                MessageBox.Show($"تم تحديث {updatedCount} يوم بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التغييرات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
