namespace EmployeeManagementSystem
{
    partial class NotificationsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            notificationsDataGridView = new DataGridView();
            panel1 = new Panel();
            label1 = new Label();
            btnRefreshNotifications = new Button();
            markAllReadButton = new Button();
            toolTip1 = new ToolTip(components);
            lbl_NoNotifications = new Label();
            ((System.ComponentModel.ISupportInitialize)notificationsDataGridView).BeginInit();
            panel1.SuspendLayout();
            SuspendLayout();
            // 
            // notificationsDataGridView
            // 
            notificationsDataGridView.AllowUserToAddRows = false;
            notificationsDataGridView.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            notificationsDataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            notificationsDataGridView.BackgroundColor = Color.White;
            notificationsDataGridView.BorderStyle = BorderStyle.None;
            notificationsDataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            notificationsDataGridView.Location = new Point(12, 60);
            notificationsDataGridView.Name = "notificationsDataGridView";
            notificationsDataGridView.Size = new Size(1016, 378);
            notificationsDataGridView.TabIndex = 0;
            notificationsDataGridView.CellContentClick += notificationsDataGridView_CellContentClick;
            // 
            // panel1
            // 
            panel1.BackColor = Color.FromArgb(0, 122, 204);
            panel1.Controls.Add(label1);
            panel1.Controls.Add(btnRefreshNotifications);
            panel1.Controls.Add(markAllReadButton);
            panel1.Dock = DockStyle.Top;
            panel1.Location = new Point(0, 0);
            panel1.Name = "panel1";
            panel1.Size = new Size(1040, 54);
            panel1.TabIndex = 1;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new Font("Cairo", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            label1.ForeColor = Color.White;
            label1.Location = new Point(54, 9);
            label1.Name = "label1";
            label1.Size = new Size(96, 36);
            label1.TabIndex = 0;
            label1.Text = "الاشعارات";
            toolTip1.SetToolTip(label1, "الاشعارات");
            // 
            // btnRefreshNotifications
            // 
            btnRefreshNotifications.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnRefreshNotifications.BackColor = Color.White;
            btnRefreshNotifications.FlatStyle = FlatStyle.Flat;
            btnRefreshNotifications.Font = new Font("Cairo", 10.75F, FontStyle.Bold);
            btnRefreshNotifications.Image = Properties.Resources.available_updates_32px;
            btnRefreshNotifications.ImageAlign = ContentAlignment.MiddleLeft;
            btnRefreshNotifications.Location = new Point(761, 6);
            btnRefreshNotifications.Name = "btnRefreshNotifications";
            btnRefreshNotifications.Size = new Size(99, 42);
            btnRefreshNotifications.TabIndex = 1;
            btnRefreshNotifications.Text = "تحديث";
            btnRefreshNotifications.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(btnRefreshNotifications, "تحديث الاشعارات");
            btnRefreshNotifications.UseVisualStyleBackColor = false;
            btnRefreshNotifications.Click += BtnRefreshNotifications_Click;
            // 
            // markAllReadButton
            // 
            markAllReadButton.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            markAllReadButton.BackColor = Color.White;
            markAllReadButton.FlatStyle = FlatStyle.Flat;
            markAllReadButton.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            markAllReadButton.Image = Properties.Resources.checked_checkbox_32px;
            markAllReadButton.ImageAlign = ContentAlignment.MiddleLeft;
            markAllReadButton.Location = new Point(866, 6);
            markAllReadButton.Name = "markAllReadButton";
            markAllReadButton.Size = new Size(162, 42);
            markAllReadButton.TabIndex = 1;
            markAllReadButton.Text = "تحديد الكل مقروءة";
            markAllReadButton.TextAlign = ContentAlignment.MiddleRight;
            toolTip1.SetToolTip(markAllReadButton, "تحديد الكل الاشعارت مقروءة");
            markAllReadButton.UseVisualStyleBackColor = false;
            markAllReadButton.Click += markAllReadButton_Click;
            // 
            // lbl_NoNotifications
            // 
            lbl_NoNotifications.Anchor = AnchorStyles.None;
            lbl_NoNotifications.AutoSize = true;
            lbl_NoNotifications.Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lbl_NoNotifications.Location = new Point(493, 210);
            lbl_NoNotifications.Name = "lbl_NoNotifications";
            lbl_NoNotifications.Size = new Size(54, 30);
            lbl_NoNotifications.TabIndex = 2;
            lbl_NoNotifications.Text = "label2";
            lbl_NoNotifications.Visible = false;
            // 
            // NotificationsForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1040, 450);
            Controls.Add(lbl_NoNotifications);
            Controls.Add(panel1);
            Controls.Add(notificationsDataGridView);
            Name = "NotificationsForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "الاشعارات";
            Load += NotificationsForm_Load;
            ((System.ComponentModel.ISupportInitialize)notificationsDataGridView).EndInit();
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private System.Windows.Forms.DataGridView notificationsDataGridView;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Button markAllReadButton;        // تم نقل تعريف الأعمدة إلى CustomizeDataGridView
        private ToolTip toolTip1;
        private Button btnRefreshNotifications;
        private Label lbl_NoNotifications;
    }
}
