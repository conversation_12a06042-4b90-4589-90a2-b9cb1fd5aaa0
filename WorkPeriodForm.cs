using System;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using System.Collections.Generic;

namespace EmployeeManagementSystem
{
    public partial class WorkPeriodForm : Form
    {
        private int selectedWorkPeriodId = 0;
        private int selectedEmployeeCode = 0;
        private string selectedEmployeeName = "";

        public WorkPeriodForm()
        {
            InitializeComponent();
        }

        private void WorkPeriodForm_Load(object sender, EventArgs e)
        {
            // تهيئة النموذج بحالة نظيفة
            InitializeFormToCleanState();
        }

        private void InitializeFormToCleanState()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("بدء تهيئة النموذج بحالة نظيفة...");

                // إعادة تعيين المتغيرات أولاً
                selectedEmployeeCode = 0;
                selectedEmployeeName = "";
                selectedWorkPeriodId = 0;

                // تحميل قوائم البيانات (مع ضمان التهيئة الصحيحة)
                LoadEmployees(); // سيضع cmbEmployee على "اختر موظف"
                LoadWorkGroups();
                SetDefaultSearchDates();

                // مسح جميع الحقول وإعادة تعيين الحالة
                ClearAllFields(); // سيعيد تعيين checkbox تحديد الكل

                // تحميل جميع فترات العمل (بدون فلترة)
                LoadAllWorkPeriods();

                // التأكد من إعادة تعيين checkbox تحديد الكل مرة أخرى بعد تحميل البيانات
                ResetSelectAllCheckbox();

                // التأكد من أن cmbEmployee على "اختر موظف"
                if (cmbEmployee.SelectedIndex != 0)
                {
                    cmbEmployee.SelectedIndexChanged -= cmbEmployee_SelectedIndexChanged;
                    cmbEmployee.SelectedIndex = 0;
                    cmbEmployee.SelectedIndexChanged += cmbEmployee_SelectedIndexChanged;
                }

                System.Diagnostics.Debug.WriteLine("تم تهيئة النموذج بحالة نظيفة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة النموذج: {ex.Message}");
                MessageBox.Show($"خطأ في تهيئة النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadEmployees()
        {
            try
            {
                // منع إثارة حدث التغيير أثناء التحميل
                cmbEmployee.SelectedIndexChanged -= cmbEmployee_SelectedIndexChanged;

                var employees = DatabaseHelper.GetEmployeesForAttendance();
                cmbEmployee.Items.Clear();
                cmbEmployee.Items.Add("اختر موظف");

                foreach (DataRow row in employees.Rows)
                {
                    string employeeInfo = $"{row["Name"]}";
                    cmbEmployee.Items.Add(employeeInfo);
                }

                // التأكد من اختيار "اختر موظف" (المؤشر 0)
                cmbEmployee.SelectedIndex = 0;

                // إعادة تعيين المتغيرات
                selectedEmployeeCode = 0;
                selectedEmployeeName = "";

                // إعادة ربط حدث التغيير
                cmbEmployee.SelectedIndexChanged += cmbEmployee_SelectedIndexChanged;

                System.Diagnostics.Debug.WriteLine($"تم تحميل {employees.Rows.Count} موظف - المؤشر على 'اختر موظف'");
            }
            catch (Exception ex)
            {
                // إعادة ربط الحدث في حالة الخطأ
                cmbEmployee.SelectedIndexChanged += cmbEmployee_SelectedIndexChanged;
                MessageBox.Show($"خطأ في تحميل قائمة الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadAllWorkPeriods()
        {
            try
            {
                // تحميل جميع فترات العمل بدون فلترة
                DateTime startDate = DateTime.Today.AddYears(-1);
                DateTime endDate = DateTime.Today.AddYears(1);
                DataTable workPeriods = DatabaseHelper.GetAllWorkPeriods(startDate, endDate);

                // منع إثارة أحداث التحديد أثناء تحميل البيانات
                dataGridViewWorkPeriods.SelectionChanged -= dataGridViewWorkPeriods_SelectionChanged;

                dataGridViewWorkPeriods.DataSource = workPeriods;
                FormatDataGridView();

                // مسح أي تحديد
                dataGridViewWorkPeriods.ClearSelection();

                // إعادة ربط حدث التحديد
                dataGridViewWorkPeriods.SelectionChanged += dataGridViewWorkPeriods_SelectionChanged;

                System.Diagnostics.Debug.WriteLine($"تم تحميل {workPeriods.Rows.Count} فترة عمل (جميع الفترات)");
            }
            catch (Exception ex)
            {
                // إعادة ربط الحدث في حالة الخطأ
                dataGridViewWorkPeriods.SelectionChanged += dataGridViewWorkPeriods_SelectionChanged;
                MessageBox.Show($"خطأ في تحميل فترات العمل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadWorkPeriods()
        {
            try
            {
                DataTable workPeriods;

                if (selectedEmployeeCode > 0)
                {
                    // استخدام نطاق تاريخ واسع لعرض جميع فترات الموظف
                    DateTime startDate = DateTime.Today.AddYears(-1);
                    DateTime endDate = DateTime.Today.AddYears(1);
                    workPeriods = DatabaseHelper.GetWorkPeriodsByEmployee(selectedEmployeeCode, startDate, endDate);
                    System.Diagnostics.Debug.WriteLine($"تم تحميل {workPeriods.Rows.Count} فترة عمل للموظف {selectedEmployeeCode}");
                }
                else
                {
                    // استخدام نطاق تاريخ واسع لعرض جميع الفترات
                    DateTime startDate = DateTime.Today.AddYears(-1);
                    DateTime endDate = DateTime.Today.AddYears(1);
                    workPeriods = DatabaseHelper.GetAllWorkPeriods(startDate, endDate);
                    System.Diagnostics.Debug.WriteLine($"تم تحميل {workPeriods.Rows.Count} فترة عمل (جميع الموظفين)");
                }

                dataGridViewWorkPeriods.DataSource = workPeriods;
                FormatDataGridView();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل فترات العمل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void FormatDataGridView()
        {
            try
            {
                // تنسيق الأعمدة
                if (dataGridViewWorkPeriods.Columns.Count > 0)
                {
                    foreach (DataGridViewColumn column in dataGridViewWorkPeriods.Columns)
                    {
                        column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنسيق الجدول: {ex.Message}");
            }
        }

        private void SetDefaultValues()
        {
            dateTimePickerStart.Value = DateTime.Today;
            dateTimePickerEnd.Value = DateTime.Today.AddDays(30);
            cmbStatus.SelectedIndex = 0; // نشط
            numericUpDownDailyHours.Value = 8;
            
            // تحديد أيام العمل الافتراضية (الأحد إلى الخميس)
            chkSunday.Checked = true;
            chkMonday.Checked = true;
            chkTuesday.Checked = true;
            chkWednesday.Checked = true;
            chkThursday.Checked = true;
            chkFriday.Checked = false;
            chkSaturday.Checked = false;
        }

        private void cmbEmployee_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (cmbEmployee.SelectedIndex > 0)
                {
                    string selectedEmployeeName = cmbEmployee.SelectedItem.ToString();

                    // البحث عن الموظف بالاسم للحصول على الكود
                    var employees = DatabaseHelper.GetEmployeesForAttendance();
                    foreach (DataRow row in employees.Rows)
                    {
                        if (row["Name"].ToString() == selectedEmployeeName)
                        {
                            this.selectedEmployeeName = selectedEmployeeName;
                            this.selectedEmployeeCode = Convert.ToInt32(row["EmployeeCode"]);
                            break;
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"تم اختيار الموظف: {selectedEmployeeName} ({selectedEmployeeCode})");

                    // تحميل فترات العمل للموظف المحدد
                    LoadWorkPeriods();
                }
                else
                {
                    selectedEmployeeCode = 0;
                    selectedEmployeeName = "";

                    System.Diagnostics.Debug.WriteLine("تم إلغاء اختيار الموظف - عرض جميع فترات العمل");

                    // عرض جميع فترات العمل
                    LoadAllWorkPeriods();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تغيير اختيار الموظف: {ex.Message}");
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                var workPeriod = CreateWorkPeriodFromInput();
                DatabaseHelper.AddWorkPeriod(workPeriod);
                
                MessageBox.Show("تم إضافة فترة العمل بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                LoadWorkPeriods();
                ClearFields();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة فترة العمل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            if (selectedWorkPeriodId <= 0)
            {
                MessageBox.Show("الرجاء اختيار فترة عمل للتحديث", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!ValidateInput())
                return;

            try
            {
                var workPeriod = CreateWorkPeriodFromInput();
                workPeriod.WorkPeriodId = selectedWorkPeriodId;
                DatabaseHelper.UpdateWorkPeriod(workPeriod);
                
                MessageBox.Show("تم تحديث فترة العمل بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                LoadWorkPeriods();
                ClearFields();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث فترة العمل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (selectedWorkPeriodId <= 0)
            {
                MessageBox.Show("الرجاء اختيار فترة عمل للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف فترة العمل المحددة؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    DatabaseHelper.DeleteWorkPeriod(selectedWorkPeriodId);
                    MessageBox.Show("تم حذف فترة العمل بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    LoadWorkPeriods();
                    ClearFields();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف فترة العمل: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearFields();
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable workPeriods;

                if (string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    // إذا كان حقل البحث فارغ، عرض رسالة تنبيه
                    MessageBox.Show("الرجاء إدخال اسم الموظف أو مكان العمل للبحث", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // استخدام نطاق تاريخ واسع للبحث
                DateTime startDate = DateTime.Today.AddYears(-1);
                DateTime endDate = DateTime.Today.AddYears(1);

                // البحث المباشر في قاعدة البيانات
                workPeriods = DatabaseHelper.SearchWorkPeriods(txtSearch.Text.Trim(), startDate, endDate);

                // عرض النتائج
                dataGridViewWorkPeriods.DataSource = workPeriods;

                // إظهار رسالة بعدد النتائج
                int resultCount = workPeriods.Rows.Count;
                if (resultCount == 0)
                {
                    MessageBox.Show($"لم يتم العثور على نتائج للبحث عن: '{txtSearch.Text}'", "نتيجة البحث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show($"تم العثور على {resultCount} نتيجة للبحث عن: '{txtSearch.Text}'", "نتيجة البحث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                // إعادة تهيئة النموذج بحالة نظيفة
                InitializeFormToCleanState();

                MessageBox.Show("تم تحديث البيانات وإعادة تعيين النموذج", "تحديث",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dataGridViewWorkPeriods_SelectionChanged(object sender, EventArgs e)
        {
            // تحديث حالة "تحديد الكل" بناءً على التحديد الحالي
            UpdateSelectAllCheckbox();

            if (dataGridViewWorkPeriods.SelectedRows.Count > 0)
            {
                try
                {
                    var selectedRow = dataGridViewWorkPeriods.SelectedRows[0];
                    if (selectedRow.Cells["المعرف"] != null && selectedRow.Cells["المعرف"].Value != null)
                    {
                        selectedWorkPeriodId = Convert.ToInt32(selectedRow.Cells["المعرف"].Value);

                        var workPeriod = DatabaseHelper.GetWorkPeriodById(selectedWorkPeriodId);
                        if (workPeriod != null)
                        {
                            LoadWorkPeriodToForm(workPeriod);
                            UpdateSummaryLabels(workPeriod);
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل بيانات فترة العمل: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                selectedWorkPeriodId = 0;
                ResetSummaryLabels();
            }
        }

        private void ResetSelectAllCheckbox()
        {
            try
            {
                // إعادة تعيين checkbox تحديد الكل بشكل صحيح
                chkSelectAll.CheckedChanged -= chkSelectAll_CheckedChanged;
                chkSelectAll.Checked = false;
                chkSelectAll.CheckedChanged += chkSelectAll_CheckedChanged;

                System.Diagnostics.Debug.WriteLine("تم إعادة تعيين checkbox تحديد الكل إلى false");
            }
            catch (Exception ex)
            {
                // إعادة ربط الحدث في حالة الخطأ
                chkSelectAll.CheckedChanged += chkSelectAll_CheckedChanged;
                System.Diagnostics.Debug.WriteLine($"خطأ في إعادة تعيين checkbox تحديد الكل: {ex.Message}");
            }
        }

        private void UpdateSelectAllCheckbox()
        {
            try
            {
                // تجنب إثارة الحدث أثناء التحديث
                chkSelectAll.CheckedChanged -= chkSelectAll_CheckedChanged;

                // عد الصفوف الفعلية (بدون الصف الجديد)
                int totalRows = 0;
                for (int i = 0; i < dataGridViewWorkPeriods.Rows.Count; i++)
                {
                    if (!dataGridViewWorkPeriods.Rows[i].IsNewRow)
                        totalRows++;
                }

                int selectedRows = dataGridViewWorkPeriods.SelectedRows.Count;

                // إذا كانت جميع الصفوف الفعلية محددة
                if (totalRows > 0 && selectedRows == totalRows)
                {
                    chkSelectAll.Checked = true;
                }
                // إذا لم يكن هناك تحديد أو تحديد جزئي
                else
                {
                    chkSelectAll.Checked = false;
                }

                System.Diagnostics.Debug.WriteLine($"تحديث حالة تحديد الكل: إجمالي={totalRows}, محدد={selectedRows}, حالة CheckBox={chkSelectAll.Checked}");

                // إعادة ربط الحدث
                chkSelectAll.CheckedChanged += chkSelectAll_CheckedChanged;
            }
            catch (Exception ex)
            {
                // إعادة ربط الحدث في حالة الخطأ
                chkSelectAll.CheckedChanged += chkSelectAll_CheckedChanged;
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث حالة تحديد الكل: {ex.Message}");
            }
        }

        private bool ValidateInput()
        {
            if (selectedEmployeeCode <= 0)
            {
                MessageBox.Show("الرجاء اختيار موظف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtProjectName.Text))
            {
                MessageBox.Show("الرجاء إدخال مكان العمل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (dateTimePickerEnd.Value <= dateTimePickerStart.Value)
            {
                MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (!GetSelectedWorkingDays().Any())
            {
                MessageBox.Show("الرجاء اختيار يوم واحد على الأقل من أيام العمل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private WorkPeriod CreateWorkPeriodFromInput()
        {
            return new WorkPeriod
            {
                EmployeeCode = selectedEmployeeCode,
                EmployeeName = selectedEmployeeName,
                ProjectName = txtProjectName.Text.Trim(),
                Description = txtDescription.Text.Trim(),
                StartDate = dateTimePickerStart.Value.Date,
                EndDate = dateTimePickerEnd.Value.Date,
                WorkingDays = string.Join(",", GetSelectedWorkingDays()),
                DailyWorkingHours = (double)numericUpDownDailyHours.Value,
                Status = cmbStatus.SelectedItem?.ToString() ?? "نشط"
            };
        }

        private List<string> GetSelectedWorkingDays()
        {
            var selectedDays = new List<string>();
            
            if (chkSunday.Checked) selectedDays.Add("الأحد");
            if (chkMonday.Checked) selectedDays.Add("الاثنين");
            if (chkTuesday.Checked) selectedDays.Add("الثلاثاء");
            if (chkWednesday.Checked) selectedDays.Add("الأربعاء");
            if (chkThursday.Checked) selectedDays.Add("الخميس");
            if (chkFriday.Checked) selectedDays.Add("الجمعة");
            if (chkSaturday.Checked) selectedDays.Add("السبت");
            
            return selectedDays;
        }

        private void LoadWorkPeriodToForm(WorkPeriod workPeriod)
        {
            // تحديد الموظف
            for (int i = 1; i < cmbEmployee.Items.Count; i++)
            {
                string item = cmbEmployee.Items[i].ToString();
                if (item.Contains($"- {workPeriod.EmployeeCode}"))
                {
                    cmbEmployee.SelectedIndex = i;
                    break;
                }
            }

            txtProjectName.Text = workPeriod.ProjectName ?? "";
            txtDescription.Text = workPeriod.Description ?? "";
            dateTimePickerStart.Value = workPeriod.StartDate;
            dateTimePickerEnd.Value = workPeriod.EndDate;
            numericUpDownDailyHours.Value = (decimal)workPeriod.DailyWorkingHours;

            // تحديد الحالة
            if (cmbStatus.Items.Contains(workPeriod.Status))
            {
                cmbStatus.SelectedItem = workPeriod.Status;
            }

            // تحديد أيام العمل
            SetWorkingDaysFromString(workPeriod.WorkingDays ?? "");
        }

        private void SetWorkingDaysFromString(string workingDays)
        {
            // إعادة تعيين جميع الأيام
            chkSunday.Checked = false;
            chkMonday.Checked = false;
            chkTuesday.Checked = false;
            chkWednesday.Checked = false;
            chkThursday.Checked = false;
            chkFriday.Checked = false;
            chkSaturday.Checked = false;

            if (string.IsNullOrEmpty(workingDays))
                return;

            var days = workingDays.Split(',').Select(d => d.Trim()).ToList();

            if (days.Contains("الأحد")) chkSunday.Checked = true;
            if (days.Contains("الاثنين")) chkMonday.Checked = true;
            if (days.Contains("الثلاثاء")) chkTuesday.Checked = true;
            if (days.Contains("الأربعاء")) chkWednesday.Checked = true;
            if (days.Contains("الخميس")) chkThursday.Checked = true;
            if (days.Contains("الجمعة")) chkFriday.Checked = true;
            if (days.Contains("السبت")) chkSaturday.Checked = true;
        }

        private void UpdateSummaryLabels(WorkPeriod workPeriod)
        {
            lblSelectedPeriod.Text = $"الفترة: {workPeriod.ProjectName}";
            lblTotalDays.Text = $"إجمالي الأيام: {workPeriod.GetTotalWorkingDays()}";
            lblTotalHours.Text = $"إجمالي الساعات: {workPeriod.GetTotalExpectedHours():F1}";
        }

        private void ResetSummaryLabels()
        {
            lblSelectedPeriod.Text = "لم يتم اختيار فترة";
            lblTotalDays.Text = "إجمالي الأيام: 0";
            lblTotalHours.Text = "إجمالي الساعات: 0";
        }

        private void ClearAllFields()
        {
            try
            {
                // إعادة تعيين المتغيرات
                selectedWorkPeriodId = 0;
                selectedEmployeeCode = 0;
                selectedEmployeeName = "";

                // مسح حقول الإدخال مع منع إثارة الأحداث
                cmbEmployee.SelectedIndexChanged -= cmbEmployee_SelectedIndexChanged;
                cmbEmployee.SelectedIndex = 0; // "اختر موظف"
                cmbEmployee.SelectedIndexChanged += cmbEmployee_SelectedIndexChanged;

                txtProjectName.Clear();
                txtDescription.Clear();
                txtSearch.Clear();

                // إعادة تعيين القيم الافتراضية
                SetDefaultValues();
                ResetSummaryLabels();

                // مسح تحديد الجدول
                dataGridViewWorkPeriods.ClearSelection();

                // إعادة تعيين checkbox تحديد الكل بشكل صحيح
                ResetSelectAllCheckbox();

                System.Diagnostics.Debug.WriteLine("تم مسح جميع الحقول وإعادة تعيين الحالة");
            }
            catch (Exception ex)
            {
                // إعادة ربط الأحداث في حالة الخطأ
                cmbEmployee.SelectedIndexChanged += cmbEmployee_SelectedIndexChanged;
                chkSelectAll.CheckedChanged += chkSelectAll_CheckedChanged;
                System.Diagnostics.Debug.WriteLine($"خطأ في مسح الحقول: {ex.Message}");
            }
        }

        private void ClearFields()
        {
            selectedWorkPeriodId = 0;
            cmbEmployee.SelectedIndex = 0;
            txtProjectName.Clear();
            txtDescription.Clear();
            SetDefaultValues();
            ResetSummaryLabels();
        }

        private void dataGridViewWorkPeriods_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            // منع ظهور رسالة الخطأ الافتراضية
            e.Cancel = true;

            // تسجيل الخطأ للتشخيص
            System.Diagnostics.Debug.WriteLine($"DataGridView Error: {e.Exception?.Message} at Row: {e.RowIndex}, Column: {e.ColumnIndex}");

            // يمكن إضافة معالجة خاصة هنا إذا لزم الأمر
        }

        private void monthCalendar_DateChanged(object sender, DateRangeEventArgs e)
        {
            try
            {
                // تحديث أيام العمل بناءً على التقويم المحدد
                UpdateWorkingDaysFromCalendar();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث التقويم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateWorkingDaysFromCalendar()
        {
            try
            {
                // مسح جميع أيام العمل أولاً
                chkSunday.Checked = false;
                chkMonday.Checked = false;
                chkTuesday.Checked = false;
                chkWednesday.Checked = false;
                chkThursday.Checked = false;
                chkFriday.Checked = false;
                chkSaturday.Checked = false;

                // الحصول على التواريخ المحددة في التقويم
                DateTime startDate = monthCalendar.SelectionStart;
                DateTime endDate = monthCalendar.SelectionEnd;

                // تحديد أيام العمل بناءً على التواريخ المحددة
                for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    switch (date.DayOfWeek)
                    {
                        case DayOfWeek.Sunday:
                            chkSunday.Checked = true;
                            break;
                        case DayOfWeek.Monday:
                            chkMonday.Checked = true;
                            break;
                        case DayOfWeek.Tuesday:
                            chkTuesday.Checked = true;
                            break;
                        case DayOfWeek.Wednesday:
                            chkWednesday.Checked = true;
                            break;
                        case DayOfWeek.Thursday:
                            chkThursday.Checked = true;
                            break;
                        case DayOfWeek.Friday:
                            chkFriday.Checked = true;
                            break;
                        case DayOfWeek.Saturday:
                            chkSaturday.Checked = true;
                            break;
                    }
                }

                // تحديث تواريخ البداية والنهاية
                dateTimePickerStart.Value = startDate;
                dateTimePickerEnd.Value = endDate;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث أيام العمل من التقويم: {ex.Message}");
            }
        }

        private void chkSelectAll_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                // تعطيل أحداث التحديد مؤقتاً لتجنب التداخل
                dataGridViewWorkPeriods.SelectionChanged -= dataGridViewWorkPeriods_SelectionChanged;

                if (chkSelectAll.Checked)
                {
                    // تحديد جميع الصفوف يدوياً
                    dataGridViewWorkPeriods.ClearSelection();
                    for (int i = 0; i < dataGridViewWorkPeriods.Rows.Count; i++)
                    {
                        if (!dataGridViewWorkPeriods.Rows[i].IsNewRow)
                        {
                            dataGridViewWorkPeriods.Rows[i].Selected = true;
                        }
                    }
                    System.Diagnostics.Debug.WriteLine($"تم تحديد جميع الصفوف يدوياً: {dataGridViewWorkPeriods.SelectedRows.Count}");
                }
                else
                {
                    // إلغاء تحديد جميع الصفوف
                    dataGridViewWorkPeriods.ClearSelection();
                    System.Diagnostics.Debug.WriteLine("تم إلغاء تحديد جميع الصفوف");
                }

                // إعادة تفعيل أحداث التحديد
                dataGridViewWorkPeriods.SelectionChanged += dataGridViewWorkPeriods_SelectionChanged;
            }
            catch (Exception ex)
            {
                // إعادة تفعيل الأحداث في حالة الخطأ
                dataGridViewWorkPeriods.SelectionChanged += dataGridViewWorkPeriods_SelectionChanged;
                MessageBox.Show($"خطأ في تحديد الصفوف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDeleteSelected_Click(object sender, EventArgs e)
        {
            try
            {
                // عد الصفوف المحددة الفعلية (بدون الصف الجديد)
                int actualSelectedCount = 0;
                foreach (DataGridViewRow row in dataGridViewWorkPeriods.SelectedRows)
                {
                    if (!row.IsNewRow)
                        actualSelectedCount++;
                }

                if (actualSelectedCount == 0)
                {
                    MessageBox.Show("الرجاء اختيار فترة عمل واحدة أو أكثر للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show($"هل أنت متأكد من حذف {actualSelectedCount} فترة عمل محددة؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    int successCount = 0;
                    int errorCount = 0;
                    var selectedIds = new List<int>();

                    System.Diagnostics.Debug.WriteLine($"بدء عملية جمع المعرفات من {dataGridViewWorkPeriods.SelectedRows.Count} صف محدد");

                    // جمع معرفات فترات العمل المحددة أولاً
                    foreach (DataGridViewRow row in dataGridViewWorkPeriods.SelectedRows)
                    {
                        try
                        {
                            // التأكد من أن الصف ليس صف جديد
                            if (!row.IsNewRow && row.Cells["المعرف"] != null && row.Cells["المعرف"].Value != null)
                            {
                                string idValue = row.Cells["المعرف"].Value.ToString();
                                if (!string.IsNullOrEmpty(idValue) && int.TryParse(idValue, out int workPeriodId))
                                {
                                    selectedIds.Add(workPeriodId);
                                    System.Diagnostics.Debug.WriteLine($"تم جمع معرف فترة العمل: {workPeriodId} من الصف {row.Index}");
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine($"فشل في تحويل المعرف: '{idValue}' من الصف {row.Index}");
                                    errorCount++;
                                }
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"تم تجاهل الصف {row.Index} - صف جديد أو قيمة فارغة");
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"خطأ في قراءة معرف فترة العمل من الصف {row.Index}: {ex.Message}");
                            errorCount++;
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"تم جمع {selectedIds.Count} معرف للحذف من أصل {actualSelectedCount} صف محدد");

                    if (selectedIds.Count == 0)
                    {
                        MessageBox.Show("لم يتم العثور على معرفات صحيحة للحذف", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    // حذف فترات العمل
                    foreach (int workPeriodId in selectedIds)
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"محاولة حذف فترة العمل: {workPeriodId}");
                            DatabaseHelper.DeleteWorkPeriod(workPeriodId);
                            successCount++;
                            System.Diagnostics.Debug.WriteLine($"تم حذف فترة العمل بنجاح: {workPeriodId}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"خطأ في حذف فترة العمل {workPeriodId}: {ex.Message}");
                            errorCount++;
                        }
                    }

                    // عرض النتيجة
                    string message = $"تم حذف {successCount} فترة عمل بنجاح";
                    if (errorCount > 0)
                        message += $"\nفشل في حذف {errorCount} فترة";

                    MessageBox.Show(message, "نتيجة العملية",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // تحديث البيانات
                    LoadWorkPeriods();
                    ClearFields();

                    // إلغاء تحديد "تحديد الكل"
                    chkSelectAll.CheckedChanged -= chkSelectAll_CheckedChanged;
                    chkSelectAll.Checked = false;
                    chkSelectAll.CheckedChanged += chkSelectAll_CheckedChanged;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف فترات العمل المحددة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #region البحث المتقدم بالمجموعات

        private void LoadWorkGroups()
        {
            try
            {
                cmbSearchGroup.Items.Clear();
                cmbSearchGroup.Items.Add("جميع المجموعات");

                var groups = DatabaseHelper.GetAllWorkGroups();
                foreach (DataRow row in groups.Rows)
                {
                    string groupName = row["اسم المجموعة"].ToString();
                    cmbSearchGroup.Items.Add(groupName);
                }

                cmbSearchGroup.SelectedIndex = 0; // جميع المجموعات
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل المجموعات: {ex.Message}");
            }
        }

        private void SetDefaultSearchDates()
        {
            // تعيين تاريخ البحث للشهر الحالي
            DateTime now = DateTime.Now;
            dateTimePickerSearchStart.Value = new DateTime(now.Year, now.Month, 1);
            dateTimePickerSearchEnd.Value = dateTimePickerSearchStart.Value.AddMonths(1).AddDays(-1);
        }

        private void btnAdvancedSearch_Click(object sender, EventArgs e)
        {
            try
            {
                string selectedGroup = cmbSearchGroup.SelectedItem?.ToString();
                DateTime searchStartDate = dateTimePickerSearchStart.Value;
                DateTime searchEndDate = dateTimePickerSearchEnd.Value;

                System.Diagnostics.Debug.WriteLine($"بحث متقدم: المجموعة={selectedGroup}, من={searchStartDate:yyyy-MM-dd}, إلى={searchEndDate:yyyy-MM-dd}");

                DataTable workPeriods;

                if (selectedGroup == "جميع المجموعات" || string.IsNullOrEmpty(selectedGroup))
                {
                    // البحث في جميع فترات العمل ضمن النطاق الزمني
                    workPeriods = DatabaseHelper.GetAllWorkPeriods(searchStartDate, searchEndDate);
                }
                else
                {
                    // البحث في فترات العمل للمجموعة المحددة ضمن النطاق الزمني
                    workPeriods = DatabaseHelper.GetWorkPeriodsByGroup(selectedGroup, searchStartDate, searchEndDate);
                }

                dataGridViewWorkPeriods.DataSource = workPeriods;

                // تنسيق الأعمدة
                if (dataGridViewWorkPeriods.Columns.Count > 0)
                {
                    foreach (DataGridViewColumn column in dataGridViewWorkPeriods.Columns)
                    {
                        column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    }
                }

                // عرض نتائج البحث
                int resultCount = workPeriods.Rows.Count;
                string message = $"تم العثور على {resultCount} فترة عمل";
                if (selectedGroup != "جميع المجموعات")
                    message += $" للمجموعة: {selectedGroup}";
                message += $" في الفترة من {searchStartDate:dd/MM/yyyy} إلى {searchEndDate:dd/MM/yyyy}";

                System.Diagnostics.Debug.WriteLine(message);

                // إظهار رسالة للمستخدم
                if (resultCount == 0)
                {
                    MessageBox.Show("لم يتم العثور على فترات عمل تطابق معايير البحث", "نتيجة البحث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show(message, "نتيجة البحث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث المتقدم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDailyTracking_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewWorkPeriods.SelectedRows.Count == 0)
                {
                    MessageBox.Show("الرجاء اختيار فترة عمل للتتبع اليومي", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = dataGridViewWorkPeriods.SelectedRows[0];

                // الحصول على معلومات فترة العمل
                int workPeriodId = Convert.ToInt32(selectedRow.Cells["المعرف"].Value);
                string projectName = selectedRow.Cells["مكان العمل"].Value.ToString();
                DateTime startDate = DateTime.Parse(selectedRow.Cells["تاريخ البداية"].Value.ToString());
                DateTime endDate = DateTime.Parse(selectedRow.Cells["تاريخ النهاية"].Value.ToString());

                // فتح نموذج التتبع اليومي
                var trackingForm = new DailyWorkTrackingForm(workPeriodId, startDate, endDate, projectName);
                trackingForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح التتبع اليومي: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
