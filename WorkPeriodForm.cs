using System;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using System.Collections.Generic;

namespace EmployeeManagementSystem
{
    public partial class WorkPeriodForm : Form
    {
        private int selectedWorkPeriodId = 0;
        private int selectedEmployeeCode = 0;
        private string selectedEmployeeName = "";

        public WorkPeriodForm()
        {
            InitializeComponent();
        }

        private void WorkPeriodForm_Load(object sender, EventArgs e)
        {
            LoadEmployees();
            LoadWorkPeriods();
            SetDefaultValues();
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = DatabaseHelper.GetEmployeesForAttendance();
                cmbEmployee.Items.Clear();
                cmbEmployee.Items.Add("اختر موظف");
                
                foreach (DataRow row in employees.Rows)
                {
                    string employeeInfo = $"{row["Name"]} - {row["EmployeeCode"]}";
                    cmbEmployee.Items.Add(employeeInfo);
                }
                
                cmbEmployee.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadWorkPeriods()
        {
            try
            {
                DataTable workPeriods;

                if (selectedEmployeeCode > 0)
                {
                    // استخدام نطاق تاريخ واسع لعرض جميع فترات الموظف
                    DateTime startDate = DateTime.Today.AddYears(-1);
                    DateTime endDate = DateTime.Today.AddYears(1);
                    workPeriods = DatabaseHelper.GetWorkPeriodsByEmployee(selectedEmployeeCode, startDate, endDate);
                }
                else
                {
                    // استخدام نطاق تاريخ واسع لعرض جميع الفترات
                    DateTime startDate = DateTime.Today.AddYears(-1);
                    DateTime endDate = DateTime.Today.AddYears(1);
                    workPeriods = DatabaseHelper.GetAllWorkPeriods(startDate, endDate);
                }

                dataGridViewWorkPeriods.DataSource = workPeriods;

                // تنسيق الأعمدة
                if (dataGridViewWorkPeriods.Columns.Count > 0)
                {
                    foreach (DataGridViewColumn column in dataGridViewWorkPeriods.Columns)
                    {
                        column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل فترات العمل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetDefaultValues()
        {
            dateTimePickerStart.Value = DateTime.Today;
            dateTimePickerEnd.Value = DateTime.Today.AddDays(30);
            cmbStatus.SelectedIndex = 0; // نشط
            numericUpDownDailyHours.Value = 8;
            
            // تحديد أيام العمل الافتراضية (الأحد إلى الخميس)
            chkSunday.Checked = true;
            chkMonday.Checked = true;
            chkTuesday.Checked = true;
            chkWednesday.Checked = true;
            chkThursday.Checked = true;
            chkFriday.Checked = false;
            chkSaturday.Checked = false;
        }

        private void cmbEmployee_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbEmployee.SelectedIndex > 0)
            {
                string selectedText = cmbEmployee.SelectedItem.ToString();
                string[] parts = selectedText.Split('-');
                
                if (parts.Length >= 2)
                {
                    selectedEmployeeName = parts[0].Trim();
                    selectedEmployeeCode = int.Parse(parts[1].Trim());
                }
            }
            else
            {
                selectedEmployeeCode = 0;
                selectedEmployeeName = "";
            }
            
            LoadWorkPeriods();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                var workPeriod = CreateWorkPeriodFromInput();
                DatabaseHelper.AddWorkPeriod(workPeriod);
                
                MessageBox.Show("تم إضافة فترة العمل بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                LoadWorkPeriods();
                ClearFields();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة فترة العمل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            if (selectedWorkPeriodId <= 0)
            {
                MessageBox.Show("الرجاء اختيار فترة عمل للتحديث", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!ValidateInput())
                return;

            try
            {
                var workPeriod = CreateWorkPeriodFromInput();
                workPeriod.WorkPeriodId = selectedWorkPeriodId;
                DatabaseHelper.UpdateWorkPeriod(workPeriod);
                
                MessageBox.Show("تم تحديث فترة العمل بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                LoadWorkPeriods();
                ClearFields();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث فترة العمل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (selectedWorkPeriodId <= 0)
            {
                MessageBox.Show("الرجاء اختيار فترة عمل للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف فترة العمل المحددة؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    DatabaseHelper.DeleteWorkPeriod(selectedWorkPeriodId);
                    MessageBox.Show("تم حذف فترة العمل بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    
                    LoadWorkPeriods();
                    ClearFields();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف فترة العمل: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearFields();
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                // استخدام نطاق تاريخ واسع للبحث
                DateTime startDate = DateTime.Today.AddYears(-1);
                DateTime endDate = DateTime.Today.AddYears(1);
                DataTable workPeriods = DatabaseHelper.GetAllWorkPeriods(startDate, endDate);

                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    if (workPeriods.Rows.Count > 0)
                    {
                        var filteredRows = workPeriods.AsEnumerable()
                            .Where(row =>
                            {
                                try
                                {
                                    string employeeName = row.Field<string>("اسم الموظف") ?? "";
                                    string workPlace = row.Field<string>("مكان العمل") ?? "";

                                    return employeeName.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase) ||
                                           workPlace.Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase);
                                }
                                catch
                                {
                                    return false;
                                }
                            });

                        if (filteredRows.Any())
                        {
                            workPeriods = filteredRows.CopyToDataTable();
                        }
                        else
                        {
                            workPeriods.Clear();
                        }
                    }
                }

                dataGridViewWorkPeriods.DataSource = workPeriods;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadWorkPeriods();
            txtSearch.Clear();
        }

        private void dataGridViewWorkPeriods_SelectionChanged(object sender, EventArgs e)
        {
            if (dataGridViewWorkPeriods.SelectedRows.Count > 0)
            {
                try
                {
                    var selectedRow = dataGridViewWorkPeriods.SelectedRows[0];
                    selectedWorkPeriodId = Convert.ToInt32(selectedRow.Cells["المعرف"].Value);
                    
                    var workPeriod = DatabaseHelper.GetWorkPeriodById(selectedWorkPeriodId);
                    if (workPeriod != null)
                    {
                        LoadWorkPeriodToForm(workPeriod);
                        UpdateSummaryLabels(workPeriod);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل بيانات فترة العمل: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
            {
                selectedWorkPeriodId = 0;
                ResetSummaryLabels();
            }
        }

        private bool ValidateInput()
        {
            if (selectedEmployeeCode <= 0)
            {
                MessageBox.Show("الرجاء اختيار موظف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtProjectName.Text))
            {
                MessageBox.Show("الرجاء إدخال مكان العمل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (dateTimePickerEnd.Value <= dateTimePickerStart.Value)
            {
                MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (!GetSelectedWorkingDays().Any())
            {
                MessageBox.Show("الرجاء اختيار يوم واحد على الأقل من أيام العمل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private WorkPeriod CreateWorkPeriodFromInput()
        {
            return new WorkPeriod
            {
                EmployeeCode = selectedEmployeeCode,
                EmployeeName = selectedEmployeeName,
                ProjectName = txtProjectName.Text.Trim(),
                Description = txtDescription.Text.Trim(),
                StartDate = dateTimePickerStart.Value.Date,
                EndDate = dateTimePickerEnd.Value.Date,
                WorkingDays = string.Join(",", GetSelectedWorkingDays()),
                DailyWorkingHours = (double)numericUpDownDailyHours.Value,
                Status = cmbStatus.SelectedItem?.ToString() ?? "نشط"
            };
        }

        private List<string> GetSelectedWorkingDays()
        {
            var selectedDays = new List<string>();
            
            if (chkSunday.Checked) selectedDays.Add("الأحد");
            if (chkMonday.Checked) selectedDays.Add("الاثنين");
            if (chkTuesday.Checked) selectedDays.Add("الثلاثاء");
            if (chkWednesday.Checked) selectedDays.Add("الأربعاء");
            if (chkThursday.Checked) selectedDays.Add("الخميس");
            if (chkFriday.Checked) selectedDays.Add("الجمعة");
            if (chkSaturday.Checked) selectedDays.Add("السبت");
            
            return selectedDays;
        }

        private void LoadWorkPeriodToForm(WorkPeriod workPeriod)
        {
            // تحديد الموظف
            for (int i = 1; i < cmbEmployee.Items.Count; i++)
            {
                string item = cmbEmployee.Items[i].ToString();
                if (item.Contains($"- {workPeriod.EmployeeCode}"))
                {
                    cmbEmployee.SelectedIndex = i;
                    break;
                }
            }

            txtProjectName.Text = workPeriod.ProjectName ?? "";
            txtDescription.Text = workPeriod.Description ?? "";
            dateTimePickerStart.Value = workPeriod.StartDate;
            dateTimePickerEnd.Value = workPeriod.EndDate;
            numericUpDownDailyHours.Value = (decimal)workPeriod.DailyWorkingHours;

            // تحديد الحالة
            if (cmbStatus.Items.Contains(workPeriod.Status))
            {
                cmbStatus.SelectedItem = workPeriod.Status;
            }

            // تحديد أيام العمل
            SetWorkingDaysFromString(workPeriod.WorkingDays ?? "");
        }

        private void SetWorkingDaysFromString(string workingDays)
        {
            // إعادة تعيين جميع الأيام
            chkSunday.Checked = false;
            chkMonday.Checked = false;
            chkTuesday.Checked = false;
            chkWednesday.Checked = false;
            chkThursday.Checked = false;
            chkFriday.Checked = false;
            chkSaturday.Checked = false;

            if (string.IsNullOrEmpty(workingDays))
                return;

            var days = workingDays.Split(',').Select(d => d.Trim()).ToList();

            if (days.Contains("الأحد")) chkSunday.Checked = true;
            if (days.Contains("الاثنين")) chkMonday.Checked = true;
            if (days.Contains("الثلاثاء")) chkTuesday.Checked = true;
            if (days.Contains("الأربعاء")) chkWednesday.Checked = true;
            if (days.Contains("الخميس")) chkThursday.Checked = true;
            if (days.Contains("الجمعة")) chkFriday.Checked = true;
            if (days.Contains("السبت")) chkSaturday.Checked = true;
        }

        private void UpdateSummaryLabels(WorkPeriod workPeriod)
        {
            lblSelectedPeriod.Text = $"الفترة: {workPeriod.ProjectName}";
            lblTotalDays.Text = $"إجمالي الأيام: {workPeriod.GetTotalWorkingDays()}";
            lblTotalHours.Text = $"إجمالي الساعات: {workPeriod.GetTotalExpectedHours():F1}";
        }

        private void ResetSummaryLabels()
        {
            lblSelectedPeriod.Text = "لم يتم اختيار فترة";
            lblTotalDays.Text = "إجمالي الأيام: 0";
            lblTotalHours.Text = "إجمالي الساعات: 0";
        }

        private void ClearFields()
        {
            selectedWorkPeriodId = 0;
            cmbEmployee.SelectedIndex = 0;
            txtProjectName.Clear();
            txtDescription.Clear();
            SetDefaultValues();
            ResetSummaryLabels();
        }

        private void dataGridViewWorkPeriods_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            // منع ظهور رسالة الخطأ الافتراضية
            e.Cancel = true;

            // تسجيل الخطأ للتشخيص
            System.Diagnostics.Debug.WriteLine($"DataGridView Error: {e.Exception?.Message} at Row: {e.RowIndex}, Column: {e.ColumnIndex}");

            // يمكن إضافة معالجة خاصة هنا إذا لزم الأمر
        }

        private void monthCalendar_DateChanged(object sender, DateRangeEventArgs e)
        {
            try
            {
                // تحديث أيام العمل بناءً على التقويم المحدد
                UpdateWorkingDaysFromCalendar();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث التقويم: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateWorkingDaysFromCalendar()
        {
            try
            {
                // مسح جميع أيام العمل أولاً
                chkSunday.Checked = false;
                chkMonday.Checked = false;
                chkTuesday.Checked = false;
                chkWednesday.Checked = false;
                chkThursday.Checked = false;
                chkFriday.Checked = false;
                chkSaturday.Checked = false;

                // الحصول على التواريخ المحددة في التقويم
                DateTime startDate = monthCalendar.SelectionStart;
                DateTime endDate = monthCalendar.SelectionEnd;

                // تحديد أيام العمل بناءً على التواريخ المحددة
                for (DateTime date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    switch (date.DayOfWeek)
                    {
                        case DayOfWeek.Sunday:
                            chkSunday.Checked = true;
                            break;
                        case DayOfWeek.Monday:
                            chkMonday.Checked = true;
                            break;
                        case DayOfWeek.Tuesday:
                            chkTuesday.Checked = true;
                            break;
                        case DayOfWeek.Wednesday:
                            chkWednesday.Checked = true;
                            break;
                        case DayOfWeek.Thursday:
                            chkThursday.Checked = true;
                            break;
                        case DayOfWeek.Friday:
                            chkFriday.Checked = true;
                            break;
                        case DayOfWeek.Saturday:
                            chkSaturday.Checked = true;
                            break;
                    }
                }

                // تحديث تواريخ البداية والنهاية
                dateTimePickerStart.Value = startDate;
                dateTimePickerEnd.Value = endDate;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث أيام العمل من التقويم: {ex.Message}");
            }
        }

        private void chkSelectAll_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                if (chkSelectAll.Checked)
                {
                    // تحديد جميع الصفوف
                    dataGridViewWorkPeriods.SelectAll();
                }
                else
                {
                    // إلغاء تحديد جميع الصفوف
                    dataGridViewWorkPeriods.ClearSelection();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديد الصفوف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDeleteSelected_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewWorkPeriods.SelectedRows.Count == 0)
                {
                    MessageBox.Show("الرجاء اختيار فترة عمل واحدة أو أكثر للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                int selectedCount = dataGridViewWorkPeriods.SelectedRows.Count;
                var result = MessageBox.Show($"هل أنت متأكد من حذف {selectedCount} فترة عمل محددة؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    int successCount = 0;
                    int errorCount = 0;
                    var selectedIds = new List<int>();

                    // جمع معرفات فترات العمل المحددة
                    foreach (DataGridViewRow row in dataGridViewWorkPeriods.SelectedRows)
                    {
                        try
                        {
                            if (row.Cells["المعرف"] != null &&
                                int.TryParse(row.Cells["المعرف"].Value?.ToString(), out int workPeriodId))
                            {
                                selectedIds.Add(workPeriodId);
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"خطأ في قراءة معرف فترة العمل: {ex.Message}");
                            errorCount++;
                        }
                    }

                    // حذف فترات العمل
                    foreach (int workPeriodId in selectedIds)
                    {
                        try
                        {
                            DatabaseHelper.DeleteWorkPeriod(workPeriodId);
                            successCount++;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"خطأ في حذف فترة العمل {workPeriodId}: {ex.Message}");
                            errorCount++;
                        }
                    }

                    // عرض النتيجة
                    string message = $"تم حذف {successCount} فترة عمل بنجاح";
                    if (errorCount > 0)
                        message += $"\nفشل في حذف {errorCount} فترة";

                    MessageBox.Show(message, "نتيجة العملية",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // تحديث البيانات
                    LoadWorkPeriods();
                    ClearFields();

                    // إلغاء تحديد "تحديد الكل"
                    chkSelectAll.Checked = false;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف فترات العمل المحددة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
