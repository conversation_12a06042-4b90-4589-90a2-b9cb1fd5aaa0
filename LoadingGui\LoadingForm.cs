﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DocumentFormat.OpenXml.Spreadsheet;

namespace EmployeeManagementSystem.LoadingGui
{
    public partial class LoadingForm : Form
    {
        private static LoadingForm? loadingForm;

        public LoadingForm(Form ownerForm)
        {
            InitializeComponent();
            this.Owner = ownerForm;
            ThemeManager.ApplyThemeToForm(this);
        }


        public static LoadingForm Instance(Form ownerForm)
        {
            return loadingForm ??= new LoadingForm(ownerForm);
        }

    }
}
