using System;
using System.Data;
using System.Linq;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class AttendanceForm : Form
    {
        private int selectedEmployeeCode = 0;
        private string selectedEmployeeName = "";

        public AttendanceForm()
        {
            InitializeComponent();
        }

        private void AttendanceForm_Load(object sender, EventArgs e)
        {
            LoadEmployees();
            LoadAttendanceData();
            UpdateCurrentDateTime();
            
            // تعيين التواريخ الافتراضية
            dateTimePickerStart.Value = DateTime.Today.AddDays(-30);
            dateTimePickerEnd.Value = DateTime.Today;
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = DatabaseHelper.GetEmployeesForAttendance();
                cmbEmployee.Items.Clear();
                cmbEmployee.Items.Add("اختر موظف");

                foreach (DataRow row in employees.Rows)
                {
                    string employeeInfo = $"{row["Name"]} - {row["EmployeeCode"]}";
                    cmbEmployee.Items.Add(employeeInfo);
                }

                cmbEmployee.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadAttendanceData()
        {
            try
            {
                DataTable attendanceData;
                
                if (selectedEmployeeCode > 0)
                {
                    attendanceData = DatabaseHelper.GetAttendanceByEmployee(selectedEmployeeCode);
                }
                else
                {
                    attendanceData = DatabaseHelper.GetAttendanceByDateRange(
                        dateTimePickerStart.Value, dateTimePickerEnd.Value);
                }
                
                dataGridViewAttendance.DataSource = attendanceData;
                
                // تنسيق الأعمدة
                if (dataGridViewAttendance.Columns.Count > 0)
                {
                    foreach (DataGridViewColumn column in dataGridViewAttendance.Columns)
                    {
                        column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الحضور: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateCurrentDateTime()
        {
            lblCurrentDate.Text = DateTime.Now.ToString("yyyy/MM/dd");
            lblCurrentTime.Text = DateTime.Now.ToString("HH:mm:ss");
        }

        private void UpdateTodayAttendanceInfo()
        {
            if (selectedEmployeeCode <= 0)
            {
                lblEmployeeName.Text = "اختر موظف أولاً";
                lblCheckInTime.Text = "وقت الحضور: -";
                lblCheckOutTime.Text = "وقت الانصراف: -";
                lblStatus.Text = "الحالة: -";
                lblWorkingHours.Text = "ساعات العمل: -";
                lblOvertimeHours.Text = "الساعات الإضافية: -";
                return;
            }

            try
            {
                var todayAttendance = DatabaseHelper.GetTodayAttendance(selectedEmployeeCode);
                lblEmployeeName.Text = selectedEmployeeName;
                
                if (todayAttendance != null)
                {
                    lblCheckInTime.Text = $"وقت الحضور: {(todayAttendance.CheckInTime?.ToString("HH:mm") ?? "-")}";
                    lblCheckOutTime.Text = $"وقت الانصراف: {(todayAttendance.CheckOutTime?.ToString("HH:mm") ?? "-")}";
                    lblStatus.Text = $"الحالة: {todayAttendance.Status}";
                    lblWorkingHours.Text = $"ساعات العمل: {todayAttendance.WorkingHours:F2} ساعة";
                    lblOvertimeHours.Text = $"الساعات الإضافية: {todayAttendance.OvertimeHours:F2} ساعة";
                    
                    // تلوين الحالة
                    switch (todayAttendance.Status)
                    {
                        case "حاضر":
                            lblStatus.ForeColor = System.Drawing.Color.Green;
                            break;
                        case "متأخر":
                            lblStatus.ForeColor = System.Drawing.Color.Orange;
                            break;
                        case "غائب":
                            lblStatus.ForeColor = System.Drawing.Color.Red;
                            break;
                        default:
                            lblStatus.ForeColor = System.Drawing.Color.Black;
                            break;
                    }
                }
                else
                {
                    lblCheckInTime.Text = "وقت الحضور: -";
                    lblCheckOutTime.Text = "وقت الانصراف: -";
                    lblStatus.Text = "الحالة: لم يسجل حضور";
                    lblWorkingHours.Text = "ساعات العمل: -";
                    lblOvertimeHours.Text = "الساعات الإضافية: -";
                    lblStatus.ForeColor = System.Drawing.Color.Gray;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث معلومات الحضور: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void cmbEmployee_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbEmployee.SelectedIndex > 0)
            {
                string selectedText = cmbEmployee.SelectedItem.ToString();
                string[] parts = selectedText.Split('-');
                
                if (parts.Length >= 2)
                {
                    selectedEmployeeName = parts[0].Trim();
                    selectedEmployeeCode = int.Parse(parts[1].Trim());
                }
            }
            else
            {
                selectedEmployeeCode = 0;
                selectedEmployeeName = "";
            }
            
            UpdateTodayAttendanceInfo();
            LoadAttendanceData();
        }

        private void btnCheckIn_Click(object sender, EventArgs e)
        {
            if (selectedEmployeeCode <= 0)
            {
                MessageBox.Show("الرجاء اختيار موظف أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                bool success = DatabaseHelper.CheckIn(selectedEmployeeCode, selectedEmployeeName);
                
                if (success)
                {
                    MessageBox.Show("تم تسجيل الحضور بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    UpdateTodayAttendanceInfo();
                    LoadAttendanceData();
                }
                else
                {
                    MessageBox.Show("الموظف سجل حضوره بالفعل اليوم", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الحضور: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCheckOut_Click(object sender, EventArgs e)
        {
            if (selectedEmployeeCode <= 0)
            {
                MessageBox.Show("الرجاء اختيار موظف أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                bool success = DatabaseHelper.CheckOut(selectedEmployeeCode);
                
                if (success)
                {
                    // إضافة الملاحظات إذا كانت موجودة
                    if (!string.IsNullOrWhiteSpace(txtNotes.Text))
                    {
                        var todayAttendance = DatabaseHelper.GetTodayAttendance(selectedEmployeeCode);
                        if (todayAttendance != null)
                        {
                            todayAttendance.Notes = txtNotes.Text;
                            DatabaseHelper.UpdateAttendance(todayAttendance);
                        }
                    }
                    
                    MessageBox.Show("تم تسجيل الانصراف بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    UpdateTodayAttendanceInfo();
                    LoadAttendanceData();
                    txtNotes.Clear();
                }
                else
                {
                    MessageBox.Show("لم يتم تسجيل حضور الموظف اليوم أو تم تسجيل الانصراف بالفعل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الانصراف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable attendanceData;
                
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    // البحث بالاسم
                    attendanceData = DatabaseHelper.GetAttendanceByDateRange(
                        dateTimePickerStart.Value, dateTimePickerEnd.Value);
                    
                    // تصفية النتائج بالاسم
                    var filteredRows = attendanceData.AsEnumerable()
                        .Where(row => row.Field<string>("اسم الموظف")
                            .Contains(txtSearch.Text, StringComparison.OrdinalIgnoreCase));
                    
                    if (filteredRows.Any())
                    {
                        attendanceData = filteredRows.CopyToDataTable();
                    }
                    else
                    {
                        attendanceData.Clear();
                    }
                }
                else
                {
                    // البحث بالتاريخ فقط
                    attendanceData = DatabaseHelper.GetAttendanceByDateRange(
                        dateTimePickerStart.Value, dateTimePickerEnd.Value);
                }
                
                dataGridViewAttendance.DataSource = attendanceData;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadAttendanceData();
            UpdateTodayAttendanceInfo();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            UpdateCurrentDateTime();
        }
    }
}
