using System;
using System.Data;
using System.Linq;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class AttendanceForm : Form
    {
        private int selectedEmployeeCode = 0;
        private string selectedEmployeeName = "";
        private int selectedWorkPeriodId = 0;
        private WorkPeriod? selectedWorkPeriod = null;

        public AttendanceForm()
        {
            InitializeComponent();
        }

        private void AttendanceForm_Load(object sender, EventArgs e)
        {
            LoadEmployees();
            LoadAttendanceData();
            UpdateCurrentDateTime();
            InitializeAttendanceStatus();

            // تعيين التواريخ الافتراضية
            dateTimePickerStart.Value = DateTime.Today.AddDays(-30);
            dateTimePickerEnd.Value = DateTime.Today;
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = DatabaseHelper.GetEmployeesForAttendance();
                cmbEmployee.Items.Clear();
                cmbEmployee.Items.Add("اختر موظف");

                foreach (DataRow row in employees.Rows)
                {
                    string employeeInfo = $"{row["Name"]}";
                    cmbEmployee.Items.Add(employeeInfo);
                }

                cmbEmployee.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadAttendanceData()
        {
            try
            {
                DataTable attendanceData;
                
                if (selectedEmployeeCode > 0)
                {
                    attendanceData = DatabaseHelper.GetAttendanceByEmployee(selectedEmployeeCode);
                }
                else
                {
                    attendanceData = DatabaseHelper.GetAttendanceByDateRange(
                        dateTimePickerStart.Value, dateTimePickerEnd.Value);
                }
                
                dataGridViewAttendance.DataSource = attendanceData;

                // تنسيق الأعمدة
                if (dataGridViewAttendance.Columns.Count > 0)
                {
                    foreach (DataGridViewColumn column in dataGridViewAttendance.Columns)
                    {
                        column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    }

                    // تنسيق ساعات العمل والساعات الإضافية
                    FormatWorkingHoursColumns();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الحضور: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateCurrentDateTime()
        {
            lblCurrentDate.Text = DateTime.Now.ToString("yyyy/MM/dd");
            lblCurrentTime.Text = DateTime.Now.ToString("HH:mm:ss");
        }

        private void UpdateTodayAttendanceInfo()
        {
            if (selectedEmployeeCode <= 0)
            {
                lblEmployeeName.Text = "اختر موظف أولاً";
                lblCheckInTime.Text = "وقت الحضور: -";
                lblCheckOutTime.Text = "وقت الانصراف: -";
                lblStatus.Text = "الحالة: -";
                lblWorkingHours.Text = "ساعات العمل: -";
                lblOvertimeHours.Text = "الساعات الإضافية: -";
                return;
            }

            try
            {
                var todayAttendance = DatabaseHelper.GetTodayAttendance(selectedEmployeeCode);
                lblEmployeeName.Text = selectedEmployeeName;
                
                if (todayAttendance != null)
                {
                    lblCheckInTime.Text = $"وقت الحضور: {(todayAttendance.CheckInTime?.ToString("HH:mm") ?? "-")}";
                    lblCheckOutTime.Text = $"وقت الانصراف: {(todayAttendance.CheckOutTime?.ToString("HH:mm") ?? "-")}";
                    lblStatus.Text = $"الحالة: {todayAttendance.Status}";
                    lblWorkingHours.Text = $"ساعات العمل: {FormatHoursDisplay(todayAttendance.WorkingHours)}";
                    lblOvertimeHours.Text = $"الساعات الإضافية: {FormatHoursDisplay(todayAttendance.OvertimeHours)}";
                    
                    // تلوين الحالة
                    switch (todayAttendance.Status)
                    {
                        case "حاضر":
                            lblStatus.ForeColor = System.Drawing.Color.Green;
                            break;
                        case "متأخر":
                            lblStatus.ForeColor = System.Drawing.Color.Orange;
                            break;
                        case "غائب":
                            lblStatus.ForeColor = System.Drawing.Color.Red;
                            break;
                        default:
                            lblStatus.ForeColor = System.Drawing.Color.Black;
                            break;
                    }

                    // تحديث قائمة الحالة لتعكس الحالة الحالية
                    UpdateAttendanceStatusComboBox(todayAttendance.Status);

                    // تحديث حقل الساعات الإضافية
                    UpdateOvertimeHoursInput(todayAttendance.OvertimeHours);
                }
                else
                {
                    lblCheckInTime.Text = "وقت الحضور: -";
                    lblCheckOutTime.Text = "وقت الانصراف: -";
                    lblStatus.Text = "الحالة: لم يسجل حضور";
                    lblWorkingHours.Text = "ساعات العمل: -";
                    lblOvertimeHours.Text = "الساعات الإضافية: -";
                    lblStatus.ForeColor = System.Drawing.Color.Gray;

                    // تعيين الحالة الافتراضية
                    UpdateAttendanceStatusComboBox("حاضر");

                    // تعيين الساعات الإضافية الافتراضية
                    UpdateOvertimeHoursInput(0);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث معلومات الحضور: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void cmbEmployee_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbEmployee.SelectedIndex > 0)
            {
                string selectedEmployeeName = cmbEmployee.SelectedItem.ToString();

                // البحث عن الموظف بالاسم للحصول على الكود
                var employees = DatabaseHelper.GetEmployeesForAttendance();
                foreach (DataRow row in employees.Rows)
                {
                    if (row["Name"].ToString() == selectedEmployeeName)
                    {
                        this.selectedEmployeeName = selectedEmployeeName;
                        this.selectedEmployeeCode = Convert.ToInt32(row["EmployeeCode"]);
                        break;
                    }
                }
            }
            else
            {
                selectedEmployeeCode = 0;
                selectedEmployeeName = "";
            }

            LoadWorkPeriods();
            UpdateTodayAttendanceInfo();
            LoadAttendanceData();
        }

        private void LoadWorkPeriods()
        {
            try
            {
                cmbWorkPeriod.Items.Clear();
                cmbWorkPeriod.Items.Add("حضور عادي (بدون فترة محددة)");

                if (selectedEmployeeCode > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"تحميل فترات العمل للموظف: {selectedEmployeeCode}");

                    var activeWorkPeriods = DatabaseHelper.GetActiveWorkPeriods(selectedEmployeeCode);
                    System.Diagnostics.Debug.WriteLine($"تم العثور على {activeWorkPeriods?.Count ?? 0} فترة عمل نشطة");

                    if (activeWorkPeriods != null && activeWorkPeriods.Count > 0)
                    {
                        int validPeriodsCount = 0;
                        foreach (var workPeriod in activeWorkPeriods)
                        {
                            try
                            {
                                if (workPeriod != null)
                                {
                                    System.Diagnostics.Debug.WriteLine($"فترة العمل: {workPeriod.ProjectName} من {workPeriod.StartDate:yyyy-MM-dd} إلى {workPeriod.EndDate:yyyy-MM-dd}");

                                    // التحقق من أن الفترة نشطة (تشمل اليوم الحالي أو المستقبل)
                                    if (workPeriod.EndDate >= DateTime.Today)
                                    {
                                        string projectName = workPeriod.ProjectName ?? "مكان عمل غير محدد";
                                        string status = workPeriod.Status ?? "غير محدد";
                                        string periodInfo = $"{projectName} - {status} ({workPeriod.StartDate:dd/MM/yyyy} - {workPeriod.EndDate:dd/MM/yyyy})";
                                        cmbWorkPeriod.Items.Add(periodInfo);
                                        validPeriodsCount++;

                                        System.Diagnostics.Debug.WriteLine($"تمت إضافة فترة العمل: {periodInfo}");
                                    }
                                    else
                                    {
                                        System.Diagnostics.Debug.WriteLine($"فترة العمل منتهية: {workPeriod.ProjectName}");
                                    }
                                }
                            }
                            catch (Exception innerEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"خطأ في معالجة فترة العمل: {innerEx.Message}");
                                continue;
                            }
                        }

                        System.Diagnostics.Debug.WriteLine($"تم إضافة {validPeriodsCount} فترة عمل صالحة");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("لا توجد فترات عمل نشطة للموظف");
                    }
                }

                cmbWorkPeriod.SelectedIndex = 0;
                System.Diagnostics.Debug.WriteLine($"إجمالي العناصر في cmbWorkPeriod: {cmbWorkPeriod.Items.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل فترات العمل: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل فترات العمل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);

                // في حالة خطأ، تأكد من وجود العنصر الافتراضي على الأقل
                if (cmbWorkPeriod.Items.Count == 0)
                {
                    cmbWorkPeriod.Items.Add("حضور عادي (بدون فترة محددة)");
                }
                cmbWorkPeriod.SelectedIndex = 0;
            }
        }

        private void cmbWorkPeriod_SelectedIndexChanged(object sender, EventArgs e)
        {
            selectedWorkPeriodId = 0;
            selectedWorkPeriod = null;

            if (cmbWorkPeriod.SelectedIndex > 0 && selectedEmployeeCode > 0)
            {
                try
                {
                    var activeWorkPeriods = DatabaseHelper.GetActiveWorkPeriods(selectedEmployeeCode);
                    int periodIndex = cmbWorkPeriod.SelectedIndex - 1; // -1 لأن العنصر الأول هو "حضور عادي"

                    if (activeWorkPeriods != null && periodIndex >= 0 && periodIndex < activeWorkPeriods.Count)
                    {
                        var validWorkPeriods = activeWorkPeriods.Where(wp => wp.IsWorkingDay(DateTime.Today)).ToList();
                        if (periodIndex < validWorkPeriods.Count)
                        {
                            selectedWorkPeriod = validWorkPeriods[periodIndex];
                            selectedWorkPeriodId = selectedWorkPeriod.WorkPeriodId;
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في اختيار فترة العمل: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }

            UpdateTodayAttendanceInfo();
        }

        private void btnCheckIn_Click(object sender, EventArgs e)
        {
            if (selectedEmployeeCode <= 0)
            {
                MessageBox.Show("الرجاء اختيار موظف أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // التحقق من فترة العمل إذا كانت محددة
            if (selectedWorkPeriod != null)
            {
                if (!selectedWorkPeriod.IsWorkingDay(DateTime.Today))
                {
                    MessageBox.Show("اليوم ليس يوم عمل في الفترة المحددة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
            }

            try
            {
                // تسجيل حضور تلقائي بحت - لا يتأثر بالقوائم اليدوية
                bool success = CheckInWithWorkPeriod(selectedEmployeeCode, selectedEmployeeName, selectedWorkPeriodId);

                if (success)
                {
                    MessageBox.Show("تم تسجيل الحضور تلقائياً", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    UpdateTodayAttendanceInfo();
                    LoadAttendanceData();
                }
                else
                {
                    MessageBox.Show("الموظف سجل حضوره بالفعل اليوم", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الحضور: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCheckOut_Click(object sender, EventArgs e)
        {
            if (selectedEmployeeCode <= 0)
            {
                MessageBox.Show("الرجاء اختيار موظف أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                bool success = CheckOutWithWorkPeriod(selectedEmployeeCode, selectedEmployeeName, selectedWorkPeriodId);

                if (success)
                {
                    // إضافة الملاحظات إذا كانت موجودة
                    if (!string.IsNullOrWhiteSpace(txtNotes.Text))
                    {
                        var todayAttendance = DatabaseHelper.GetTodayAttendance(selectedEmployeeCode);
                        if (todayAttendance != null)
                        {
                            todayAttendance.Notes = txtNotes.Text;
                            DatabaseHelper.UpdateAttendance(todayAttendance);
                        }
                    }

                    MessageBox.Show("تم تسجيل الانصراف بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    UpdateTodayAttendanceInfo();
                    LoadAttendanceData();
                    txtNotes.Clear();
                }
                else
                {
                    MessageBox.Show("لم يتم تسجيل حضور الموظف اليوم أو تم تسجيل الانصراف بالفعل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الانصراف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable attendanceData;

                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    // البحث بالاسم مباشرة من قاعدة البيانات
                    attendanceData = DatabaseHelper.SearchAttendanceByName(
                        txtSearch.Text.Trim(),
                        dateTimePickerStart.Value,
                        dateTimePickerEnd.Value);

                    System.Diagnostics.Debug.WriteLine($"البحث عن: '{txtSearch.Text.Trim()}' - النتائج: {attendanceData.Rows.Count}");
                }
                else
                {
                    // البحث بالتاريخ فقط
                    attendanceData = DatabaseHelper.GetAttendanceByDateRange(
                        dateTimePickerStart.Value, dateTimePickerEnd.Value);
                }

                dataGridViewAttendance.DataSource = attendanceData;

                // تطبيق تنسيق ساعات العمل
                if (attendanceData.Rows.Count > 0)
                {
                    FormatWorkingHoursColumns();
                }

                // عرض رسالة النتائج
                if (!string.IsNullOrWhiteSpace(txtSearch.Text))
                {
                    if (attendanceData.Rows.Count == 0)
                    {
                        MessageBox.Show($"لم يتم العثور على نتائج للموظف: {txtSearch.Text}", "نتيجة البحث",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show($"تم العثور على {attendanceData.Rows.Count} سجل حضور للموظف: {txtSearch.Text}", "نتيجة البحث",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث: {ex.Message}");
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadAttendanceData();
            UpdateTodayAttendanceInfo();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            UpdateCurrentDateTime();
        }

        private bool CheckInWithWorkPeriod(int employeeCode, string employeeName, int workPeriodId)
        {
            var todayAttendance = DatabaseHelper.GetTodayAttendance(employeeCode);

            if (todayAttendance != null && todayAttendance.CheckInTime.HasValue)
            {
                return false; // الموظف سجل حضوره بالفعل اليوم
            }

            if (todayAttendance == null)
            {
                // إنشاء سجل حضور جديد - تلقائي بحت
                var attendance = new Attendance
                {
                    EmployeeCode = employeeCode,
                    EmployeeName = employeeName,
                    Date = DateTime.Today,
                    CheckInTime = DateTime.Now,
                    Status = "حاضر", // يبدأ بحاضر دائماً
                    WorkPeriodId = workPeriodId > 0 ? workPeriodId : null
                };

                // فحص التأخير تلقائياً
                TimeSpan officialStartTime = new TimeSpan(8, 0, 0); // الوقت الافتراضي
                if (selectedWorkPeriod != null)
                {
                    // يمكن إضافة وقت بداية العمل لفترة العمل لاحقاً
                    // officialStartTime = selectedWorkPeriod.StartTime;
                }

                attendance.CheckLateStatus(officialStartTime);
                attendance.Notes = "تسجيل حضور تلقائي";

                DatabaseHelper.AddAttendance(attendance);
            }
            else
            {
                // تحديث وقت الحضور - تلقائي بحت
                todayAttendance.CheckInTime = DateTime.Now;
                todayAttendance.Status = "حاضر"; // يبدأ بحاضر دائماً
                todayAttendance.WorkPeriodId = workPeriodId > 0 ? workPeriodId : null;

                // فحص التأخير تلقائياً
                TimeSpan officialStartTime = new TimeSpan(8, 0, 0);
                todayAttendance.CheckLateStatus(officialStartTime);
                todayAttendance.Notes = "تحديث حضور تلقائي";

                DatabaseHelper.UpdateAttendance(todayAttendance);
            }

            return true;
        }

        private bool CheckOutWithWorkPeriod(int employeeCode, string employeeName, int workPeriodId)
        {
            var todayAttendance = DatabaseHelper.GetTodayAttendance(employeeCode);

            if (todayAttendance == null || !todayAttendance.CheckInTime.HasValue)
            {
                return false; // لم يتم تسجيل حضور اليوم
            }

            if (todayAttendance.CheckOutTime.HasValue)
            {
                return false; // تم تسجيل الانصراف بالفعل
            }

            // تسجيل وقت الانصراف
            todayAttendance.CheckOutTime = DateTime.Now;

            // حساب ساعات العمل
            if (todayAttendance.CheckInTime.HasValue && todayAttendance.CheckOutTime.HasValue)
            {
                TimeSpan workingTime = todayAttendance.CheckOutTime.Value - todayAttendance.CheckInTime.Value;
                todayAttendance.WorkingHours = workingTime.TotalHours;

                // حساب الساعات الإضافية التلقائي (أكثر من 8 ساعات)
                double standardHours = 8.0;
                if (selectedWorkPeriod != null)
                {
                    standardHours = selectedWorkPeriod.DailyWorkingHours;
                }

                double calculatedOvertimeHours = 0;
                if (todayAttendance.WorkingHours > standardHours)
                {
                    calculatedOvertimeHours = todayAttendance.WorkingHours - standardHours;
                }

                // إذا كان هناك ساعات إضافية مدخلة يدوياً، استخدمها، وإلا استخدم المحسوبة تلقائياً
                double manualOvertimeHours = (double)numericUpDownOvertimeHours.Value;
                if (manualOvertimeHours > 0)
                {
                    todayAttendance.OvertimeHours = manualOvertimeHours;
                    todayAttendance.Notes = $"ساعات إضافية يدوية: {manualOvertimeHours} (تلقائي: {calculatedOvertimeHours:F1})";
                }
                else
                {
                    todayAttendance.OvertimeHours = calculatedOvertimeHours;
                    if (calculatedOvertimeHours > 0)
                    {
                        todayAttendance.Notes = $"ساعات إضافية تلقائية: {calculatedOvertimeHours:F1}";
                    }
                }
            }

            // تحديث فترة العمل إذا كانت محددة
            if (workPeriodId > 0)
            {
                todayAttendance.WorkPeriodId = workPeriodId;
            }

            DatabaseHelper.UpdateAttendance(todayAttendance);
            return true;
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewAttendance.SelectedRows.Count == 0)
                {
                    MessageBox.Show("الرجاء اختيار سجل حضور للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من حذف سجل الحضور المحدد؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var selectedRow = dataGridViewAttendance.SelectedRows[0];

                    // الحصول على معرف سجل الحضور
                    if (selectedRow.Cells["المعرف"] != null &&
                        int.TryParse(selectedRow.Cells["المعرف"].Value?.ToString(), out int attendanceId))
                    {
                        bool success = DatabaseHelper.DeleteAttendance(attendanceId);

                        if (success)
                        {
                            MessageBox.Show("تم حذف سجل الحضور بنجاح", "نجح",
                                MessageBoxButtons.OK, MessageBoxIcon.Information);
                            LoadAttendanceData();
                            UpdateTodayAttendanceInfo();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف سجل الحضور", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        MessageBox.Show("لا يمكن تحديد سجل الحضور للحذف", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف سجل الحضور: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dataGridViewAttendance_DataError(object sender, DataGridViewDataErrorEventArgs e)
        {
            // منع ظهور رسالة الخطأ الافتراضية
            e.Cancel = true;

            // تسجيل الخطأ للتشخيص
            System.Diagnostics.Debug.WriteLine($"DataGridView Error: {e.Exception?.Message} at Row: {e.RowIndex}, Column: {e.ColumnIndex}");

            // يمكن إضافة معالجة خاصة هنا إذا لزم الأمر
        }

        private void dataGridViewAttendance_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0 && dataGridViewAttendance.Rows[e.RowIndex].Cells["كود الموظف"].Value != null)
                {
                    // الحصول على كود الموظف من الصف المحدد
                    if (int.TryParse(dataGridViewAttendance.Rows[e.RowIndex].Cells["كود الموظف"].Value.ToString(), out int employeeCode))
                    {
                        // تحديث الموظف المحدد
                        selectedEmployeeCode = employeeCode;
                        selectedEmployeeName = dataGridViewAttendance.Rows[e.RowIndex].Cells["اسم الموظف"].Value?.ToString() ?? "";

                        // تحديث قائمة الموظفين لتظهر الموظف المحدد
                        for (int i = 0; i < cmbEmployee.Items.Count; i++)
                        {
                            string item = cmbEmployee.Items[i].ToString();
                            if (item.Contains($"- {employeeCode}"))
                            {
                                cmbEmployee.SelectedIndex = i;
                                break;
                            }
                        }

                        // تحديث معلومات اليوم وفترات العمل
                        LoadWorkPeriods();
                        UpdateTodayAttendanceInfo();

                        System.Diagnostics.Debug.WriteLine($"تم تحديد الموظف من الجدول: {selectedEmployeeName} ({selectedEmployeeCode})");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديد الموظف من الجدول: {ex.Message}");
            }
        }

        private string FormatHoursDisplay(double totalHours)
        {
            if (totalHours <= 0)
                return "0";

            // تقريب إلى منزلة عشرية واحدة
            totalHours = Math.Round(totalHours, 1);

            // إذا كان رقم صحيح، اعرضه بدون منازل عشرية
            if (totalHours == Math.Floor(totalHours))
                return totalHours.ToString("F0");
            else
                return totalHours.ToString("F1");
        }

        private string FormatHoursAndMinutes(double totalHours)
        {
            if (totalHours <= 0)
                return "0 دقيقة";

            // تحويل إلى دقائق أولاً للحصول على دقة أفضل
            int totalMinutes = (int)Math.Round(totalHours * 60);

            int hours = totalMinutes / 60;
            int minutes = totalMinutes % 60;

            if (hours == 0)
                return $"{minutes} دقيقة";
            else if (minutes == 0)
                return $"{hours} ساعة";
            else
                return $"{hours} ساعة {minutes} دقيقة";
        }

        private void FormatWorkingHoursColumns()
        {
            try
            {
                // إنشاء DataTable جديد مع البيانات المنسقة
                if (dataGridViewAttendance.DataSource is DataTable originalTable && originalTable.Rows.Count > 0)
                {
                    DataTable formattedTable = originalTable.Copy();

                    // تنسيق عمود ساعات العمل
                    if (formattedTable.Columns.Contains("ساعات العمل"))
                    {
                        foreach (DataRow row in formattedTable.Rows)
                        {
                            if (row["ساعات العمل"] != null && row["ساعات العمل"] != DBNull.Value)
                            {
                                if (double.TryParse(row["ساعات العمل"].ToString(), out double workingHours))
                                {
                                    row["ساعات العمل"] = FormatHoursDisplay(workingHours);
                                }
                            }
                        }
                    }

                    // تنسيق عمود الساعات الإضافية
                    if (formattedTable.Columns.Contains("الساعات الإضافية"))
                    {
                        foreach (DataRow row in formattedTable.Rows)
                        {
                            if (row["الساعات الإضافية"] != null && row["الساعات الإضافية"] != DBNull.Value)
                            {
                                if (double.TryParse(row["الساعات الإضافية"].ToString(), out double overtimeHours))
                                {
                                    row["الساعات الإضافية"] = FormatHoursDisplay(overtimeHours);
                                }
                            }
                        }
                    }

                    // إعادة ربط البيانات المنسقة
                    dataGridViewAttendance.DataSource = formattedTable;

                    System.Diagnostics.Debug.WriteLine("تم تنسيق ساعات العمل في الجدول");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تنسيق أعمدة ساعات العمل: {ex.Message}");
            }
        }

        #region إدارة حالة الحضور

        private void InitializeAttendanceStatus()
        {
            // تعيين القيمة الافتراضية
            cmbAttendanceStatus.SelectedIndex = 0; // "حاضر"

            // تعيين القيمة الافتراضية للساعات الإضافية
            numericUpDownOvertimeHours.Value = 0;
        }

        private void UpdateAttendanceStatusComboBox(string currentStatus)
        {
            try
            {
                // منع تشغيل الحدث أثناء التحديث
                cmbAttendanceStatus.SelectedIndexChanged -= cmbAttendanceStatus_SelectedIndexChanged;

                // البحث عن الحالة في القائمة وتحديدها
                for (int i = 0; i < cmbAttendanceStatus.Items.Count; i++)
                {
                    if (cmbAttendanceStatus.Items[i].ToString() == currentStatus)
                    {
                        cmbAttendanceStatus.SelectedIndex = i;
                        break;
                    }
                }

                // إعادة تشغيل الحدث
                cmbAttendanceStatus.SelectedIndexChanged += cmbAttendanceStatus_SelectedIndexChanged;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث قائمة الحالة: {ex.Message}");
            }
        }

        private void UpdateOvertimeHoursInput(double overtimeHours)
        {
            try
            {
                // منع تشغيل الحدث أثناء التحديث
                numericUpDownOvertimeHours.ValueChanged -= numericUpDownOvertimeHours_ValueChanged;

                // تحديث القيمة
                decimal value = (decimal)Math.Max(0, Math.Min(12, overtimeHours));
                numericUpDownOvertimeHours.Value = value;

                // إعادة تشغيل الحدث
                numericUpDownOvertimeHours.ValueChanged += numericUpDownOvertimeHours_ValueChanged;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث حقل الساعات الإضافية: {ex.Message}");
            }
        }

        private void numericUpDownOvertimeHours_ValueChanged(object sender, EventArgs e)
        {
            // لا نفعل شيئاً هنا - الحقل للعرض والاختيار فقط
            // التحديث الفعلي يتم عبر زر "التسجيل اليدوي"
        }

        private void UpdateEmployeeOvertimeHours(int employeeCode, DateTime date, double overtimeHours)
        {
            try
            {
                // البحث عن سجل حضور موجود لهذا الموظف في هذا التاريخ
                var existingAttendance = DatabaseHelper.GetAttendanceByEmployeeAndDate(employeeCode, date);

                if (existingAttendance != null)
                {
                    // تحديث الساعات الإضافية للسجل الموجود
                    existingAttendance.OvertimeHours = overtimeHours;

                    // إضافة ملاحظة عن التحديث اليدوي
                    if (overtimeHours > 0)
                    {
                        existingAttendance.Notes = $"ساعات إضافية: {overtimeHours} (تم التحديث يدوياً)";
                    }

                    DatabaseHelper.UpdateAttendance(existingAttendance);
                }
                else
                {
                    // إنشاء سجل جديد إذا لم يكن موجود
                    var employee = DatabaseHelper.GetEmployeeById(employeeCode);
                    if (employee != null)
                    {
                        var newAttendance = new Attendance
                        {
                            EmployeeCode = employeeCode,
                            EmployeeName = employee.Name,
                            Date = date,
                            Status = "حاضر",
                            CheckInTime = DateTime.Now,
                            CheckOutTime = null,
                            WorkingHours = 0,
                            OvertimeHours = overtimeHours,
                            Notes = $"ساعات إضافية: {overtimeHours} (تم الإنشاء يدوياً)",
                            CreatedDate = DateTime.Now
                        };

                        DatabaseHelper.AddAttendance(newAttendance);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"تم تحديث الساعات الإضافية للموظف {employeeCode} في {date:yyyy-MM-dd} إلى: {overtimeHours}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الساعات الإضافية للموظف: {ex.Message}");
                throw;
            }
        }

        private void cmbAttendanceStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            // لا نفعل شيئاً هنا - القائمة للعرض والاختيار فقط
            // التحديث الفعلي يتم عبر زر "التسجيل اليدوي"
        }

        private void UpdateEmployeeAttendanceStatus(int employeeCode, DateTime date, string status)
        {
            try
            {
                // البحث عن سجل حضور موجود لهذا الموظف في هذا التاريخ
                var existingAttendance = DatabaseHelper.GetAttendanceByEmployeeAndDate(employeeCode, date);

                if (existingAttendance != null)
                {
                    // تحديث الحالة للسجل الموجود
                    existingAttendance.Status = status;

                    // إذا كانت الحالة "غائب"، مسح أوقات الحضور والانصراف
                    if (status == "غائب")
                    {
                        existingAttendance.CheckInTime = null;
                        existingAttendance.CheckOutTime = null;
                        existingAttendance.WorkingHours = 0;
                        existingAttendance.OvertimeHours = 0;
                        existingAttendance.Notes = "تم تعديل الحالة يدوياً إلى غائب";
                    }

                    DatabaseHelper.UpdateAttendance(existingAttendance);
                }
                else
                {
                    // إنشاء سجل جديد
                    var employee = DatabaseHelper.GetEmployeeById(employeeCode);
                    if (employee != null)
                    {
                        var newAttendance = new Attendance
                        {
                            EmployeeCode = employeeCode,
                            EmployeeName = employee.Name,
                            Date = date,
                            Status = status,
                            CheckInTime = status == "غائب" ? null : DateTime.Now,
                            CheckOutTime = null,
                            WorkingHours = status == "غائب" ? 0 : 0,
                            OvertimeHours = 0,
                            Notes = $"تم إنشاء السجل يدوياً بحالة: {status}",
                            CreatedDate = DateTime.Now
                        };

                        DatabaseHelper.AddAttendance(newAttendance);
                    }
                }

                System.Diagnostics.Debug.WriteLine($"تم تحديث حالة الموظف {employeeCode} في {date:yyyy-MM-dd} إلى: {status}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث حالة الموظف: {ex.Message}");
                throw;
            }
        }

        private void btnManualEntry_Click(object sender, EventArgs e)
        {
            if (selectedEmployeeCode <= 0)
            {
                MessageBox.Show("الرجاء اختيار موظف أولاً", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                // الحصول على القيم المختارة من الواجهة
                string selectedStatus = cmbAttendanceStatus.SelectedItem?.ToString() ?? "حاضر";
                double overtimeHours = (double)numericUpDownOvertimeHours.Value;

                // تأكيد العملية
                var result = MessageBox.Show(
                    $"هل تريد تسجيل الموظف: {selectedEmployeeName}\n" +
                    $"الحالة: {selectedStatus}\n" +
                    $"الساعات الإضافية: {overtimeHours}\n\n" +
                    "هذا تسجيل يدوي وسيتم حفظه مباشرة في قاعدة البيانات.",
                    "تأكيد التسجيل اليدوي",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // تنفيذ التسجيل اليدوي
                    bool success = ManualAttendanceEntry(selectedEmployeeCode, selectedEmployeeName,
                                                       selectedStatus, overtimeHours, selectedWorkPeriodId);

                    if (success)
                    {
                        MessageBox.Show($"تم التسجيل اليدوي بنجاح\nالحالة: {selectedStatus}", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // تحديث الواجهة
                        UpdateTodayAttendanceInfo();
                        LoadAttendanceData();
                    }
                    else
                    {
                        MessageBox.Show("فشل في التسجيل اليدوي", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التسجيل اليدوي: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ManualAttendanceEntry(int employeeCode, string employeeName, string status,
                                         double overtimeHours, int workPeriodId)
        {
            try
            {
                // البحث عن سجل موجود
                var existingAttendance = DatabaseHelper.GetAttendanceByEmployeeAndDate(employeeCode, DateTime.Today);

                if (existingAttendance != null)
                {
                    // تحديث السجل الموجود
                    existingAttendance.Status = status;
                    existingAttendance.OvertimeHours = overtimeHours;
                    existingAttendance.WorkPeriodId = workPeriodId > 0 ? workPeriodId : null;

                    // معالجة خاصة حسب الحالة
                    if (status == "غائب")
                    {
                        existingAttendance.CheckInTime = null;
                        existingAttendance.CheckOutTime = null;
                        existingAttendance.WorkingHours = 0;
                        existingAttendance.OvertimeHours = 0; // الغائب لا يحصل على ساعات إضافية
                    }
                    else if (status == "حاضر" || status == "متأخر")
                    {
                        // إذا لم يكن هناك وقت حضور، ضع الوقت الحالي
                        if (!existingAttendance.CheckInTime.HasValue)
                        {
                            existingAttendance.CheckInTime = DateTime.Now;
                        }
                    }

                    existingAttendance.Notes = $"تسجيل يدوي - الحالة: {status}, ساعات إضافية: {overtimeHours}";

                    DatabaseHelper.UpdateAttendance(existingAttendance);
                }
                else
                {
                    // إنشاء سجل جديد
                    var newAttendance = new Attendance
                    {
                        EmployeeCode = employeeCode,
                        EmployeeName = employeeName,
                        Date = DateTime.Today,
                        Status = status,
                        CheckInTime = status == "غائب" ? null : DateTime.Now,
                        CheckOutTime = null,
                        WorkingHours = 0,
                        OvertimeHours = status == "غائب" ? 0 : overtimeHours,
                        WorkPeriodId = workPeriodId > 0 ? workPeriodId : null,
                        Notes = $"تسجيل يدوي جديد - الحالة: {status}, ساعات إضافية: {overtimeHours}",
                        CreatedDate = DateTime.Now
                    };

                    DatabaseHelper.AddAttendance(newAttendance);
                }

                System.Diagnostics.Debug.WriteLine($"تم التسجيل اليدوي للموظف {employeeCode} - الحالة: {status}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التسجيل اليدوي: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
