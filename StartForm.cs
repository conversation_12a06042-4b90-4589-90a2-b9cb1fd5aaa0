﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SQLite;
using System.Drawing;
using System.Drawing.Printing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class StartForm : Form
    {
        public StartForm()
        {
            InitializeComponent();
            this.labelCopyright.Text = "جميع الحقوق محفوظة  © 2024-" + DateTime.Now.Year.ToString();
            this.StartPosition = FormStartPosition.CenterScreen;
            try
            {
                DatabaseHelper.InitializeDatabase();

               
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }

        }

        private async void TimerStart_Tick(object sender, EventArgs e)
        {
            labelState.Text = "جاري التحقق من الاتصال...";
            timerStart.Enabled = false; // إيقاف التايمر

            progressBar1.Style = ProgressBarStyle.Marquee;
            progressBar1.MarqueeAnimationSpeed = 30;
            progressBar1.Visible = true;
            await Task.Delay(500); // فقط للتأثير البصري

            try
            {
                // التأكد من تهيئة قاعدة البيانات
                DatabaseHelper.InitializeDatabase();

                using var conn = new SQLiteConnection(DatabaseHelper.ConnectionString);
                conn.Open();
                string query = "SELECT COUNT(*) FROM Users";
                using var cmd = new SQLiteCommand(query, conn);
                int userCount = Convert.ToInt32(cmd.ExecuteScalar());

                if (userCount == 0)
                {
                    labelState.Text = "لا يوجد مستخدمون. سيتم فتح نموذج إضافة مستخدم...";
                    Application.DoEvents();
                    await Task.Delay(500);

                    UserForm userForm = new(); // يجب أن تكون جهزت هذا النموذج
                    userForm.Show();
                }
                else
                {
                    labelState.Text = "تم العثور على مستخدمين. يتم فتح نموذج تسجيل الدخول...";
                    Application.DoEvents();
                    await Task.Delay(500);

                    LoginForm loginForm = new();
                    loginForm.Show();
                }

                this.Hide();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل الاتصال بقاعدة البيانات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                labelState.Text = "فشل الاتصال بقاعدة البيانات.";
                await Task.Delay(2000);
                Application.Exit();
                progressBar1.Visible = false;
            }
        }

    }
}
