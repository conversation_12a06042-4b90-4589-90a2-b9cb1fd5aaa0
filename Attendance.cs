using System;
using System.Text;

namespace EmployeeManagementSystem
{
    public class Attendance
    {
        public int AttendanceId { get; set; }
        public int EmployeeCode { get; set; }
        public string? EmployeeName { get; set; }
        public DateTime Date { get; set; }
        public DateTime? CheckInTime { get; set; }
        public DateTime? CheckOutTime { get; set; }
        public double WorkingHours { get; set; }
        public double OvertimeHours { get; set; }
        public string? Status { get; set; } // "حاضر", "غائب", "متأخر", "إجازة"
        public string? Notes { get; set; }
        public int? WorkPeriodId { get; set; } // معرف فترة العمل (اختياري)
        public DateTime CreatedDate { get; set; }

        public Attendance()
        {
            Date = DateTime.Today;
            CreatedDate = DateTime.Now;
            Status = "غائب";
        }

        // حساب ساعات العمل
        public void CalculateWorkingHours()
        {
            if (CheckInTime.HasValue && CheckOutTime.HasValue)
            {
                var totalHours = (CheckOutTime.Value - CheckInTime.Value).TotalHours;
                
                // ساعات العمل العادية (8 ساعات)
                const double normalWorkingHours = 8.0;
                
                if (totalHours <= normalWorkingHours)
                {
                    WorkingHours = totalHours;
                    OvertimeHours = 0;
                }
                else
                {
                    WorkingHours = normalWorkingHours;
                    OvertimeHours = totalHours - normalWorkingHours;
                }

                // تحديد الحالة
                if (totalHours >= 1) // إذا عمل ساعة واحدة على الأقل
                {
                    Status = "حاضر";
                }
            }
        }

        // تحديد حالة التأخير
        public void CheckLateStatus(TimeSpan officialStartTime)
        {
            if (CheckInTime.HasValue)
            {
                var checkInTimeOnly = CheckInTime.Value.TimeOfDay;
                if (checkInTimeOnly > officialStartTime.Add(TimeSpan.FromMinutes(15))) // تأخير أكثر من 15 دقيقة
                {
                    Status = "متأخر";
                }
            }
        }

        // تحويل إلى تقرير HTML
        public string GetHtmlReport()
        {
            StringBuilder html = new StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير الحضور</title>
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            padding: 20px;
            direction: rtl;
            background-color: #f5f5f5;
        }
        .report-header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        .report-header h1 {
            margin: 0;
            font-size: 28px;
        }
        .attendance-details {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #eee;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .label {
            font-weight: bold;
            color: #333;
            flex: 1;
        }
        .value {
            flex: 2;
            text-align: left;
            color: #666;
        }
        .status {
            padding: 5px 15px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
        }
        .status.present { background-color: #28a745; }
        .status.absent { background-color: #dc3545; }
        .status.late { background-color: #ffc107; color: #333; }
        .status.vacation { background-color: #17a2b8; }
        @media print {
            body {
                background-color: white;
            }
            .report-header {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class=""report-header"">
        <h1>تقرير حضور موظف</h1>
        <p>التاريخ: " + DateTime.Now.ToString("dd/MM/yyyy") + @"</p>
    </div>
    
    <div class=""attendance-details"">");

            AddDetailRow(html, "اسم الموظف", EmployeeName ?? "");
            AddDetailRow(html, "تاريخ الحضور", Date.ToString("dd/MM/yyyy"));
            AddDetailRow(html, "وقت الحضور", CheckInTime?.ToString("HH:mm") ?? "لم يسجل");
            AddDetailRow(html, "وقت الانصراف", CheckOutTime?.ToString("HH:mm") ?? "لم يسجل");
            AddDetailRow(html, "ساعات العمل", WorkingHours.ToString("F2") + " ساعة");
            AddDetailRow(html, "الساعات الإضافية", OvertimeHours.ToString("F2") + " ساعة");
            
            // إضافة الحالة مع التنسيق المناسب
            string statusClass = Status?.ToLower() switch
            {
                "حاضر" => "present",
                "غائب" => "absent", 
                "متأخر" => "late",
                "إجازة" => "vacation",
                _ => "absent"
            };
            
            html.AppendFormat(@"
        <div class=""detail-row"">
            <div class=""label"">الحالة:</div>
            <div class=""value""><span class=""status {0}"">{1}</span></div>
        </div>", statusClass, Status ?? "غائب");
            
            AddDetailRow(html, "ملاحظات", Notes ?? "لا توجد ملاحظات");

            html.Append(@"</div>
</body>
</html>");

            return html.ToString();
        }

        private void AddDetailRow(StringBuilder html, string label, string value)
        {
            html.AppendFormat(@"
        <div class=""detail-row"">
            <div class=""label"">{0}:</div>
            <div class=""value"">{1}</div>
        </div>", label, value);
        }
    }
}
