using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class GroupWorkPeriodForm : Form
    {
        private List<int> selectedWorkEmployees = new List<int>();
        private List<int> selectedVacationEmployees = new List<int>();

        public GroupWorkPeriodForm()
        {
            InitializeComponent();
        }

        private void GroupWorkPeriodForm_Load(object sender, EventArgs e)
        {
            LoadEmployees();
            SetDefaultDates();
            cmbStatus.SelectedIndex = 0; // نشط
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = DatabaseHelper.GetAllEmployees();
                
                // تحميل قائمة جميع الموظفين
                listBoxAllEmployees.Items.Clear();
                foreach (DataRow row in employees.Rows)
                {
                    string employeeInfo = $"{row["الاسم"]} - {row["كود الموظف"]}";
                    listBoxAllEmployees.Items.Add(employeeInfo);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetDefaultDates()
        {
            dateTimePickerStart.Value = DateTime.Today;
            dateTimePickerEnd.Value = DateTime.Today.AddDays(30);
        }

        private void btnAddToWork_Click(object sender, EventArgs e)
        {
            MoveSelectedEmployees(listBoxAllEmployees, listBoxWorkEmployees, selectedWorkEmployees);
        }

        private void btnRemoveFromWork_Click(object sender, EventArgs e)
        {
            MoveSelectedEmployees(listBoxWorkEmployees, listBoxAllEmployees, selectedWorkEmployees, true);
        }

        private void btnAddToVacation_Click(object sender, EventArgs e)
        {
            MoveSelectedEmployees(listBoxAllEmployees, listBoxVacationEmployees, selectedVacationEmployees);
        }

        private void btnRemoveFromVacation_Click(object sender, EventArgs e)
        {
            MoveSelectedEmployees(listBoxVacationEmployees, listBoxAllEmployees, selectedVacationEmployees, true);
        }

        private void MoveSelectedEmployees(ListBox source, ListBox destination, List<int> employeeList, bool isRemoving = false)
        {
            try
            {
                if (source.SelectedItems.Count == 0)
                {
                    MessageBox.Show("الرجاء اختيار موظف أو أكثر", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedItems = source.SelectedItems.Cast<string>().ToList();
                
                foreach (string item in selectedItems)
                {
                    // استخراج كود الموظف
                    string[] parts = item.Split('-');
                    if (parts.Length >= 2 && int.TryParse(parts[1].Trim(), out int employeeCode))
                    {
                        if (isRemoving)
                        {
                            employeeList.Remove(employeeCode);
                        }
                        else
                        {
                            if (!employeeList.Contains(employeeCode))
                            {
                                employeeList.Add(employeeCode);
                            }
                        }
                    }

                    // نقل العنصر
                    destination.Items.Add(item);
                    source.Items.Remove(item);
                }

                UpdateEmployeeCountLabels();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نقل الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateEmployeeCountLabels()
        {
            lblWorkCount.Text = $"موظفين في العمل: {listBoxWorkEmployees.Items.Count}";
            lblVacationCount.Text = $"موظفين في إجازة: {listBoxVacationEmployees.Items.Count}";
        }

        private void btnCreatePeriods_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                int successCount = 0;
                int errorCount = 0;

                // إنشاء فترات عمل للموظفين في العمل
                foreach (int employeeCode in selectedWorkEmployees)
                {
                    if (CreateWorkPeriod(employeeCode, "حضور"))
                        successCount++;
                    else
                        errorCount++;
                }

                // إنشاء فترات إجازة للموظفين في الإجازة
                foreach (int employeeCode in selectedVacationEmployees)
                {
                    if (CreateWorkPeriod(employeeCode, "إجازة"))
                        successCount++;
                    else
                        errorCount++;
                }

                string message = $"تم إنشاء {successCount} فترة عمل بنجاح";
                if (errorCount > 0)
                    message += $"\nفشل في إنشاء {errorCount} فترة";

                MessageBox.Show(message, "نتيجة العملية",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                if (successCount > 0)
                {
                    ClearForm();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء فترات العمل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool CreateWorkPeriod(int employeeCode, string status)
        {
            try
            {
                var employee = DatabaseHelper.GetEmployeeById(employeeCode);
                
                var workPeriod = new WorkPeriod
                {
                    EmployeeCode = employeeCode,
                    EmployeeName = employee.Name,
                    ProjectName = txtWorkPlace.Text,
                    Description = txtDescription.Text,
                    StartDate = dateTimePickerStart.Value,
                    EndDate = dateTimePickerEnd.Value,
                    WorkingDays = GetSelectedWorkingDays(),
                    DailyWorkingHours = (double)numericUpDownDailyHours.Value,
                    Status = status,
                    CreatedDate = DateTime.Now
                };

                DatabaseHelper.AddWorkPeriod(workPeriod);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private string GetSelectedWorkingDays()
        {
            var days = new List<string>();
            if (chkSunday.Checked) days.Add("الأحد");
            if (chkMonday.Checked) days.Add("الاثنين");
            if (chkTuesday.Checked) days.Add("الثلاثاء");
            if (chkWednesday.Checked) days.Add("الأربعاء");
            if (chkThursday.Checked) days.Add("الخميس");
            if (chkFriday.Checked) days.Add("الجمعة");
            if (chkSaturday.Checked) days.Add("السبت");
            return string.Join(",", days);
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtWorkPlace.Text))
            {
                MessageBox.Show("الرجاء إدخال مكان العمل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (selectedWorkEmployees.Count == 0 && selectedVacationEmployees.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار موظف واحد على الأقل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (dateTimePickerEnd.Value <= dateTimePickerStart.Value)
            {
                MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            txtWorkPlace.Clear();
            txtDescription.Clear();
            
            // إعادة جميع الموظفين للقائمة الأصلية
            listBoxAllEmployees.Items.Clear();
            listBoxWorkEmployees.Items.Clear();
            listBoxVacationEmployees.Items.Clear();
            
            selectedWorkEmployees.Clear();
            selectedVacationEmployees.Clear();
            
            LoadEmployees();
            SetDefaultDates();
            UpdateEmployeeCountLabels();
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }
    }
}
