using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class GroupWorkPeriodForm : Form
    {
        private List<int> selectedWorkEmployees = new List<int>();
        private List<int> selectedVacationEmployees = new List<int>();
        private int selectedGroupId = 0;

        public GroupWorkPeriodForm()
        {
            InitializeComponent();
        }

        private void GroupWorkPeriodForm_Load(object sender, EventArgs e)
        {
            LoadEmployees();
            SetDefaultDates();
            LoadSavedGroups();
            InitializeStatusComboBoxes();
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = DatabaseHelper.GetAllEmployees();

                // تحميل قائمة جميع الموظفين (بدون أرقام)
                listBoxAllEmployees.Items.Clear();
                foreach (DataRow row in employees.Rows)
                {
                    string employeeInfo = $"{row["الاسم"]}";
                    listBoxAllEmployees.Items.Add(employeeInfo);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetDefaultDates()
        {
            dateTimePickerStart.Value = DateTime.Today;
            dateTimePickerEnd.Value = DateTime.Today.AddDays(30);
        }

        private void InitializeStatusComboBoxes()
        {
            // تعيين القيم الافتراضية
            cmbWorkStatus.SelectedIndex = 4; // "حضور"
            cmbVacationStatus.SelectedIndex = 6; // "إجازة"
            cmbStatus.SelectedIndex = 0; // "نشط" (للاحتفاظ بالتوافق مع الكود الموجود)
        }

        private void btnAddToWork_Click(object sender, EventArgs e)
        {
            MoveSelectedEmployees(listBoxAllEmployees, listBoxWorkEmployees, selectedWorkEmployees);
        }

        private void btnRemoveFromWork_Click(object sender, EventArgs e)
        {
            MoveSelectedEmployees(listBoxWorkEmployees, listBoxAllEmployees, selectedWorkEmployees, true);
        }

        private void btnAddToVacation_Click(object sender, EventArgs e)
        {
            MoveSelectedEmployees(listBoxAllEmployees, listBoxVacationEmployees, selectedVacationEmployees);
        }

        private void btnRemoveFromVacation_Click(object sender, EventArgs e)
        {
            MoveSelectedEmployees(listBoxVacationEmployees, listBoxAllEmployees, selectedVacationEmployees, true);
        }

        private void MoveSelectedEmployees(ListBox source, ListBox destination, List<int> employeeList, bool isRemoving = false)
        {
            try
            {
                if (source.SelectedItems.Count == 0)
                {
                    MessageBox.Show("الرجاء اختيار موظف أو أكثر", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedItems = source.SelectedItems.Cast<string>().ToList();

                foreach (string employeeName in selectedItems)
                {
                    // البحث عن كود الموظف بالاسم
                    int employeeCode = GetEmployeeCodeByName(employeeName);

                    if (employeeCode > 0)
                    {
                        if (isRemoving)
                        {
                            employeeList.Remove(employeeCode);
                        }
                        else
                        {
                            if (!employeeList.Contains(employeeCode))
                            {
                                employeeList.Add(employeeCode);
                            }
                        }
                    }

                    // نقل العنصر
                    destination.Items.Add(employeeName);
                    source.Items.Remove(employeeName);
                }

                UpdateEmployeeCountLabels();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نقل الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private int GetEmployeeCodeByName(string employeeName)
        {
            try
            {
                var employees = DatabaseHelper.GetAllEmployees();
                foreach (DataRow row in employees.Rows)
                {
                    if (row["الاسم"].ToString() == employeeName)
                    {
                        return Convert.ToInt32(row["كود الموظف"]);
                    }
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private void UpdateEmployeeCountLabels()
        {
            lblWorkCount.Text = $"موظفين في العمل: {listBoxWorkEmployees.Items.Count}";
            lblVacationCount.Text = $"موظفين في إجازة: {listBoxVacationEmployees.Items.Count}";
        }

        private void btnCreatePeriods_Click(object sender, EventArgs e)
        {
            if (!ValidateInput())
                return;

            try
            {
                int successCount = 0;
                int errorCount = 0;

                // الحصول على الحالات المحددة لكل مجموعة
                string workStatus = cmbWorkStatus.SelectedItem?.ToString() ?? "حضور";
                string vacationStatus = cmbVacationStatus.SelectedItem?.ToString() ?? "إجازة";

                // إنشاء فترات عمل للموظفين في العمل
                foreach (int employeeCode in selectedWorkEmployees)
                {
                    if (CreateWorkPeriod(employeeCode, workStatus))
                        successCount++;
                    else
                        errorCount++;
                }

                // إنشاء فترات عمل للموظفين في الإجازة
                foreach (int employeeCode in selectedVacationEmployees)
                {
                    if (CreateWorkPeriod(employeeCode, vacationStatus))
                        successCount++;
                    else
                        errorCount++;
                }

                string message = $"تم إنشاء {successCount} فترة عمل بنجاح";
                if (selectedWorkEmployees.Count > 0)
                    message += $"\n- {selectedWorkEmployees.Count} موظف عمل بحالة '{workStatus}'";
                if (selectedVacationEmployees.Count > 0)
                    message += $"\n- {selectedVacationEmployees.Count} موظف إجازة بحالة '{vacationStatus}'";
                if (errorCount > 0)
                    message += $"\nفشل في إنشاء {errorCount} فترة";

                MessageBox.Show(message, "نتيجة العملية",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                if (successCount > 0)
                {
                    ClearForm();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء فترات العمل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool CreateWorkPeriod(int employeeCode, string status)
        {
            try
            {
                var employee = DatabaseHelper.GetEmployeeById(employeeCode);
                
                var workPeriod = new WorkPeriod
                {
                    EmployeeCode = employeeCode,
                    EmployeeName = employee.Name,
                    ProjectName = txtWorkPlace.Text,
                    Description = txtDescription.Text,
                    StartDate = dateTimePickerStart.Value,
                    EndDate = dateTimePickerEnd.Value,
                    WorkingDays = GetSelectedWorkingDays(),
                    DailyWorkingHours = (double)numericUpDownDailyHours.Value,
                    Status = status,
                    CreatedDate = DateTime.Now
                };

                DatabaseHelper.AddWorkPeriod(workPeriod);
                return true;
            }
            catch
            {
                return false;
            }
        }

        private string GetSelectedWorkingDays()
        {
            var days = new List<string>();
            if (chkSunday.Checked) days.Add("الأحد");
            if (chkMonday.Checked) days.Add("الاثنين");
            if (chkTuesday.Checked) days.Add("الثلاثاء");
            if (chkWednesday.Checked) days.Add("الأربعاء");
            if (chkThursday.Checked) days.Add("الخميس");
            if (chkFriday.Checked) days.Add("الجمعة");
            if (chkSaturday.Checked) days.Add("السبت");
            return string.Join(",", days);
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtWorkPlace.Text))
            {
                MessageBox.Show("الرجاء إدخال مكان العمل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (selectedWorkEmployees.Count == 0 && selectedVacationEmployees.Count == 0)
            {
                MessageBox.Show("الرجاء اختيار موظف واحد على الأقل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (dateTimePickerEnd.Value <= dateTimePickerStart.Value)
            {
                MessageBox.Show("تاريخ النهاية يجب أن يكون بعد تاريخ البداية", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (selectedWorkEmployees.Count > 0 && cmbWorkStatus.SelectedIndex == -1)
            {
                MessageBox.Show("الرجاء اختيار حالة للموظفين في العمل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (selectedVacationEmployees.Count > 0 && cmbVacationStatus.SelectedIndex == -1)
            {
                MessageBox.Show("الرجاء اختيار حالة للموظفين في الإجازة", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            txtWorkPlace.Clear();
            txtDescription.Clear();
            
            // إعادة جميع الموظفين للقائمة الأصلية
            listBoxAllEmployees.Items.Clear();
            listBoxWorkEmployees.Items.Clear();
            listBoxVacationEmployees.Items.Clear();
            
            selectedWorkEmployees.Clear();
            selectedVacationEmployees.Clear();
            
            LoadEmployees();
            SetDefaultDates();
            UpdateEmployeeCountLabels();
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearForm();
        }

        #region إدارة المجموعات المحفوظة

        private void LoadSavedGroups()
        {
            try
            {
                var groups = DatabaseHelper.GetAllWorkGroups();
                dataGridViewGroups.DataSource = groups;

                // تنسيق الأعمدة
                if (dataGridViewGroups.Columns.Count > 0)
                {
                    foreach (DataGridViewColumn column in dataGridViewGroups.Columns)
                    {
                        column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجموعات المحفوظة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnSaveGroup_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtGroupName.Text))
                {
                    MessageBox.Show("الرجاء إدخال اسم المجموعة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (selectedWorkEmployees.Count == 0 && selectedVacationEmployees.Count == 0)
                {
                    MessageBox.Show("الرجاء اختيار موظف واحد على الأقل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إنشاء المجموعة
                int groupId = DatabaseHelper.CreateWorkGroup(txtGroupName.Text, txtDescription.Text, "النظام");

                // إضافة موظفي العمل
                foreach (int employeeCode in selectedWorkEmployees)
                {
                    var employee = DatabaseHelper.GetEmployeeById(employeeCode);
                    DatabaseHelper.AddMemberToWorkGroup(groupId, employeeCode, employee.Name, "عمل");
                }

                // إضافة موظفي الإجازة
                foreach (int employeeCode in selectedVacationEmployees)
                {
                    var employee = DatabaseHelper.GetEmployeeById(employeeCode);
                    DatabaseHelper.AddMemberToWorkGroup(groupId, employeeCode, employee.Name, "إجازة");
                }

                MessageBox.Show("تم حفظ المجموعة بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoadSavedGroups();
                txtGroupName.Clear();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المجموعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnLoadGroup_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedGroupId == 0)
                {
                    MessageBox.Show("الرجاء اختيار مجموعة للتحميل", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"بدء تحميل المجموعة: {selectedGroupId}");

                // مسح التحديد الحالي
                ClearForm();

                // تحميل أعضاء المجموعة
                DatabaseHelper.LoadWorkGroupToForm(selectedGroupId, out List<int> workEmployees, out List<int> vacationEmployees);

                System.Diagnostics.Debug.WriteLine($"تم تحميل {workEmployees.Count} موظف عمل و {vacationEmployees.Count} موظف إجازة من قاعدة البيانات");

                // نقل الموظفين للقوائم المناسبة
                var allEmployees = DatabaseHelper.GetAllEmployees();

                foreach (int employeeCode in workEmployees)
                {
                    var employeeRow = allEmployees.AsEnumerable()
                        .FirstOrDefault(row =>
                        {
                            try
                            {
                                // التعامل مع أنواع البيانات المختلفة
                                var codeValue = row["كود الموظف"];
                                int code = Convert.ToInt32(codeValue);
                                return code == employeeCode;
                            }
                            catch
                            {
                                return false;
                            }
                        });

                    if (employeeRow != null)
                    {
                        string employeeInfo = $"{employeeRow["الاسم"]}";

                        // إزالة من القائمة الأصلية وإضافة لقائمة العمل
                        if (listBoxAllEmployees.Items.Contains(employeeInfo))
                        {
                            listBoxAllEmployees.Items.Remove(employeeInfo);
                            listBoxWorkEmployees.Items.Add(employeeInfo);
                            selectedWorkEmployees.Add(employeeCode);
                        }
                    }
                }

                foreach (int employeeCode in vacationEmployees)
                {
                    var employeeRow = allEmployees.AsEnumerable()
                        .FirstOrDefault(row =>
                        {
                            try
                            {
                                // التعامل مع أنواع البيانات المختلفة
                                var codeValue = row["كود الموظف"];
                                int code = Convert.ToInt32(codeValue);
                                return code == employeeCode;
                            }
                            catch
                            {
                                return false;
                            }
                        });

                    if (employeeRow != null)
                    {
                        string employeeInfo = $"{employeeRow["الاسم"]}";

                        // إزالة من القائمة الأصلية وإضافة لقائمة الإجازة
                        if (listBoxAllEmployees.Items.Contains(employeeInfo))
                        {
                            listBoxAllEmployees.Items.Remove(employeeInfo);
                            listBoxVacationEmployees.Items.Add(employeeInfo);
                            selectedVacationEmployees.Add(employeeCode);
                        }
                    }
                }

                UpdateEmployeeCountLabels();

                MessageBox.Show("تم تحميل المجموعة بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المجموعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDeleteGroup_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedGroupId == 0)
                {
                    MessageBox.Show("الرجاء اختيار مجموعة للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من حذف المجموعة المحددة؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    DatabaseHelper.DeleteWorkGroup(selectedGroupId);

                    MessageBox.Show("تم حذف المجموعة بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    LoadSavedGroups();
                    selectedGroupId = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المجموعة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void dataGridViewGroups_SelectionChanged(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewGroups.SelectedRows.Count > 0)
                {
                    var selectedRow = dataGridViewGroups.SelectedRows[0];
                    if (selectedRow.Cells["معرف المجموعة"] != null && selectedRow.Cells["معرف المجموعة"].Value != null)
                    {
                        // التعامل مع أنواع البيانات المختلفة (Int32/Int64)
                        var groupIdValue = selectedRow.Cells["معرف المجموعة"].Value;
                        selectedGroupId = Convert.ToInt32(groupIdValue);

                        // عرض اسم المجموعة في حقل النص
                        if (selectedRow.Cells["اسم المجموعة"] != null)
                        {
                            txtGroupName.Text = selectedRow.Cells["اسم المجموعة"].Value?.ToString() ?? "";
                        }

                        System.Diagnostics.Debug.WriteLine($"تم تحديد المجموعة: {selectedGroupId}");
                    }
                }
                else
                {
                    selectedGroupId = 0;
                    System.Diagnostics.Debug.WriteLine("تم إلغاء تحديد المجموعة");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديد المجموعة: {ex.Message}");
                selectedGroupId = 0;
            }
        }

        #endregion
    }
}
