using System;
using System.Windows.Forms;
using System.IO;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Printing;

namespace EmployeeManagementSystem
{
    public partial class DocumentViewerForm : Form
    {
        private readonly string filePath;

        public DocumentViewerForm(string filePath)
        {
            this.filePath = filePath;
            InitializeComponent();
            this.Text = $"عرض الوثيقة - {Path.GetFileName(filePath)}";

            // تطبيق الثيم المحفوظ
            var settings = DatabaseHelper.GetSettings();
            if (settings.Rows.Count > 0)
            {
                string theme = settings.Rows[0]["Theme"]?.ToString() ?? "default";
                ThemeManager.ApplyThemeToForm(this);
            }

            LoadDocument();
        }

        private void LoadDocument()
        {
            try
            {
                string extension = Path.GetExtension(filePath).ToLower();
                if (IsImageFile(extension))
                {
                    LoadImage();
                    webBrowser1.Visible = false;
                    pictureBox1.Visible = true;
                    btnPrint.Click += PrintImage;
                }
                else if (extension == ".pdf")
                {
                    webBrowser1.Navigate(new Uri(Path.GetFullPath(filePath)));
                    webBrowser1.Dock = DockStyle.Fill;
                    pictureBox1.Visible = false;
                    webBrowser1.Visible = true;
                    btnPrint.Click += PrintPdf;
                }
                else
                {
                    try
                    {
                        Process.Start(new ProcessStartInfo(filePath) { UseShellExecute = true });
                        this.Close();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"لا يمكن فتح هذا النوع من الملفات: {ex.Message}", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        this.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الملف: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                Close();
            }
        }

        private void LoadImage()
        {
            using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                var image = Image.FromStream(stream);
                pictureBox1.Image?.Dispose();
                pictureBox1.Image = new Bitmap(image);
                pictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
            }
            pictureBox1.Dock = DockStyle.Fill;
        }

        private bool IsImageFile(string extension)
        {
            string[] imageExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".bmp" };
            return imageExtensions.Contains(extension);
        }

        private void PrintImage(object? sender, EventArgs e)
        {
            try
            {
                if (pictureBox1.Image == null) return;

                using (var printDialog = new PrintDialog())
                {
                    if (printDialog.ShowDialog() == DialogResult.OK)
                    {
                        using (var printDocument = new PrintDocument())
                        {
                            printDocument.PrintPage += (s, pe) =>
                            {
                                if (pictureBox1.Image == null || pe.Graphics == null) return;
                                
                                var image = pictureBox1.Image;
                                var ratio = Math.Min((double)pe.MarginBounds.Width / image.Width,
                                                   (double)pe.MarginBounds.Height / image.Height);
                                
                                var width = (int)(image.Width * ratio);
                                var height = (int)(image.Height * ratio);
                                
                                var x = (pe.PageBounds.Width - width) / 2;
                                var y = (pe.PageBounds.Height - height) / 2;
                                
                                pe.Graphics.DrawImage(image, x, y, width, height);
                            };
                            
                            printDocument.Print();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الصورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintPdf(object? sender, EventArgs e)
        {
            try
            {
                if (webBrowser1.Document != null)
                {
                    webBrowser1.Document.ExecCommand("Print", true, string.Empty);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة المستند: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);
            if (pictureBox1.Image != null)
            {
                pictureBox1.Image.Dispose();
                pictureBox1.Image = null;
            }
        }
    }
}