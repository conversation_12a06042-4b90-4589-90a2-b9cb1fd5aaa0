namespace EmployeeManagementSystem
{
    partial class WorkPeriodForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            groupBoxWorkPeriod = new GroupBox();
            numericUpDownDailyHours = new NumericUpDown();
            lblDailyHours = new Label();
            cmbStatus = new ComboBox();
            lblStatus = new Label();
            groupBoxCalendar = new GroupBox();
            monthCalendar = new MonthCalendar();
            lblCalendarTitle = new Label();
            groupBoxWorkingDays = new GroupBox();
            chkSaturday = new CheckBox();
            chkFriday = new CheckBox();
            chkThursday = new CheckBox();
            chkWednesday = new CheckBox();
            chkTuesday = new CheckBox();
            chkMonday = new CheckBox();
            chkSunday = new CheckBox();
            dateTimePickerEnd = new DateTimePicker();
            dateTimePickerStart = new DateTimePicker();
            lblEndDate = new Label();
            lblStartDate = new Label();
            txtDescription = new TextBox();
            lblDescription = new Label();
            txtProjectName = new TextBox();
            lblProjectName = new Label();
            cmbEmployee = new ComboBox();
            lblEmployee = new Label();
            groupBoxActions = new GroupBox();
            btnClear = new Button();
            btnDelete = new Button();
            btnUpdate = new Button();
            btnAdd = new Button();
            groupBoxWorkPeriodList = new GroupBox();
            btnRefresh = new Button();
            btnDailyTracking = new Button();
            chkSelectAll = new CheckBox();
            btnDeleteSelected = new Button();
            cmbSearchGroup = new ComboBox();
            lblSearchGroup = new Label();
            dateTimePickerSearchStart = new DateTimePicker();
            dateTimePickerSearchEnd = new DateTimePicker();
            lblSearchPeriod = new Label();
            btnAdvancedSearch = new Button();
            btnSearch = new Button();
            txtSearch = new TextBox();
            lblSearch = new Label();
            dataGridViewWorkPeriods = new DataGridView();
            groupBoxSummary = new GroupBox();
            lblTotalHours = new Label();
            lblTotalDays = new Label();
            lblSelectedPeriod = new Label();
            groupBoxWorkPeriod.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDownDailyHours).BeginInit();
            groupBoxCalendar.SuspendLayout();
            groupBoxWorkingDays.SuspendLayout();
            groupBoxActions.SuspendLayout();
            groupBoxWorkPeriodList.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewWorkPeriods).BeginInit();
            groupBoxSummary.SuspendLayout();
            SuspendLayout();
            // 
            // groupBoxWorkPeriod
            // 
            groupBoxWorkPeriod.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxWorkPeriod.Controls.Add(numericUpDownDailyHours);
            groupBoxWorkPeriod.Controls.Add(lblDailyHours);
            groupBoxWorkPeriod.Controls.Add(cmbStatus);
            groupBoxWorkPeriod.Controls.Add(lblStatus);
            groupBoxWorkPeriod.Controls.Add(groupBoxCalendar);
            groupBoxWorkPeriod.Controls.Add(groupBoxWorkingDays);
            groupBoxWorkPeriod.Controls.Add(dateTimePickerEnd);
            groupBoxWorkPeriod.Controls.Add(dateTimePickerStart);
            groupBoxWorkPeriod.Controls.Add(lblEndDate);
            groupBoxWorkPeriod.Controls.Add(lblStartDate);
            groupBoxWorkPeriod.Controls.Add(txtDescription);
            groupBoxWorkPeriod.Controls.Add(lblDescription);
            groupBoxWorkPeriod.Controls.Add(txtProjectName);
            groupBoxWorkPeriod.Controls.Add(lblProjectName);
            groupBoxWorkPeriod.Controls.Add(cmbEmployee);
            groupBoxWorkPeriod.Controls.Add(lblEmployee);
            groupBoxWorkPeriod.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxWorkPeriod.Location = new Point(12, 12);
            groupBoxWorkPeriod.Name = "groupBoxWorkPeriod";
            groupBoxWorkPeriod.RightToLeft = RightToLeft.Yes;
            groupBoxWorkPeriod.Size = new Size(1160, 280);
            groupBoxWorkPeriod.TabIndex = 0;
            groupBoxWorkPeriod.TabStop = false;
            groupBoxWorkPeriod.Text = "إدارة فترات العمل";
            // 
            // numericUpDownDailyHours
            // 
            numericUpDownDailyHours.DecimalPlaces = 1;
            numericUpDownDailyHours.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            numericUpDownDailyHours.Location = new Point(580, 237);
            numericUpDownDailyHours.Maximum = new decimal(new int[] { 24, 0, 0, 0 });
            numericUpDownDailyHours.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numericUpDownDailyHours.Name = "numericUpDownDailyHours";
            numericUpDownDailyHours.Size = new Size(120, 32);
            numericUpDownDailyHours.TabIndex = 14;
            numericUpDownDailyHours.Value = new decimal(new int[] { 8, 0, 0, 0 });
            // 
            // lblDailyHours
            // 
            lblDailyHours.AutoSize = true;
            lblDailyHours.Font = new Font("Cairo", 9.75F);
            lblDailyHours.Location = new Point(715, 240);
            lblDailyHours.Name = "lblDailyHours";
            lblDailyHours.Size = new Size(126, 24);
            lblDailyHours.TabIndex = 13;
            lblDailyHours.Text = "ساعات العمل اليومية:";
            // 
            // cmbStatus
            // 
            cmbStatus.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbStatus.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cmbStatus.FormattingEnabled = true;
            cmbStatus.Items.AddRange(new object[] { "نشط", "مكتمل", "ملغي", "لم يبدأ", "حضور", "غياب", "إجازة", "هروب" });
            cmbStatus.Location = new Point(880, 236);
            cmbStatus.Name = "cmbStatus";
            cmbStatus.Size = new Size(150, 32);
            cmbStatus.TabIndex = 12;
            // 
            // lblStatus
            // 
            lblStatus.AutoSize = true;
            lblStatus.Font = new Font("Cairo", 9.75F);
            lblStatus.Location = new Point(1050, 239);
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(43, 24);
            lblStatus.TabIndex = 11;
            lblStatus.Text = "الحالة:";
            // 
            // groupBoxCalendar
            // 
            groupBoxCalendar.Controls.Add(monthCalendar);
            groupBoxCalendar.Controls.Add(lblCalendarTitle);
            groupBoxCalendar.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxCalendar.Location = new Point(6, 156);
            groupBoxCalendar.Name = "groupBoxCalendar";
            groupBoxCalendar.RightToLeft = RightToLeft.Yes;
            groupBoxCalendar.Size = new Size(250, 114);
            groupBoxCalendar.TabIndex = 15;
            groupBoxCalendar.TabStop = false;
            groupBoxCalendar.Text = "التقويم الشهري";
            // 
            // monthCalendar
            // 
            monthCalendar.Location = new Point(8, 44);
            monthCalendar.MaxSelectionCount = 31;
            monthCalendar.Name = "monthCalendar";
            monthCalendar.RightToLeft = RightToLeft.Yes;
            monthCalendar.RightToLeftLayout = true;
            monthCalendar.TabIndex = 1;
            monthCalendar.DateChanged += monthCalendar_DateChanged;
            // 
            // lblCalendarTitle
            // 
            lblCalendarTitle.AutoSize = true;
            lblCalendarTitle.Font = new Font("Cairo", 8.999999F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblCalendarTitle.Location = new Point(3, 20);
            lblCalendarTitle.Name = "lblCalendarTitle";
            lblCalendarTitle.Size = new Size(137, 23);
            lblCalendarTitle.TabIndex = 0;
            lblCalendarTitle.Text = "اختر الأيام المطلوبة للعمل";
            // 
            // groupBoxWorkingDays
            // 
            groupBoxWorkingDays.Controls.Add(chkSaturday);
            groupBoxWorkingDays.Controls.Add(chkFriday);
            groupBoxWorkingDays.Controls.Add(chkThursday);
            groupBoxWorkingDays.Controls.Add(chkWednesday);
            groupBoxWorkingDays.Controls.Add(chkTuesday);
            groupBoxWorkingDays.Controls.Add(chkMonday);
            groupBoxWorkingDays.Controls.Add(chkSunday);
            groupBoxWorkingDays.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            groupBoxWorkingDays.Location = new Point(20, 80);
            groupBoxWorkingDays.Name = "groupBoxWorkingDays";
            groupBoxWorkingDays.RightToLeft = RightToLeft.Yes;
            groupBoxWorkingDays.Size = new Size(500, 70);
            groupBoxWorkingDays.TabIndex = 10;
            groupBoxWorkingDays.TabStop = false;
            groupBoxWorkingDays.Text = "أيام العمل";
            // 
            // chkSaturday
            // 
            chkSaturday.AutoSize = true;
            chkSaturday.Font = new Font("Cairo", 8.999999F);
            chkSaturday.Location = new Point(12, 25);
            chkSaturday.Name = "chkSaturday";
            chkSaturday.RightToLeft = RightToLeft.Yes;
            chkSaturday.Size = new Size(60, 27);
            chkSaturday.TabIndex = 6;
            chkSaturday.Text = "السبت";
            chkSaturday.UseVisualStyleBackColor = true;
            // 
            // chkFriday
            // 
            chkFriday.AutoSize = true;
            chkFriday.Font = new Font("Cairo", 8.999999F);
            chkFriday.Location = new Point(80, 25);
            chkFriday.Name = "chkFriday";
            chkFriday.RightToLeft = RightToLeft.Yes;
            chkFriday.Size = new Size(64, 27);
            chkFriday.TabIndex = 5;
            chkFriday.Text = "الجمعة";
            chkFriday.UseVisualStyleBackColor = true;
            // 
            // chkThursday
            // 
            chkThursday.AutoSize = true;
            chkThursday.Checked = true;
            chkThursday.CheckState = CheckState.Checked;
            chkThursday.Font = new Font("Cairo", 8.999999F);
            chkThursday.Location = new Point(150, 25);
            chkThursday.Name = "chkThursday";
            chkThursday.RightToLeft = RightToLeft.Yes;
            chkThursday.Size = new Size(69, 27);
            chkThursday.TabIndex = 4;
            chkThursday.Text = "الخميس";
            chkThursday.UseVisualStyleBackColor = true;
            // 
            // chkWednesday
            // 
            chkWednesday.AutoSize = true;
            chkWednesday.Checked = true;
            chkWednesday.CheckState = CheckState.Checked;
            chkWednesday.Font = new Font("Cairo", 8.999999F);
            chkWednesday.Location = new Point(220, 25);
            chkWednesday.Name = "chkWednesday";
            chkWednesday.RightToLeft = RightToLeft.Yes;
            chkWednesday.Size = new Size(63, 27);
            chkWednesday.TabIndex = 3;
            chkWednesday.Text = "الأربعاء";
            chkWednesday.UseVisualStyleBackColor = true;
            // 
            // chkTuesday
            // 
            chkTuesday.AutoSize = true;
            chkTuesday.Checked = true;
            chkTuesday.CheckState = CheckState.Checked;
            chkTuesday.Font = new Font("Cairo", 8.999999F);
            chkTuesday.Location = new Point(290, 25);
            chkTuesday.Name = "chkTuesday";
            chkTuesday.RightToLeft = RightToLeft.Yes;
            chkTuesday.Size = new Size(60, 27);
            chkTuesday.TabIndex = 2;
            chkTuesday.Text = "الثلاثاء";
            chkTuesday.UseVisualStyleBackColor = true;
            // 
            // chkMonday
            // 
            chkMonday.AutoSize = true;
            chkMonday.Checked = true;
            chkMonday.CheckState = CheckState.Checked;
            chkMonday.Font = new Font("Cairo", 8.999999F);
            chkMonday.Location = new Point(360, 25);
            chkMonday.Name = "chkMonday";
            chkMonday.RightToLeft = RightToLeft.Yes;
            chkMonday.Size = new Size(61, 27);
            chkMonday.TabIndex = 1;
            chkMonday.Text = "الاثنين";
            chkMonday.UseVisualStyleBackColor = true;
            // 
            // chkSunday
            // 
            chkSunday.AutoSize = true;
            chkSunday.Checked = true;
            chkSunday.CheckState = CheckState.Checked;
            chkSunday.Font = new Font("Cairo", 8.999999F);
            chkSunday.Location = new Point(430, 25);
            chkSunday.Name = "chkSunday";
            chkSunday.RightToLeft = RightToLeft.Yes;
            chkSunday.Size = new Size(51, 27);
            chkSunday.TabIndex = 0;
            chkSunday.Text = "الأحد";
            chkSunday.UseVisualStyleBackColor = true;
            // 
            // dateTimePickerEnd
            // 
            dateTimePickerEnd.Font = new Font("Cairo", 9.75F);
            dateTimePickerEnd.Format = DateTimePickerFormat.Short;
            dateTimePickerEnd.Location = new Point(540, 129);
            dateTimePickerEnd.Name = "dateTimePickerEnd";
            dateTimePickerEnd.Size = new Size(200, 32);
            dateTimePickerEnd.TabIndex = 9;
            // 
            // dateTimePickerStart
            // 
            dateTimePickerStart.Font = new Font("Cairo", 9.75F);
            dateTimePickerStart.Format = DateTimePickerFormat.Short;
            dateTimePickerStart.Location = new Point(840, 129);
            dateTimePickerStart.Name = "dateTimePickerStart";
            dateTimePickerStart.Size = new Size(200, 32);
            dateTimePickerStart.TabIndex = 8;
            // 
            // lblEndDate
            // 
            lblEndDate.AutoSize = true;
            lblEndDate.Font = new Font("Cairo", 9.75F);
            lblEndDate.Location = new Point(745, 133);
            lblEndDate.Name = "lblEndDate";
            lblEndDate.Size = new Size(77, 24);
            lblEndDate.TabIndex = 7;
            lblEndDate.Text = "تاريخ النهاية:";
            // 
            // lblStartDate
            // 
            lblStartDate.AutoSize = true;
            lblStartDate.Font = new Font("Cairo", 9.75F);
            lblStartDate.Location = new Point(1045, 133);
            lblStartDate.Name = "lblStartDate";
            lblStartDate.Size = new Size(74, 24);
            lblStartDate.TabIndex = 6;
            lblStartDate.Text = "تاريخ البداية:";
            // 
            // txtDescription
            // 
            txtDescription.Font = new Font("Cairo", 9.75F);
            txtDescription.Location = new Point(580, 80);
            txtDescription.Multiline = true;
            txtDescription.Name = "txtDescription";
            txtDescription.Size = new Size(450, 25);
            txtDescription.TabIndex = 5;
            // 
            // lblDescription
            // 
            lblDescription.AutoSize = true;
            lblDescription.Font = new Font("Cairo", 9.75F);
            lblDescription.Location = new Point(1050, 83);
            lblDescription.Name = "lblDescription";
            lblDescription.Size = new Size(50, 24);
            lblDescription.TabIndex = 4;
            lblDescription.Text = "الوصف:";
            // 
            // txtProjectName
            // 
            txtProjectName.Font = new Font("Cairo", 9.75F);
            txtProjectName.Location = new Point(580, 40);
            txtProjectName.Name = "txtProjectName";
            txtProjectName.Size = new Size(450, 32);
            txtProjectName.TabIndex = 3;
            // 
            // lblProjectName
            // 
            lblProjectName.AutoSize = true;
            lblProjectName.Font = new Font("Cairo", 9.75F);
            lblProjectName.Location = new Point(1050, 43);
            lblProjectName.Name = "lblProjectName";
            lblProjectName.Size = new Size(77, 24);
            lblProjectName.TabIndex = 2;
            lblProjectName.Text = "مكان العمل:";
            // 
            // cmbEmployee
            // 
            cmbEmployee.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbEmployee.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cmbEmployee.FormattingEnabled = true;
            cmbEmployee.Location = new Point(36, 40);
            cmbEmployee.Name = "cmbEmployee";
            cmbEmployee.Size = new Size(450, 32);
            cmbEmployee.TabIndex = 1;
            cmbEmployee.SelectedIndexChanged += cmbEmployee_SelectedIndexChanged;
            // 
            // lblEmployee
            // 
            lblEmployee.AutoSize = true;
            lblEmployee.Font = new Font("Cairo", 9.75F);
            lblEmployee.Location = new Point(496, 43);
            lblEmployee.Name = "lblEmployee";
            lblEmployee.Size = new Size(59, 24);
            lblEmployee.TabIndex = 0;
            lblEmployee.Text = "الموظف:";
            // 
            // groupBoxActions
            // 
            groupBoxActions.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxActions.Controls.Add(btnClear);
            groupBoxActions.Controls.Add(btnDelete);
            groupBoxActions.Controls.Add(btnUpdate);
            groupBoxActions.Controls.Add(btnAdd);
            groupBoxActions.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxActions.Location = new Point(12, 300);
            groupBoxActions.Name = "groupBoxActions";
            groupBoxActions.RightToLeft = RightToLeft.Yes;
            groupBoxActions.Size = new Size(580, 80);
            groupBoxActions.TabIndex = 1;
            groupBoxActions.TabStop = false;
            groupBoxActions.Text = "العمليات";
            // 
            // btnClear
            // 
            btnClear.BackColor = Color.FromArgb(108, 117, 125);
            btnClear.FlatStyle = FlatStyle.Flat;
            btnClear.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnClear.ForeColor = Color.White;
            btnClear.Location = new Point(20, 30);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(120, 35);
            btnClear.TabIndex = 3;
            btnClear.Text = "مسح الحقول";
            btnClear.UseVisualStyleBackColor = false;
            btnClear.Click += btnClear_Click;
            // 
            // btnDelete
            // 
            btnDelete.BackColor = Color.FromArgb(220, 53, 69);
            btnDelete.FlatStyle = FlatStyle.Flat;
            btnDelete.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnDelete.ForeColor = Color.White;
            btnDelete.Location = new Point(160, 30);
            btnDelete.Name = "btnDelete";
            btnDelete.Size = new Size(120, 35);
            btnDelete.TabIndex = 2;
            btnDelete.Text = "حذف";
            btnDelete.UseVisualStyleBackColor = false;
            btnDelete.Click += btnDelete_Click;
            // 
            // btnUpdate
            // 
            btnUpdate.BackColor = Color.FromArgb(255, 193, 7);
            btnUpdate.FlatStyle = FlatStyle.Flat;
            btnUpdate.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnUpdate.ForeColor = Color.Black;
            btnUpdate.Location = new Point(300, 30);
            btnUpdate.Name = "btnUpdate";
            btnUpdate.Size = new Size(120, 35);
            btnUpdate.TabIndex = 1;
            btnUpdate.Text = "تحديث";
            btnUpdate.UseVisualStyleBackColor = false;
            btnUpdate.Click += btnUpdate_Click;
            // 
            // btnAdd
            // 
            btnAdd.BackColor = Color.FromArgb(40, 167, 69);
            btnAdd.FlatStyle = FlatStyle.Flat;
            btnAdd.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnAdd.ForeColor = Color.White;
            btnAdd.Location = new Point(440, 30);
            btnAdd.Name = "btnAdd";
            btnAdd.Size = new Size(120, 35);
            btnAdd.TabIndex = 0;
            btnAdd.Text = "إضافة";
            btnAdd.UseVisualStyleBackColor = false;
            btnAdd.Click += btnAdd_Click;
            // 
            // groupBoxWorkPeriodList
            // 
            groupBoxWorkPeriodList.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxWorkPeriodList.Controls.Add(btnRefresh);
            groupBoxWorkPeriodList.Controls.Add(btnDailyTracking);
            groupBoxWorkPeriodList.Controls.Add(chkSelectAll);
            groupBoxWorkPeriodList.Controls.Add(btnDeleteSelected);
            groupBoxWorkPeriodList.Controls.Add(cmbSearchGroup);
            groupBoxWorkPeriodList.Controls.Add(lblSearchGroup);
            groupBoxWorkPeriodList.Controls.Add(dateTimePickerSearchStart);
            groupBoxWorkPeriodList.Controls.Add(dateTimePickerSearchEnd);
            groupBoxWorkPeriodList.Controls.Add(lblSearchPeriod);
            groupBoxWorkPeriodList.Controls.Add(btnAdvancedSearch);
            groupBoxWorkPeriodList.Controls.Add(btnSearch);
            groupBoxWorkPeriodList.Controls.Add(txtSearch);
            groupBoxWorkPeriodList.Controls.Add(lblSearch);
            groupBoxWorkPeriodList.Controls.Add(dataGridViewWorkPeriods);
            groupBoxWorkPeriodList.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxWorkPeriodList.Location = new Point(12, 390);
            groupBoxWorkPeriodList.Name = "groupBoxWorkPeriodList";
            groupBoxWorkPeriodList.RightToLeft = RightToLeft.Yes;
            groupBoxWorkPeriodList.Size = new Size(1160, 350);
            groupBoxWorkPeriodList.TabIndex = 2;
            groupBoxWorkPeriodList.TabStop = false;
            groupBoxWorkPeriodList.Text = "قائمة فترات العمل";
            // 
            // btnRefresh
            // 
            btnRefresh.BackColor = Color.FromArgb(0, 123, 255);
            btnRefresh.FlatStyle = FlatStyle.Flat;
            btnRefresh.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.Location = new Point(20, 24);
            btnRefresh.Name = "btnRefresh";
            btnRefresh.Size = new Size(100, 32);
            btnRefresh.TabIndex = 4;
            btnRefresh.Text = "تحديث";
            btnRefresh.UseVisualStyleBackColor = false;
            btnRefresh.Click += btnRefresh_Click;
            // 
            // btnDailyTracking
            // 
            btnDailyTracking.BackColor = Color.FromArgb(255, 165, 0);
            btnDailyTracking.FlatStyle = FlatStyle.Flat;
            btnDailyTracking.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnDailyTracking.ForeColor = Color.White;
            btnDailyTracking.Location = new Point(260, 24);
            btnDailyTracking.Name = "btnDailyTracking";
            btnDailyTracking.Size = new Size(120, 32);
            btnDailyTracking.TabIndex = 5;
            btnDailyTracking.Text = "التتبع اليومي";
            btnDailyTracking.UseVisualStyleBackColor = false;
            btnDailyTracking.Click += btnDailyTracking_Click;
            // 
            // chkSelectAll
            // 
            chkSelectAll.AutoSize = true;
            chkSelectAll.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            chkSelectAll.ForeColor = Color.Blue;
            chkSelectAll.Location = new Point(815, 33);
            chkSelectAll.Name = "chkSelectAll";
            chkSelectAll.RightToLeft = RightToLeft.No;
            chkSelectAll.Size = new Size(93, 28);
            chkSelectAll.TabIndex = 5;
            chkSelectAll.Text = "تحديد الكل";
            chkSelectAll.UseVisualStyleBackColor = true;
            chkSelectAll.CheckedChanged += chkSelectAll_CheckedChanged;
            // 
            // btnDeleteSelected
            // 
            btnDeleteSelected.BackColor = Color.FromArgb(220, 53, 69);
            btnDeleteSelected.FlatStyle = FlatStyle.Flat;
            btnDeleteSelected.Font = new Font("Cairo", 9.75F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnDeleteSelected.ForeColor = Color.White;
            btnDeleteSelected.Location = new Point(920, 30);
            btnDeleteSelected.Name = "btnDeleteSelected";
            btnDeleteSelected.Size = new Size(120, 31);
            btnDeleteSelected.TabIndex = 6;
            btnDeleteSelected.Text = "حذف المحدد";
            btnDeleteSelected.UseVisualStyleBackColor = false;
            btnDeleteSelected.Click += btnDeleteSelected_Click;
            // 
            // cmbSearchGroup
            // 
            cmbSearchGroup.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbSearchGroup.Font = new Font("Cairo", 8.999999F, FontStyle.Regular, GraphicsUnit.Point, 0);
            cmbSearchGroup.FormattingEnabled = true;
            cmbSearchGroup.Location = new Point(20, 62);
            cmbSearchGroup.Name = "cmbSearchGroup";
            cmbSearchGroup.Size = new Size(200, 31);
            cmbSearchGroup.TabIndex = 10;
            // 
            // lblSearchGroup
            // 
            lblSearchGroup.AutoSize = true;
            lblSearchGroup.Font = new Font("Cairo", 8.999999F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblSearchGroup.Location = new Point(230, 66);
            lblSearchGroup.Name = "lblSearchGroup";
            lblSearchGroup.Size = new Size(107, 23);
            lblSearchGroup.TabIndex = 11;
            lblSearchGroup.Text = "البحث بالمجموعة:";
            // 
            // dateTimePickerSearchStart
            // 
            dateTimePickerSearchStart.Font = new Font("Cairo", 8.999999F);
            dateTimePickerSearchStart.Format = DateTimePickerFormat.Short;
            dateTimePickerSearchStart.Location = new Point(350, 62);
            dateTimePickerSearchStart.Name = "dateTimePickerSearchStart";
            dateTimePickerSearchStart.Size = new Size(120, 30);
            dateTimePickerSearchStart.TabIndex = 12;
            // 
            // dateTimePickerSearchEnd
            // 
            dateTimePickerSearchEnd.Font = new Font("Cairo", 8.999999F);
            dateTimePickerSearchEnd.Format = DateTimePickerFormat.Short;
            dateTimePickerSearchEnd.Location = new Point(480, 62);
            dateTimePickerSearchEnd.Name = "dateTimePickerSearchEnd";
            dateTimePickerSearchEnd.Size = new Size(120, 30);
            dateTimePickerSearchEnd.TabIndex = 13;
            // 
            // lblSearchPeriod
            // 
            lblSearchPeriod.AutoSize = true;
            lblSearchPeriod.Font = new Font("Cairo", 8.999999F, FontStyle.Bold, GraphicsUnit.Point, 0);
            lblSearchPeriod.Location = new Point(610, 66);
            lblSearchPeriod.Name = "lblSearchPeriod";
            lblSearchPeriod.Size = new Size(71, 23);
            lblSearchPeriod.TabIndex = 14;
            lblSearchPeriod.Text = "فترة البحث:";
            // 
            // btnAdvancedSearch
            // 
            btnAdvancedSearch.BackColor = Color.FromArgb(0, 123, 255);
            btnAdvancedSearch.FlatStyle = FlatStyle.Flat;
            btnAdvancedSearch.Font = new Font("Cairo", 8.999999F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnAdvancedSearch.ForeColor = Color.White;
            btnAdvancedSearch.Location = new Point(700, 58);
            btnAdvancedSearch.Name = "btnAdvancedSearch";
            btnAdvancedSearch.Size = new Size(100, 34);
            btnAdvancedSearch.TabIndex = 15;
            btnAdvancedSearch.Text = "بحث متقدم";
            btnAdvancedSearch.UseVisualStyleBackColor = false;
            btnAdvancedSearch.Click += btnAdvancedSearch_Click;
            // 
            // btnSearch
            // 
            btnSearch.BackColor = Color.FromArgb(40, 167, 69);
            btnSearch.FlatStyle = FlatStyle.Flat;
            btnSearch.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            btnSearch.ForeColor = Color.White;
            btnSearch.Location = new Point(390, 24);
            btnSearch.Name = "btnSearch";
            btnSearch.Size = new Size(100, 32);
            btnSearch.TabIndex = 3;
            btnSearch.Text = "بحث";
            btnSearch.UseVisualStyleBackColor = false;
            btnSearch.Click += btnSearch_Click;
            // 
            // txtSearch
            // 
            txtSearch.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            txtSearch.Location = new Point(500, 24);
            txtSearch.Name = "txtSearch";
            txtSearch.Size = new Size(250, 32);
            txtSearch.TabIndex = 2;
            // 
            // lblSearch
            // 
            lblSearch.AutoSize = true;
            lblSearch.Font = new Font("Cairo", 9.75F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lblSearch.Location = new Point(760, 27);
            lblSearch.Name = "lblSearch";
            lblSearch.Size = new Size(197, 24);
            lblSearch.TabIndex = 1;
            lblSearch.Text = "بحث (اسم الموظف أو مكان العمل):";
            // 
            // dataGridViewWorkPeriods
            // 
            dataGridViewWorkPeriods.AllowUserToAddRows = false;
            dataGridViewWorkPeriods.AllowUserToDeleteRows = false;
            dataGridViewWorkPeriods.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridViewWorkPeriods.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridViewWorkPeriods.BackgroundColor = Color.White;
            dataGridViewWorkPeriods.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridViewWorkPeriods.Location = new Point(20, 97);
            dataGridViewWorkPeriods.Name = "dataGridViewWorkPeriods";
            dataGridViewWorkPeriods.ReadOnly = true;
            dataGridViewWorkPeriods.RightToLeft = RightToLeft.Yes;
            dataGridViewWorkPeriods.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridViewWorkPeriods.Size = new Size(1120, 243);
            dataGridViewWorkPeriods.TabIndex = 0;
            dataGridViewWorkPeriods.DataError += dataGridViewWorkPeriods_DataError;
            dataGridViewWorkPeriods.SelectionChanged += dataGridViewWorkPeriods_SelectionChanged;
            // 
            // groupBoxSummary
            // 
            groupBoxSummary.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            groupBoxSummary.Controls.Add(lblTotalHours);
            groupBoxSummary.Controls.Add(lblTotalDays);
            groupBoxSummary.Controls.Add(lblSelectedPeriod);
            groupBoxSummary.Font = new Font("Cairo", 12F, FontStyle.Bold, GraphicsUnit.Point, 0);
            groupBoxSummary.Location = new Point(600, 300);
            groupBoxSummary.Name = "groupBoxSummary";
            groupBoxSummary.RightToLeft = RightToLeft.Yes;
            groupBoxSummary.Size = new Size(572, 80);
            groupBoxSummary.TabIndex = 3;
            groupBoxSummary.TabStop = false;
            groupBoxSummary.Text = "ملخص الفترة المحددة";
            // 
            // lblTotalHours
            // 
            lblTotalHours.AutoSize = true;
            lblTotalHours.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblTotalHours.ForeColor = Color.Blue;
            lblTotalHours.Location = new Point(20, 50);
            lblTotalHours.Name = "lblTotalHours";
            lblTotalHours.Size = new Size(113, 24);
            lblTotalHours.TabIndex = 2;
            lblTotalHours.Text = "إجمالي الساعات: 0";
            // 
            // lblTotalDays
            // 
            lblTotalDays.AutoSize = true;
            lblTotalDays.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblTotalDays.ForeColor = Color.Green;
            lblTotalDays.Location = new Point(200, 50);
            lblTotalDays.Name = "lblTotalDays";
            lblTotalDays.Size = new Size(96, 24);
            lblTotalDays.TabIndex = 1;
            lblTotalDays.Text = "إجمالي الأيام: 0";
            // 
            // lblSelectedPeriod
            // 
            lblSelectedPeriod.AutoSize = true;
            lblSelectedPeriod.Font = new Font("Cairo", 9.75F, FontStyle.Bold);
            lblSelectedPeriod.Location = new Point(350, 25);
            lblSelectedPeriod.Name = "lblSelectedPeriod";
            lblSelectedPeriod.Size = new Size(111, 24);
            lblSelectedPeriod.TabIndex = 0;
            lblSelectedPeriod.Text = "لم يتم اختيار فترة";
            // 
            // WorkPeriodForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.White;
            ClientSize = new Size(1184, 751);
            Controls.Add(groupBoxSummary);
            Controls.Add(groupBoxWorkPeriodList);
            Controls.Add(groupBoxActions);
            Controls.Add(groupBoxWorkPeriod);
            Name = "WorkPeriodForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            Text = "إدارة فترات العمل";
            Load += WorkPeriodForm_Load;
            groupBoxWorkPeriod.ResumeLayout(false);
            groupBoxWorkPeriod.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numericUpDownDailyHours).EndInit();
            groupBoxCalendar.ResumeLayout(false);
            groupBoxCalendar.PerformLayout();
            groupBoxWorkingDays.ResumeLayout(false);
            groupBoxWorkingDays.PerformLayout();
            groupBoxActions.ResumeLayout(false);
            groupBoxWorkPeriodList.ResumeLayout(false);
            groupBoxWorkPeriodList.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridViewWorkPeriods).EndInit();
            groupBoxSummary.ResumeLayout(false);
            groupBoxSummary.PerformLayout();
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox groupBoxWorkPeriod;
        private System.Windows.Forms.Label lblEmployee;
        private System.Windows.Forms.ComboBox cmbEmployee;
        private System.Windows.Forms.TextBox txtProjectName;
        private System.Windows.Forms.Label lblProjectName;
        private System.Windows.Forms.TextBox txtDescription;
        private System.Windows.Forms.Label lblDescription;
        private System.Windows.Forms.DateTimePicker dateTimePickerStart;
        private System.Windows.Forms.Label lblStartDate;
        private System.Windows.Forms.DateTimePicker dateTimePickerEnd;
        private System.Windows.Forms.Label lblEndDate;
        private System.Windows.Forms.GroupBox groupBoxWorkingDays;
        private System.Windows.Forms.CheckBox chkSunday;
        private System.Windows.Forms.CheckBox chkMonday;
        private System.Windows.Forms.CheckBox chkTuesday;
        private System.Windows.Forms.CheckBox chkWednesday;
        private System.Windows.Forms.CheckBox chkThursday;
        private System.Windows.Forms.CheckBox chkFriday;
        private System.Windows.Forms.CheckBox chkSaturday;
        private System.Windows.Forms.MonthCalendar monthCalendar;
        private System.Windows.Forms.Label lblCalendarTitle;
        private System.Windows.Forms.GroupBox groupBoxCalendar;
        private System.Windows.Forms.ComboBox cmbStatus;
        private System.Windows.Forms.Label lblStatus;
        private System.Windows.Forms.NumericUpDown numericUpDownDailyHours;
        private System.Windows.Forms.Label lblDailyHours;
        private System.Windows.Forms.GroupBox groupBoxActions;
        private System.Windows.Forms.Button btnAdd;
        private System.Windows.Forms.Button btnUpdate;
        private System.Windows.Forms.Button btnDelete;
        private System.Windows.Forms.Button btnClear;
        private System.Windows.Forms.GroupBox groupBoxWorkPeriodList;
        private System.Windows.Forms.DataGridView dataGridViewWorkPeriods;
        private System.Windows.Forms.TextBox txtSearch;
        private System.Windows.Forms.Label lblSearch;
        private System.Windows.Forms.Button btnSearch;
        private System.Windows.Forms.CheckBox chkSelectAll;
        private System.Windows.Forms.Button btnDeleteSelected;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.Button btnDailyTracking;
        private System.Windows.Forms.ComboBox cmbSearchGroup;
        private System.Windows.Forms.Label lblSearchGroup;
        private System.Windows.Forms.DateTimePicker dateTimePickerSearchStart;
        private System.Windows.Forms.DateTimePicker dateTimePickerSearchEnd;
        private System.Windows.Forms.Label lblSearchPeriod;
        private System.Windows.Forms.Button btnAdvancedSearch;
        private System.Windows.Forms.GroupBox groupBoxSummary;
        private System.Windows.Forms.Label lblSelectedPeriod;
        private System.Windows.Forms.Label lblTotalDays;
        private System.Windows.Forms.Label lblTotalHours;
    }
}