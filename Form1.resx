﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolTip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="previewBox.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAB9qSURBVHhe7d1b
        jJznfd9xw00cw85127TphVPncNOD05teuAYSoAdJlixZsiVZpM6kSMmUKFoHyzytKB4kUsuDRHK1O0tR
        3NkR5aXUiJpZJjYS6DpwetsmdeJGioOmBzhNXLR3ejvvaqaWlg/JPczzzvO+z+cLfFAgAVJ73sX+fyR3
        Zz4hSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIk
        SZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIk
        SZIkSZIkSVKzOrWw8Iuvdnq/0ur0/sVsZ/FLM/OXrpvt9L4GwOh8+L118UvT7e5vlt9z5+a+99nBt2Ep
        ftOvd39jtr34UKvdO92a7/1+31/0FQCMw+L7/f/398vvyeX35jPt3/31wbdraX2Vf7rvL86N/S+u+f4X
        2V9+/AsPgASV36vbfRvK7+GDb+fStZuYmPhk/0/6X+wf/en+F9BPB19QANTP/+1bmH29e+PEu+/+3ODb
        vPTxFhYWPtVqd+9udXp/suwLCICam53v/ZeZdvexs2ff/fTg275yr/xi6H9xbO/78Ue/WABoosX3W53F
        Rw2BzGvNXfrt/hfEf7r8CwSARmv3/rT8zYLBOVAuvXr+7X/U/wJ467IvCAByc6E1f+mXB+dBTW623b25
        /8B/suwLAIBMzcz3/qY13719cCbUtF566dIv9B/widDDB4Dyt7/KWzE4G2pCZ85f/Af9h/sfLnvYAPBR
        nd4Pptu/90uD86E6t/Q2vfO9H172kAEgYOlXBs8t/trgjKiOle/P35rv/rfQAwaAq/ifrflL/3JwTlSn
        yuP/4Q92BB8sAFxVeUPKDx8anBXVodn2xc/3H95fLX+YALBK/8OHDNWk8gf+yn+/CTxEAFiLH/nBwMT7
        8Ff9/LQ/ACPW6f3ArwgmXP9P/qeCDw4A1q17YnBulFL9B3Nb+IEBwIh0el8dnB2l0OC9/f/XZQ8KAEbr
        r312QEL1H4gP9gGgKguD86NxNtPp/dvAwwGAeDrd6wdnSONo6af+O70/CT4cAIjnh2fPvvvpwTlS1fUf
        wPZlDwQAKjEz39s2OEeqsunpP/r5/gN4b/kDAYCK/MXCwsKnBmdJVdXq9DYHHgYAVKfTe2BwllRF/cX1
        d/ov/J9d9iAAoFo/nJiY+OTgPCl2Z+a7/ybwEACgenOXfntwnhS7/gvevuwBAMAYzM73zg7Ok2I2N/e9
        z/Zf8J8ufwAAMCb/+9TCwi8OzpRiNdtZ3Bh48QFgfNqLdw7OlGLVavfmgy8+AIzPa4MzpVj1X+QfL3vR
        AWDMFt8fnCnFaPr17m+EX3gAGK9XOt1fHZwrjbqZ+e6W0IsOAGPX6W0enCuNula7dzr4ogPAmM10ei8P
        zpVGXavT/YPQiw4AY9fpfX9wrjTq+i+wHwAEIFXvDc6VRln5Jgv9F/eDZS82AKTig+np7mcGZ0ujarZ9
        8fOBFxsA0jH3zucGZ0ujavb8pS8EX2wASMSZdvefDc6WRtVsZ/FLoRcbAFIx/Xr3i4OzpVHV6nSvD73Y
        AJCKmflL1w3OlkZVa757W+jFBoB0dG8bnC2NqtlO72vhFxsA0lDeqsHZ0qgyAABInQEQIQMAgNQZABEy
        AABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEy
        AABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEy
        AABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEy
        AABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEy
        AABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEyAABInQEQIQMAgNQZABEyAABInQEQIQOA069dLF44
        +Uax+/DZYvvu08UDOyaLjd98vrhz64Hitk37ipvv21vcePfu4rq7vlPcdO+e4qsPPFt8ffNzxZ0PHyzu
        efSFYstTx4snn50unp2cK45OXyim57rB/38A1soAiJABkJ/jM28WOw+dKTY/dbS45f6JpcM+Stdv3Fnc
        ueVA8eiuk8VzR+eWBkboPwfAShkAETIA8nDwRKfY+vSJ4tb+n+hDRzumchB84+FDxY6JqaXxEfrPB3A1
        BkCEDIDmOvrKhaWjW/41fugwj0v5TwffPtAqTr36dvA/N8ByBkCEDIBmmWl3i31H54q7HjkUPL4p+fLG
        XUs/P3Bs5kLwvwvAkAEQIQOgOfZNtpf+dB06tqm77/EXiyOn3gj+9wIwACJkANTf3iPniq9tei54WOvk
        +g07i/t3vFgcn/ZzAsDHGQARMgDqqzyU924/EjymdXbDhl3Ftp0vF1OvvRP87w3kxwCIkAFQP9Pn3ln6
        4b7y39BDB7Qpyh9e3HesHXwNgLwYABEyAOrlxdPfbcRf96/GQ08e87cBkDkDIEIGQH2U79TX9D/1X0k5
        eo5MfTf4ugDNZwBEyABI39S5i8XmJ44GD2NOyvFTvn9A6DUCms0AiJABkLYTM28uve9+6CDmqnxHw/L9
        DkKvF9BMBkCEDIB0Tb6ysPTBO6EjmLv7dxxZ+mHI0OsGNI8BECEDIE2HT50vbr53T/D48aHy3Q5PnvV2
        wpADAyBCBkB69h9rFzfcnecP+63W7Vv2Fydf/Z3g6wg0hwEQIQMgLS+cPP//P3uflblj634fOQwNZwBE
        yABIx+TUheIr9/lr/7W4+9EX/EwANJgBECEDIA0nWv++uPXBieBxY2UeePxFvx0ADWUARMgAGL/yr6/9
        qt9oPLrrZPA1BurNAIiQATB+m5/yJj+jUn6i4LOTc8HXGagvAyBCBsB4PXNgNnjIWLub7tldHG+9FXy9
        gXoyACJkAIzP5NSCX/eL5BsPHyym5/w8ADSFARAhA2A8yp9Yz+1T/ar22O5TwdceqB8DIEIGwHg8vud0
        8GgxOtdv3OkTBKEhDIAIGQDVOzbzZrYf61u1DdsO+dVAaAADIEIGQPXue/xI8FgRx64XXg0+B6A+DIAI
        GQDV2nvkXPBIEc/N9+0tTvvQIKg1AyBCBkB1yr+K9oN/41H+zEXomQD1YABEyACozt7DrwWPE/HddO8e
        HxgENWYARMgAqEb5p/87Hz4YPE5U44l9rwSfDZA+AyBCBkA19h2dCx4lqnPzvXuKqdd8YiDUkQEQIQOg
        Gnc9cih4lKjWtw+0gs8HSJsBECEDIL5jMxeCx4jq3f7Q/uAzAtJmAETIAIhv+55TwWPEeLx4eiH4nIB0
        GQARMgDiu/XBieAhYjwe230y+JyAdBkAETIA4jr4Uid4hBifW+6f8PbAUDMGQIQMgLi2Pn0ieIQYrwMn
        OsHnBaTJAIiQARDXrZv2BQ8Q47V9t3cGhDoxACJkAMRzfObN4PFh/O565GDwmQFpMgAiZADEs/P5M8Hj
        w/hdv2GntwaGGjEAImQAxLP5yWPB40Ma9k22g88NSI8BECEDIJ7yp81Dh4c0bNv5cvC5AekxACJkAMRR
        /vVy6OiQjnu3Hw4+OyA9BkCEDIA4Dp86Hzw6pKP8DY3QswPSYwBEyACIY/fhs8GjQ1qmz/l0QKgDAyBC
        BkAc5e+Zhw4OaZmc8rkAUAcGQIQMgDge2DEZPDikZe+Rc8HnB6TFAIiQARDHhm2HggeHtDxzoBV8fkBa
        DIAIGQBx3LH1QPDgkJYn9r0SfH5AWgyACBkAcdy22WcA1IHPBIB6MAAiZADEcfN9e4MHh7R88zveDAjq
        wACIkAEQx5c37goeHNKy5enjwecHpMUAiJABEEf5YTOhg0NaNj15NPj8gLQYABEyAOK46Z7dwYNDWrY+
        fSL4/IC0GAARMgDi8EFA9fDorpPB5wekxQCIkAEQx9c3Pxc8OKRlx96p4PMD0mIARMgAiOMbWw8GDw5p
        eXLfdPD5AWkxACJkAMRx97YXggeHtDxzaDb4/IC0GAARMgDiKH+9LHRwSMv+Y/PB5wekxQCIkAEQR/kW
        s6GDQ1pOzLwZfH5AWgyACBkAcTw7ORc8OKSjfLOmmXY3+PyAtBgAETIA4ph8ZSF4dEjH7Vv2B58dkB4D
        IEIGQBzTc93i+o3eDTBlD35rMvjsgPQYABEyAOLxkcBpe+JZvwIIdWEARMgAiKd8l7nQ4SENh0+dDz43
        ID0GQIQMgHj2TbaDh4fxu/Hu3cXMnB8AhLowACJkAMQzde6inwNI1P07jgSfGZAmAyBCBkBcdz3iLYFT
        9NRzM8HnBaTJAIiQARDXt571hkApOjZzIfi8gDQZABEyAOIq32nu+g3+GSAl33j4UPBZAekyACJkAMS3
        Yduh4CFiPJ450Ao+JyBdBkCEDID4vnPwTPAQUb0bNuwqTr36dvA5AekyACJkAMR3+uzbxQ137woeJKp1
        /44Xg88ISJsBECEDoBpbnvLxwCnw8b9QTwZAhAyAahyf9sOA43bn1gM+/Q9qygCIkAFQnU1PTAYPE9XY
        NzkXfC5A+gyACBkA1ZmcuhA8TMR3+5bngs8EqAcDIEIGQLXKH0ILHSji2nvkXPB5APVgAETIAKjWsZk3
        iy9v9BsBVSrfh8G//UO9GQARMgCqt33vVPBQMXrlhzEdmfpu8DkA9WEARMgAqN70uXeK2zbtCx4sRmvb
        zpeDzwCoFwMgQgbAeOw/3g4eLEbnlvsmlt6EKfT6A/ViAETIABifh548FjxcrF/5ngv7jrWDrztQPwZA
        hAyA8Zk6d7H4+ubnggeM9Xls16ngaw7UkwEQIQNgvCZfWfBbASN21yMHi+k5P/UPTWIARMgAGD+fFjg6
        N927pzgx82bwdQbqywCIkAGQhq1PnwgeNFau/Kjf/cd92A80kQEQIQMgDeUb1Tyww2cFrFX5Q3+7nn81
        +NoC9WcARMgASEf5/gAbtz0fPHBc3VPPzQRfU6AZDIAIGQBpOf3axeKOrQeCR46wx3b7iX9oOgMgQgZA
        ek6++jvFnQ8fDB47Pq78dT/v8w/NZwBEyABI09Rr7xT3bj8cPHp8+G/+T+6bDr52QPMYABEyANJV/i77
        piePBg9gzsoP+NnpB/4gKwZAhAyA9D266+TSn3hDxzA3X7lvT7H/mF/1g9wYABEyAOph39G5peMXOoq5
        +MbDB4vjrbeCrw/QbAZAhAyA+nhp9q3irkcOBY9jk5V/+1F+rK+394V8GQARMgDqpTyC5a+9lf8OHjqW
        TfPVByZ8qh9gAMTIAKino9MXinsefSF4NJugHDjln/rL90UI/fcH8mIARMgAqK/y9993Hz5b3Hz/3uAR
        rasN2w4VL55eCP53BvJkAETIAKi/02ffLrbvnSpuumd38KDWxe1b9hd7j5zzxj7AZQyACBkAzTF17mLx
        7QOt4pb7J4IHNlV3bjmw9DcZof9OACUDIEIGQPOUQ+Dp/a3ijq37gwc3BTds3Fnc//iLfqcfWBEDIEIG
        QLNNTl0odkxMFbc++GzwEFft9of2F08+O730eQeh/7wAIQZAhAyAPJT/rn7geKd4bM+p4q5HDlb2zoLl
        zyXc1/+Tfvk3EsdmLgT/swFciwEQIQMgT+UPDu6bbC/9qt29jx0ubt20L3jAV+PLG3ct/bPDg9+aLJ7o
        /yn/8KnzxYw37wFGwACIkAHA0PS5d4ojU99d+kn8Zw60lv6qvvwbg28+81Kx5enjxaYnJouHnzmx9NkE
        j+85vfRpfDsPnVn6d3xv0Usdle+uGfqfkx4DIEIGAJCjI6feKG66d8/SyA3970mLARAhAwDIzfD4D//5
        yghInwEQIQMAyMny428E1IMBECEDAMjFlY7/kBGQLgMgQgYAkINrHf8hIyBNBkCEDACg6VZ6/IeMgPQY
        ABEyAIAmW+3xHzIC0mIARMgAAJpqrcd/yAhIhwEQIQMAaKL1Hv8hIyANBkCEDACgaUZ1/IeMgPEzACJk
        AABNMurjP2QEjJcBECEDAGiKWMd/yAgYHwMgQgYA0ASxj/+QETAeBkCEDACg7qo6/kNGQPUMgAgZAECd
        VX38h4yAahkAETIAgLoa1/EfMgKqYwBEyAAA6mjcx3/ICKiGARAhAwCom1SO/5AREJ8BECEDAKiT518+
        X9x0z+7gIR4nIyAuAyBCBgBQF6ke/yEjIB4DIEIGAFAHqR//ISMgDgMgQgYAkLq6HP8hI2D0DIAIGQBA
        yup2/IeMgNEyACJkAACpquvxHzICRscAiJABAKSo7sd/yAgYDQMgQgYAkJqmHP8hI2D9DIAIGQBASpp2
        /IeMgPUxACJkAACpaOrxHzIC1s4AiJABAKSg6cd/yAhYGwMgQgYAMG65HP8hI2D1DIAIGQDAOOV2/IeM
        gNUxACJkAADjkuvxHzICVs4AiJABAIxD7sd/yAhYGQMgQgYAUDXH/+OMgGszACJkAABVcvzDjICrMwAi
        ZAAAVXH8r84IuDIDIEIGAFAFx39ljIAwAyBCBgAQm+O/OkbA5QyACBkAQEyO/9oYAR9nAETIAABicfzX
        xwj4GQMgQgYAEIPjPxpGwIcMgAgZAMCoOf6jZQQYAFEyAIBRcvzjyH0EGAARMgCAUXH848p5BBgAETIA
        gFFw/KuR6wgwACJkAADr5fhXK8cRYABEyAAA1sPxH4/cRoABECEDAFgrx3+8choBBkCEDABgLRz/NOQy
        AgyACBkAwGo5/mnJYQQYABEyAIDVcPzT1PQRYABEyAAAVsrxT1uTR4ABECEDAFgJx78emjoCDIAIGQDA
        tTj+9dLEEWAARMgAAK7G8a+npo0AAyBCBgBwJY5/vTVpBBgAETIAgBDHvxmaMgIMgAgZAMByjn+zNGEE
        GAARMgCAj3L8m6nuI8AAiJABAAw5/s1W5xFgAETIAABKjn8e6joCDIAIGQCA45+XOo4AAyBCBgDkzfHP
        U91GgAEQIQOAunhp9q3g/5y1c/zz9vT+VvDrIkUGQIQMAOpgx8TU0qF64eT54P+e1XP82fTEZPBrI0UG
        QIQMAFJXHv/hNywjYDQcf0oGQOYZAKTso8d/yAhYH8efIQMg8wwAUhU6/kNGwNo4/nyUAZB5BgAputrx
        HzICVsfxZzkDIPMMAFKzkuM/ZASsjONPiAGQeQYAKVnN8R8yAq7O8edKDIDMMwBIxVqO/9CNd+8uDr30
        evD/bs4cf67GAMg8A4AUrOf4DxkBH+f4cy0GQOYZAIzbKI7/kBHwIceflTAAMs8AYJxGefyHch8Bjj8r
        ZQBkngHAuMQ4/kO5jgDHn9UwADLPAGAcYh7/odxGgOPPahkAmWcAULUqjv9QLiPA8WctDIDMMwCoUpXH
        f6jpI8DxZ60MgMwzAKjKOI7/UFNHgOPPehgAmWcAUIVxHv+hpo0Ax5/1MgAyzwAgthSO/1BTRoDjzygY
        AJlnABBTSsd/qO4jwPFnVAyAzDMAiCXF4z9U1xHg+DNKBkDmGQDEkPLxH6rbCHD8GTUDIPMMAEatDsd/
        qC4jwPEnBgMg8wwARqlOx38o9RHg+BOLAZB5BgCjUsfjP5TqCHD8ickAyDwDgFGo8/EfSm0EOP7EZgBk
        ngHAejXh+A+lMgIcf6pgAGSeAcB6NOn4D417BDj+VMUAyDwDgLVq4vEfGtcIcPypkgGQeQYAa9Hk4z9U
        9Qhw/KmaAZB5BgCrlcPxH6pqBDj+jIMBkHkGAKuR0/Efij0CHH/GxQDIPAOAlcrx+A/FGgGOP+NkAGSe
        AcBK5Hz8h0Y9Ahx/xs0AyDwDgGtx/H9mVCPA8ScFBkDmGQBcjeN/ufWOAMefVBgAmWcAcCWO/5WtdQQ4
        /qTEAMg8A4AQx//aVjsCHH9SYwBkngHAco7/yq10BDj+pMgAyDwDgI9y/FfvWiPA8SdVBkDmGQAMOf5r
        d6UR4PiTMgMg8wwASo7/+i0fAY4/qTMAMs8AwPEfneEIcPypAwMg8wyAvDn+o1cefsefOjAAMs8AyJfj
        D3kzADLPAMiT4w8YAJlnAOTH8QdKBkDmGQB5cfyBIQMg8wyAfDj+wEcZAJlnAOTB8QeWMwAyzwBoPscf
        CDEAMs8AaDbHH7gSAyDzDIDmcvyBqzEAMs8AaCbHH7gWAyDzDIDmcfyBlTAAMs8AaBbHH1gpAyDzDIDm
        cPyB1TAAMs8AaAbHH1gtAyDzDID6c/yBtTAAMs8AqDfHH1grAyDzDID6cvyB9TAAMs8AqCfHH1gvAyDz
        DID6cfyBUTAAMs8AqBfHHxgVAyDzDID6cPyBUTIAMs8AqAfHHxg1AyDzDID0Of5ADAZA5hkAaXP8gVgM
        gMwzANLl+AMxGQCZZwCkyfEHYjMAMs8ASI/jD1TBAMg8AyAtjj9QFQMg8wyAdDj+QJUMgMwzANLg+ANV
        MwAyzwAYP8cfGAcDIPMMgPFy/IFxMQAyzwAYH8cfGCcDIPMMgPFw/IFxMwAyzwConuMPpMAAyDwDoFqO
        P5AKAyDzDIDqOP5ASgyAzDMAquH4A6kxADLPAIjP8QdSZABkngEQl+MPpMoAyDwDIB7HH0iZAZB5BkAc
        jj+QOgMg8wyA0XP8gTowADLPABgtxx+oCwMg8wyA0XH8gToxADLPABgNxx+oGwMg8wyA9XP8gToyADLP
        AFgfxx+oKwMg8wyAtXP8gTozADLPAFgbxx+oOwMg8wyA1XP8gSYwADLPAFgdxx9oCgMg8wyAlXP8gSYx
        ADLPAFgZxx9oGgMg8wyAa3P8gSYyADLPALg6xx9oKgMg8wyAK3P8gSYzADLPAAhz/IGmMwAyzwC4nOMP
        5MAAyDwD4OMcfyAXBkDmGQA/4/gDOTEAMs8A+JDjD+TGAMg8A8DxB/JkAGRe7gPA8QdyZQBkXs4DwPEH
        cmYAZF6uA8DxB3JnAGRejgPA8QcwALIvtwHg+AN8yADIvJwGgOMP8DMGQOblMgAcf4CPMwAyL4cB4PgD
        XM4AyLymDwDHHyDMAMi8Jg8Axx/gygyAzGvqAHD8Aa7OAMi8Jg4Axx/g2gyAzGvaAHD8AVbGAMi8Jg0A
        xx9g5QyAzGvKAHD8AVbHAMi8JgwAxx9g9QyAzKv7AHD8AdbGAMi8Og8Axx9g7QyAzKvrAHD8AdbHAMi8
        Og4Axx9g/QyAzKvbAHD8AUbDAMi81nz3ttCLnaLnXz6/9AULwPo9vb8V/F6bpu5tg7OlUdXqdK8Pv9gA
        kIaZ+UvXDc6WRtVsZ/FLoRcbAFIx/Xr3i4OzpVE1e/7SF0IvNgAk4/WL/3RwtjSqZtsXPx98sQEgFXPv
        fG5wtjSq5ua+99n+i/vBZS82AKThg+np7mcGZ0ujrP/i/njZiw0AqXhvcK406lqd7h8EXnAAGL9O7/uD
        c6VR12r3TgdfdAAYs5lO7+XBudKom20vPhR60QFg3Gbmu5sG50qj7kz7d3899KIDwLiVv602OFeKUf9F
        9oOAACRm8f3BmVKsWu3efPjFB4DxmJ3vnR2cKcWq/0JvWP7CA8A4zXR6dwzOlGI1eEOgny5/8QFgTP7W
        GwBV1Mx8dy7wAACgep3FVwfnSbFrtRf/dfAhAEDFznR6vzU4T4rdxMTEJ1vt3p+GHgQAVGV2vvefy5s0
        OE+qovINF0IPAwCq0h8A9w/OkqpqevqPfr7/4v/58ocBANVYfH9hYeFTg7OkKmt1Fh8NPxQAiG3xkcE5
        UtW99NKlX2jNd/84/GAAII7y3/7LGzQ4RxpHfiMAgKrNzF+6bnCGNM76D+PC8ocDAHEsvjE4Pxp3rflL
        v9x/KH99+UMCgJH6ydS57j8cnB+l0Ozr3Rv7D+aDZQ8KAEblg5n57i2Ds6OU6j+cl5Y9LAAYlWODc6PU
        WvqtgE7vB4GHBgDr8Yd+5z/xpua+93fLX88IPDwAWIs/O3t28e8PzoxSbvqNS/+4/8D+67IHCACr0+79
        95lzi782OC+qQ9Pt7m/OzPf+JvhAAeAayhtS3pLBWVGdmp5f/Cf9h/iXyx8qAFzDXzn+Na81987n/EwA
        AKvwo1c63V8dnBHVuen27/2S3w4AYAX+sPX6xb83OB9qQuXHB8+2e8/3H643CwLgcu3etF/1a3CtzuJX
        +g/6J5c9eACyVP6w32yn97XBmVCTG3x2gA8QAsje4hve2z/DznR6v9X/AviPl39BANBwP5xtL/67wTlQ
        jp09++6nZ+Z72/or8P3AFwgAzfJe//v9I+Vbxw/OgHKv/CHBVrt7d2u++8eBLxgA6u1HM+3uYw6/rtjE
        xMQnp1/vfrH8adD+F8zfLvsCAqA+/k/fQvlx8RPvvvtzg2/z0rWbnu5+ptVevLPVWTznnwgA6mDpe/Vr
        M53eHeX38MG3c2l9le8MNTPf3dSa755sdXrf73+RvdfnfQUAqld+7/3zD78Xd0/OthcfnG1f/Pzg27UU
        v3Jhvtrp/crs+UtfaLV7/2pm/tJ15e+TAjA65ffWpe+xncV/Xn7P9ad7SZIkSZIkSZIkSZIkSZIkSZIk
        SZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIk
        SZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSVLT+sQn/h9qlU6wIILS
        IAAAAABJRU5ErkJggg==
</value>
  </data>
</root>