using System;
using System.Windows.Forms;
using System.Data.SQLite;

namespace EmployeeManagementSystem
{
    public partial class LoginForm : Form
    {
        public LoginForm()
        {
            InitializeComponent();
            this.labelCopyright.Text = "جميع الحقوق محفوظة  © 2024-" + DateTime.Now.Year.ToString();
            txtPassword.PasswordChar = '*';
            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterScreen;

            // تطبيق الثيم المحفوظ
            var settings = DatabaseHelper.GetSettings();
            if (settings.Rows.Count > 0)
            {
                string theme = settings.Rows[0]["Theme"]?.ToString() ?? "default";
                ThemeManager.ApplyThemeToForm(this);
            }

            // التأكد من وجود قاعدة البيانات عند بدء النموذج
            try
            {
                DatabaseHelper.InitializeDatabase();

                // إضافة أعمدة حفظ بيانات تسجيل الدخول
                AddRememberMeColumns();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddRememberMeColumns()
        {
            try
            {
                using (var connection = new SQLiteConnection(DatabaseHelper.ConnectionString))
                {
                    connection.Open(); string sql = @"
                        PRAGMA table_info(Settings);
                        ";

                    using (var command = new SQLiteCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        bool hasRememberColumns = false;
                        while (reader.Read())
                        {
                            var columnName = reader["name"]?.ToString() ?? string.Empty;
                            if (columnName == "RememberUsername")
                            {
                                hasRememberColumns = true;
                                break;
                            }
                        }

                        if (!hasRememberColumns)
                        {
                            sql = @"
                                ALTER TABLE Settings 
                                ADD COLUMN RememberUsername TEXT;
                                
                                ALTER TABLE Settings 
                                ADD COLUMN RememberPassword TEXT;
                                ";

                            using (var alterCommand = new SQLiteCommand(sql, connection))
                            {
                                alterCommand.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في تهيئة إعدادات حفظ بيانات الدخول: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void LoadSavedCredentials()
        {
            try
            {
                using (var connection = new SQLiteConnection(DatabaseHelper.ConnectionString))
                {
                    connection.Open();
                    string sql = @"SELECT RememberUsername, RememberPassword FROM Settings 
                                 WHERE RememberUsername IS NOT NULL 
                                 AND RememberUsername != '' 
                                 AND RememberPassword IS NOT NULL 
                                 AND RememberPassword != ''";

                    using (var command = new SQLiteCommand(sql, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var savedUsername = reader["RememberUsername"]?.ToString();
                            var savedPassword = reader["RememberPassword"]?.ToString();

                            if (!string.IsNullOrEmpty(savedUsername) && !string.IsNullOrEmpty(savedPassword))
                            {
                                txtUsername.Text = savedUsername;
                                txtPassword.Text = savedPassword;
                                chkRememberMe.Checked = true;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في استرجاع البيانات المحفوظة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void SaveCredentials(string username, string password)
        {
            try
            {
                using (var connection = new SQLiteConnection(DatabaseHelper.ConnectionString))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // أولاً، تأكد من وجود سجل في جدول Settings
                            string checkSql = "SELECT COUNT(*) FROM Settings";
                            using (var checkCommand = new SQLiteCommand(checkSql, connection))
                            {
                                checkCommand.Transaction = transaction;
                                int count = Convert.ToInt32(checkCommand.ExecuteScalar());

                                if (count == 0)
                                {
                                    // إذا لم يكن هناك سجل، قم بإنشاء واحد
                                    string insertSql = @"INSERT INTO Settings (CompanyName, Theme, Language, LastModified, RememberUsername, RememberPassword) 
                                                       VALUES ('اسم المؤسسة', 'Default', 'ar', datetime('now'), @Username, @Password)";
                                    using (var insertCommand = new SQLiteCommand(insertSql, connection))
                                    {
                                        insertCommand.Transaction = transaction;
                                        insertCommand.Parameters.AddWithValue("@Username", username ?? string.Empty);
                                        insertCommand.Parameters.AddWithValue("@Password", password ?? string.Empty);
                                        insertCommand.ExecuteNonQuery();
                                    }
                                }
                                else
                                {
                                    // إذا كان هناك سجل، قم بتحديثه
                                    string updateSql = @"UPDATE Settings SET 
                                                    RememberUsername = @Username,
                                                    RememberPassword = @Password";
                                    using (var updateCommand = new SQLiteCommand(updateSql, connection))
                                    {
                                        updateCommand.Transaction = transaction;
                                        updateCommand.Parameters.AddWithValue("@Username", username ?? string.Empty);
                                        updateCommand.Parameters.AddWithValue("@Password", password ?? string.Empty);
                                        updateCommand.ExecuteNonQuery();
                                    }
                                }
                            }

                            transaction.Commit();
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في حفظ البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ClearSavedCredentials()
        {
            SaveCredentials(string.Empty, string.Empty);
        }

        private async void btnLogin_Click(object sender, EventArgs e)
        {
            string username = txtUsername.Text;
            string password = txtPassword.Text;
            picloading.Visible = true;
            await Task.Delay(100); // يسمح بتحديث الواجهة قبل البدء
            try
            {
                using (var connection = new SQLiteConnection(DatabaseHelper.ConnectionString))
                {
                    connection.Open();
                    string sql = "SELECT UserType FROM Users WHERE Username = @Username AND Password = @Password";
                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@Username", username);
                        command.Parameters.AddWithValue("@Password", password);
                        var userType = command.ExecuteScalar()?.ToString();

                        if (userType != null)
                        {
                            // حفظ أو حذف بيانات تسجيل الدخول حسب حالة مربع "تذكرني"
                            if (chkRememberMe.Checked)
                            {
                                SaveCredentials(username, password);
                            }
                            else
                            {
                                ClearSavedCredentials();
                            }

                            var mainForm = new MainForm(userType);
                            mainForm.CurrentUsername = username;  // تعيين اسم المستخدم قبل إظهار النموذج
                            Hide();
                            mainForm.Show();
                            DialogResult = DialogResult.OK;
                        }
                        else
                        {
                            MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة", "خطأ",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                picloading.Visible = false;
            }
        }


        private void btnCancel_Click(object sender, EventArgs e)
        {
            Application.Exit();
            //DialogResult = DialogResult.Cancel;
            //Close();
        }

        private void LoginForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (DialogResult != DialogResult.OK)
            {
                DialogResult = DialogResult.Cancel;
            }
            Application.Exit();
        }
        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);

            // تحقق مما إذا كان هناك بيانات محفوظة
            LoadSavedCredentials();

            // إذا لم يتم تعبئة البيانات من LoadSavedCredentials، قم بمسح الحقول
            if (string.IsNullOrEmpty(txtUsername.Text))
            {
                txtUsername.Clear();
                txtPassword.Clear();
            }

            txtUsername.Focus();
        }

        private void LoginForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            Application.Exit();
        }
    }
}