using System;
using System.Windows.Forms;
using System.Data;

namespace EmployeeManagementSystem
{
    public partial class UserForm : Form
    {
        private int? selectedUserId = null;

        public UserForm()
        {
            InitializeComponent();
            LoadUsers();
            cmbUserType.Items.AddRange(new string[] { "مدير", "مستخدم" });

            // تطبيق الثيم المحفوظ
            var settings = DatabaseHelper.GetSettings();
            if (settings.Rows.Count > 0)
            {
                string theme = settings.Rows[0]["Theme"]?.ToString() ?? "default";
                ThemeManager.ApplyThemeToForm(this);
            }
        }

        private void LoadUsers()
        {
            var table = DatabaseHelper.GetAllUsers();

            foreach (DataColumn column in table.Columns)
            {
                switch (column.ColumnName)
                {
                    case "UserId":
                        column.ColumnName = "رقم المستخدم";
                        break;
                    case "Username":
                        column.ColumnName = "اسم المستخدم";
                        break;
                    case "FullName":
                        column.ColumnName = "الاسم الكامل";
                        break;
                    case "UserType":
                        column.ColumnName = "نوع المستخدم";
                        break;
                }
            }

            dataGridView1.DataSource = table;
            dataGridView1.Refresh();

            // إخفاء عمود كلمة المرور لأسباب أمنية
            if (dataGridView1.Columns.Contains("Password"))
                dataGridView1.Columns["Password"].Visible = false;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtUsername.Text) ||
                    string.IsNullOrWhiteSpace(txtFullName.Text) ||
                    string.IsNullOrWhiteSpace(txtPassword.Text) ||
                    string.IsNullOrWhiteSpace(cmbUserType.Text))
                {
                    MessageBox.Show("الرجاء إدخال جميع البيانات المطلوبة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var user = new User
                {
                    Username = txtUsername.Text,
                    FullName = txtFullName.Text,
                    Password = txtPassword.Text,
                    UserType = cmbUserType.Text
                };

                DatabaseHelper.AddUser(user);
                MessageBox.Show("تمت إضافة المستخدم بنجاح", "نجاح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                //this.DialogResult = DialogResult.OK;
                //this.Close();

                LoadUsers();
                ToastHelper.ShowAddToast();
                ClearFields();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            if (!selectedUserId.HasValue)
            {
                MessageBox.Show("الرجاء اختيار مستخدم للتعديل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var user = new User
                {
                    UserId = selectedUserId.Value,
                    Username = txtUsername.Text,
                    FullName = txtFullName.Text,
                    Password = string.IsNullOrWhiteSpace(txtPassword.Text) ?
                              null : txtPassword.Text, // تحديث كلمة المرور فقط إذا تم إدخالها
                    UserType = cmbUserType.Text
                };

                DatabaseHelper.UpdateUser(user);
                MessageBox.Show("تم تعديل المستخدم بنجاح", "نجاح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoadUsers();
                ToastHelper.ShowEditToast();
                ClearFields();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (!selectedUserId.HasValue)
            {
                MessageBox.Show("الرجاء اختيار مستخدم للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (MessageBox.Show("هل أنت متأكد من حذف هذا المستخدم؟", "تأكيد الحذف",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                try
                {
                    DatabaseHelper.DeleteUser(selectedUserId.Value);
                    MessageBox.Show("تم حذف المستخدم بنجاح", "نجاح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    LoadUsers();
                    ToastHelper.ShowDeleteToast();
                    ClearFields();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearFields();
        }

        private void dataGridView1_SelectionChanged(object sender, EventArgs e)
        {
            if (dataGridView1.CurrentRow != null)
            {
                try
                {
                    selectedUserId = Convert.ToInt32(dataGridView1.CurrentRow.Cells["رقم المستخدم"].Value);
                    if (selectedUserId.HasValue)
                    {
                        var user = DatabaseHelper.GetUserById(selectedUserId.Value);
                        txtUsername.Text = user.Username;
                        txtFullName.Text = user.FullName;
                        txtPassword.Text = ""; // لا نعرض كلمة المرور لأسباب أمنية
                        cmbUserType.Text = user.UserType;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحديد الصف: {ex.Message}");
                }
            }
        }

        private void ClearFields()
        {
            selectedUserId = null;
            txtUsername.Clear();
            txtFullName.Clear();
            txtPassword.Clear();
            cmbUserType.SelectedIndex = -1;
        }

       
    }
}