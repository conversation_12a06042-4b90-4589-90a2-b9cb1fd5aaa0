namespace EmployeeManagementSystem
{
    partial class CourseForm
    {
        private System.ComponentModel.IContainer components = null;
        private TextBox txtCategory;
        private TextBox txtGraduationGrade;
        private Label lblCategory;
        private Label lblGraduationGrade;
        private Button btnPrintReport;
        private CheckBox chkSelectAll;
        private ComboBox cmbMonthFilter;
        private Label lblMonthFilter;

        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            groupBox1 = new GroupBox();
            chkSelectAll = new CheckBox();
            dtEndDate = new DateTimePicker();
            cmbMonthFilter = new ComboBox();
            btnSearch = new Button();
            dtStartDate = new DateTimePicker();
            txtSearch = new TextBox();
            label5 = new Label();
            label4 = new Label();
            label3 = new Label();
            txtCourseNumber = new TextBox();
            label2 = new Label();
            txtCourseType = new TextBox();
            label1 = new Label();
            cmbEmployeeName = new ComboBox();
            btnExportExcel = new Button();
            btnClear = new Button();
            btnDelete = new Button();
            btnUpdate = new Button();
            btnAdd = new Button();
            txtCategory = new TextBox();
            lblCategory = new Label();
            txtGraduationGrade = new TextBox();
            lblGraduationGrade = new Label();
            btnPrintAll = new Button();
            btnPrintReport = new Button();
            lblMonthFilter = new Label();
            dataGridView1 = new DataGridView();
            toolTip1 = new ToolTip(components);
            lbl_NoDocuments = new Label();
            groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).BeginInit();
            SuspendLayout();
            // 
            // groupBox1
            // 
            groupBox1.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBox1.Controls.Add(chkSelectAll);
            groupBox1.Controls.Add(dtEndDate);
            groupBox1.Controls.Add(cmbMonthFilter);
            groupBox1.Controls.Add(btnSearch);
            groupBox1.Controls.Add(dtStartDate);
            groupBox1.Controls.Add(txtSearch);
            groupBox1.Controls.Add(label5);
            groupBox1.Controls.Add(label4);
            groupBox1.Controls.Add(label3);
            groupBox1.Controls.Add(txtCourseNumber);
            groupBox1.Controls.Add(label2);
            groupBox1.Controls.Add(txtCourseType);
            groupBox1.Controls.Add(label1);
            groupBox1.Controls.Add(cmbEmployeeName);
            groupBox1.Controls.Add(btnExportExcel);
            groupBox1.Controls.Add(btnClear);
            groupBox1.Controls.Add(btnDelete);
            groupBox1.Controls.Add(btnUpdate);
            groupBox1.Controls.Add(btnAdd);
            groupBox1.Controls.Add(txtCategory);
            groupBox1.Controls.Add(lblCategory);
            groupBox1.Controls.Add(txtGraduationGrade);
            groupBox1.Controls.Add(lblGraduationGrade);
            groupBox1.Controls.Add(btnPrintAll);
            groupBox1.Controls.Add(btnPrintReport);
            groupBox1.Controls.Add(lblMonthFilter);
            groupBox1.Font = new Font("Cairo", 12F, FontStyle.Bold);
            groupBox1.Location = new Point(12, 12);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(1002, 318);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "بيانات الدورة";
            // 
            // chkSelectAll
            // 
            chkSelectAll.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            chkSelectAll.Font = new Font("Cairo", 12F, FontStyle.Bold);
            chkSelectAll.Image = Properties.Resources.checked_checkbox_32px;
            chkSelectAll.ImageAlign = ContentAlignment.MiddleRight;
            chkSelectAll.Location = new Point(14, 266);
            chkSelectAll.Name = "chkSelectAll";
            chkSelectAll.RightToLeft = RightToLeft.No;
            chkSelectAll.Size = new Size(97, 46);
            chkSelectAll.TabIndex = 14;
            chkSelectAll.Text = "تحديد";
            toolTip1.SetToolTip(chkSelectAll, "تحديد الكل");
            chkSelectAll.UseVisualStyleBackColor = true;
            chkSelectAll.CheckedChanged += chkSelectAll_CheckedChanged;
            // 
            // dtEndDate
            // 
            dtEndDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            dtEndDate.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dtEndDate.Format = DateTimePickerFormat.Short;
            dtEndDate.Location = new Point(14, 75);
            dtEndDate.Name = "dtEndDate";
            dtEndDate.Size = new Size(338, 37);
            dtEndDate.TabIndex = 5;
            // 
            // cmbMonthFilter
            // 
            cmbMonthFilter.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbMonthFilter.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbMonthFilter.Font = new Font("Cairo", 12F, FontStyle.Bold);
            cmbMonthFilter.FormattingEnabled = true;
            cmbMonthFilter.Location = new Point(14, 157);
            cmbMonthFilter.Name = "cmbMonthFilter";
            cmbMonthFilter.RightToLeft = RightToLeft.No;
            cmbMonthFilter.Size = new Size(338, 38);
            cmbMonthFilter.TabIndex = 15;
            toolTip1.SetToolTip(cmbMonthFilter, "اختيار الشهر ");
            cmbMonthFilter.SelectedIndexChanged += cmbMonthFilter_SelectedIndexChanged;
            // 
            // btnSearch
            // 
            btnSearch.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnSearch.Font = new Font("Cairo", 14.25F, FontStyle.Bold, GraphicsUnit.Point, 0);
            btnSearch.Location = new Point(822, 264);
            btnSearch.Name = "btnSearch";
            btnSearch.Size = new Size(109, 48);
            btnSearch.TabIndex = 21;
            btnSearch.Text = "بحث";
            btnSearch.UseVisualStyleBackColor = true;
            btnSearch.Click += btnSearch_Click;
            // 
            // dtStartDate
            // 
            dtStartDate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            dtStartDate.Font = new Font("Cairo", 12F, FontStyle.Bold);
            dtStartDate.Format = DateTimePickerFormat.Short;
            dtStartDate.Location = new Point(14, 32);
            dtStartDate.Name = "dtStartDate";
            dtStartDate.Size = new Size(338, 37);
            dtStartDate.TabIndex = 4;
            // 
            // txtSearch
            // 
            txtSearch.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtSearch.Font = new Font("Cairo", 14.25F);
            txtSearch.Location = new Point(255, 267);
            txtSearch.Multiline = true;
            txtSearch.Name = "txtSearch";
            txtSearch.PlaceholderText = "ابحث عن الدورة التدريبية...";
            txtSearch.RightToLeft = RightToLeft.Yes;
            txtSearch.Size = new Size(569, 42);
            txtSearch.TabIndex = 20;
            toolTip1.SetToolTip(txtSearch, "البحث عن الدورة التدريبية للموظف");
            txtSearch.KeyDown += txtSearch_KeyDown;
            // 
            // label5
            // 
            label5.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label5.AutoSize = true;
            label5.Location = new Point(357, 80);
            label5.Name = "label5";
            label5.Size = new Size(105, 30);
            label5.TabIndex = 2;
            label5.Text = "تاريخ النهاية:";
            // 
            // label4
            // 
            label4.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label4.AutoSize = true;
            label4.Location = new Point(360, 40);
            label4.Name = "label4";
            label4.Size = new Size(102, 30);
            label4.TabIndex = 2;
            label4.Text = "تاريخ البداية:";
            // 
            // label3
            // 
            label3.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label3.AutoSize = true;
            label3.Location = new Point(890, 119);
            label3.Name = "label3";
            label3.Size = new Size(91, 30);
            label3.TabIndex = 3;
            label3.Text = "رقم الدورة:";
            // 
            // txtCourseNumber
            // 
            txtCourseNumber.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtCourseNumber.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtCourseNumber.Location = new Point(524, 116);
            txtCourseNumber.Name = "txtCourseNumber";
            txtCourseNumber.Size = new Size(338, 37);
            txtCourseNumber.TabIndex = 2;
            // 
            // label2
            // 
            label2.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label2.AutoSize = true;
            label2.Location = new Point(891, 160);
            label2.Name = "label2";
            label2.Size = new Size(90, 30);
            label2.TabIndex = 5;
            label2.Text = "نوع الدورة:";
            // 
            // txtCourseType
            // 
            txtCourseType.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtCourseType.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtCourseType.Location = new Point(524, 157);
            txtCourseType.Name = "txtCourseType";
            txtCourseType.Size = new Size(338, 37);
            txtCourseType.TabIndex = 3;
            // 
            // label1
            // 
            label1.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            label1.AutoSize = true;
            label1.Location = new Point(868, 78);
            label1.Name = "label1";
            label1.Size = new Size(113, 30);
            label1.TabIndex = 7;
            label1.Text = "اسم الموظف:";
            // 
            // cmbEmployeeName
            // 
            cmbEmployeeName.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            cmbEmployeeName.Font = new Font("Cairo", 12F, FontStyle.Bold);
            cmbEmployeeName.Location = new Point(524, 75);
            cmbEmployeeName.Name = "cmbEmployeeName";
            cmbEmployeeName.RightToLeft = RightToLeft.No;
            cmbEmployeeName.Size = new Size(338, 38);
            cmbEmployeeName.TabIndex = 1;
            // 
            // btnExportExcel
            // 
            btnExportExcel.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnExportExcel.BackColor = Color.Transparent;
            btnExportExcel.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnExportExcel.Image = Properties.Resources.microsoft_excel_2019_32px;
            btnExportExcel.ImageAlign = ContentAlignment.MiddleRight;
            btnExportExcel.Location = new Point(215, 211);
            btnExportExcel.Name = "btnExportExcel";
            btnExportExcel.Size = new Size(108, 46);
            btnExportExcel.TabIndex = 11;
            btnExportExcel.Text = "تصدير";
            btnExportExcel.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnExportExcel, "تصدير");
            btnExportExcel.UseVisualStyleBackColor = false;
            btnExportExcel.Click += btnExportExcel_Click;
            // 
            // btnClear
            // 
            btnClear.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnClear.BackColor = Color.Transparent;
            btnClear.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnClear.Image = Properties.Resources.clear_formatting_32px;
            btnClear.ImageAlign = ContentAlignment.MiddleRight;
            btnClear.Location = new Point(524, 211);
            btnClear.Name = "btnClear";
            btnClear.Size = new Size(90, 46);
            btnClear.TabIndex = 9;
            btnClear.Text = "إفراغ";
            btnClear.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnClear, "إفراغ الحقول");
            btnClear.UseVisualStyleBackColor = false;
            btnClear.Click += btnClear_Click;
            // 
            // btnDelete
            // 
            btnDelete.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnDelete.BackColor = Color.Transparent;
            btnDelete.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnDelete.Image = Properties.Resources.remove_32px;
            btnDelete.ImageAlign = ContentAlignment.MiddleRight;
            btnDelete.Location = new Point(623, 211);
            btnDelete.Name = "btnDelete";
            btnDelete.Size = new Size(90, 46);
            btnDelete.TabIndex = 12;
            btnDelete.Text = "حذف";
            btnDelete.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnDelete, "حذف");
            btnDelete.UseVisualStyleBackColor = false;
            btnDelete.Click += btnDelete_Click;
            // 
            // btnUpdate
            // 
            btnUpdate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnUpdate.BackColor = Color.Transparent;
            btnUpdate.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnUpdate.Image = Properties.Resources.edit_profile_32px;
            btnUpdate.ImageAlign = ContentAlignment.MiddleRight;
            btnUpdate.Location = new Point(722, 211);
            btnUpdate.Name = "btnUpdate";
            btnUpdate.Size = new Size(102, 46);
            btnUpdate.TabIndex = 8;
            btnUpdate.Text = "تعديل";
            btnUpdate.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnUpdate, "تعديل");
            btnUpdate.UseVisualStyleBackColor = false;
            btnUpdate.Click += btnUpdate_Click;
            // 
            // btnAdd
            // 
            btnAdd.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnAdd.BackColor = Color.Transparent;
            btnAdd.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnAdd.Image = Properties.Resources.ok_32px;
            btnAdd.ImageAlign = ContentAlignment.MiddleRight;
            btnAdd.Location = new Point(830, 211);
            btnAdd.Name = "btnAdd";
            btnAdd.Size = new Size(101, 46);
            btnAdd.TabIndex = 7;
            btnAdd.Text = "إضافة";
            btnAdd.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnAdd, "إضافة");
            btnAdd.UseVisualStyleBackColor = false;
            btnAdd.Click += btnAdd_Click;
            // 
            // txtCategory
            // 
            txtCategory.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtCategory.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtCategory.Location = new Point(524, 35);
            txtCategory.Name = "txtCategory";
            txtCategory.Size = new Size(338, 37);
            txtCategory.TabIndex = 0;
            // 
            // lblCategory
            // 
            lblCategory.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblCategory.AutoSize = true;
            lblCategory.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblCategory.Location = new Point(916, 38);
            lblCategory.Name = "lblCategory";
            lblCategory.Size = new Size(65, 30);
            lblCategory.TabIndex = 16;
            lblCategory.Text = "الصنف:";
            // 
            // txtGraduationGrade
            // 
            txtGraduationGrade.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            txtGraduationGrade.Font = new Font("Cairo", 12F, FontStyle.Bold);
            txtGraduationGrade.Location = new Point(14, 116);
            txtGraduationGrade.Name = "txtGraduationGrade";
            txtGraduationGrade.Size = new Size(338, 37);
            txtGraduationGrade.TabIndex = 6;
            // 
            // lblGraduationGrade
            // 
            lblGraduationGrade.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblGraduationGrade.AutoSize = true;
            lblGraduationGrade.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblGraduationGrade.Location = new Point(365, 119);
            lblGraduationGrade.Name = "lblGraduationGrade";
            lblGraduationGrade.Size = new Size(97, 30);
            lblGraduationGrade.TabIndex = 18;
            lblGraduationGrade.Text = "درجة التخرج:";
            // 
            // btnPrintAll
            // 
            btnPrintAll.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnPrintAll.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnPrintAll.Image = Properties.Resources.print_32px;
            btnPrintAll.ImageAlign = ContentAlignment.MiddleRight;
            btnPrintAll.Location = new Point(14, 211);
            btnPrintAll.Name = "btnPrintAll";
            btnPrintAll.Size = new Size(91, 46);
            btnPrintAll.TabIndex = 12;
            btnPrintAll.Text = " الكل";
            btnPrintAll.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnPrintAll, "طباعة كل الموظفين");
            btnPrintAll.UseVisualStyleBackColor = true;
            btnPrintAll.Click += btnPrintAll_Click;
            // 
            // btnPrintReport
            // 
            btnPrintReport.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnPrintReport.Font = new Font("Cairo", 12F, FontStyle.Bold);
            btnPrintReport.Image = Properties.Resources.print_32px;
            btnPrintReport.ImageAlign = ContentAlignment.MiddleRight;
            btnPrintReport.Location = new Point(111, 211);
            btnPrintReport.Name = "btnPrintReport";
            btnPrintReport.Size = new Size(98, 46);
            btnPrintReport.TabIndex = 12;
            btnPrintReport.Text = "طباعة ";
            btnPrintReport.TextAlign = ContentAlignment.MiddleLeft;
            toolTip1.SetToolTip(btnPrintReport, "طباعة موظف");
            btnPrintReport.UseVisualStyleBackColor = true;
            btnPrintReport.Click += btnPrintReport_Click;
            // 
            // lblMonthFilter
            // 
            lblMonthFilter.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            lblMonthFilter.AutoSize = true;
            lblMonthFilter.Font = new Font("Cairo", 12F, FontStyle.Bold);
            lblMonthFilter.Location = new Point(358, 160);
            lblMonthFilter.Name = "lblMonthFilter";
            lblMonthFilter.Size = new Size(155, 30);
            lblMonthFilter.TabIndex = 16;
            lblMonthFilter.Text = "تصفية حسب الشهر:";
            // 
            // dataGridView1
            // 
            dataGridView1.AllowUserToAddRows = false;
            dataGridView1.AllowUserToDeleteRows = false;
            dataGridView1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dataGridView1.Location = new Point(12, 336);
            dataGridView1.Name = "dataGridView1";
            dataGridView1.ReadOnly = true;
            dataGridView1.Size = new Size(1002, 294);
            dataGridView1.TabIndex = 13;
            dataGridView1.CellClick += dataGridView1_CellClick;
            // 
            // lbl_NoDocuments
            // 
            lbl_NoDocuments.Anchor = AnchorStyles.None;
            lbl_NoDocuments.AutoSize = true;
            lbl_NoDocuments.Font = new Font("Cairo", 12F, FontStyle.Regular, GraphicsUnit.Point, 0);
            lbl_NoDocuments.Location = new Point(486, 465);
            lbl_NoDocuments.Name = "lbl_NoDocuments";
            lbl_NoDocuments.Size = new Size(54, 30);
            lbl_NoDocuments.TabIndex = 35;
            lbl_NoDocuments.Text = "label6";
            lbl_NoDocuments.Visible = false;
            // 
            // CourseForm
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1026, 642);
            Controls.Add(lbl_NoDocuments);
            Controls.Add(dataGridView1);
            Controls.Add(groupBox1);
            Name = "CourseForm";
            RightToLeft = RightToLeft.Yes;
            RightToLeftLayout = true;
            StartPosition = FormStartPosition.CenterScreen;
            Text = "إدارة الدورات التدريبية";
            Load += CourseForm_Load;
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        private GroupBox groupBox1;
        private Label label1;
        private Label label2;
        private Label label3;
        private Label label4;
        private TextBox txtCourseType;
        private TextBox txtCourseNumber;
        private ComboBox cmbEmployeeName;
        private DateTimePicker dtStartDate;
        private DateTimePicker dtEndDate;
        private Button btnAdd;
        private Button btnUpdate;
        private Button btnDelete;
        private Button btnClear;
        private Button btnExportExcel;
        private DataGridView dataGridView1;
        private Label label5;
        private Button btnPrintAll;
        private ToolTip toolTip1;
        private Label lbl_NoDocuments;
        private Button btnSearch;
        private TextBox txtSearch;
    }
}