using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data.SQLite;
using EmployeeManagementSystem.LoadingGui;
using System.Drawing.Drawing2D;

namespace EmployeeManagementSystem
{
    public partial class MainForm : Form
    {
        private Form? activeForm;
        private readonly System.Windows.Forms.Timer notificationTimer = new();
        private readonly Button notificationButton = new();
        private readonly Label notificationCounter = new();

        public string UserType { get; set; } = string.Empty;
        public string CurrentUsername { get; set; } = string.Empty;

        public MainForm(string userType)
        {
            InitializeComponent();
            UserType = userType;
            if (userType != "مدير")
            {
                btnUsers.Visible = false;
            }
            // Configure main form
            this.WindowState = FormWindowState.Maximized;

            // Configure notification button
            ConfigureNotificationButton();

            // Configure notification timer
            ConfigureNotificationTimer();

            // Apply saved theme
            ApplySavedTheme();
        }

        private void ConfigureNotificationButton()
        {
            notificationButton.Size = new Size(48, 48);
            notificationButton.FlatStyle = FlatStyle.Flat;
            notificationButton.FlatAppearance.BorderSize = 0;
            notificationButton.Text = "🔔";
            notificationButton.TextAlign = ContentAlignment.MiddleCenter;
            notificationButton.Font = new Font("Segoe UI Emoji", 15f);
            notificationButton.BackColor = Color.FromArgb(45, 66, 91);
            notificationButton.ForeColor = Color.White;
            notificationButton.Cursor = Cursors.Hand;
            notificationButton.Location = new Point(5, 5);
            notificationButton.Click += (s, e) => OpenForm(new NotificationsForm());

            // Configure notification counter
            notificationCounter.Size = new Size(20, 20);
            notificationCounter.BackColor = Color.Red;
            notificationCounter.ForeColor = Color.White;
            notificationCounter.Font = new Font("Cairo", 8f, FontStyle.Bold);
            notificationCounter.TextAlign = ContentAlignment.MiddleCenter;
            notificationCounter.Visible = false;

            // Position counter over bell icon
            int badgeX = (notificationButton.Width / 2) + 5;
            int badgeY = 2;
            notificationCounter.Location = new Point(badgeX, badgeY);

            // Make counter circular
            using GraphicsPath path = new();
            path.AddEllipse(0, 0, 20, 20);
            notificationCounter.Region = new Region(path);

            notificationButton.Controls.Add(notificationCounter);
            this.Controls.Add(notificationButton);
            notificationButton.BringToFront();
        }

        private void ConfigureNotificationTimer()
        {
            notificationTimer.Interval = 30000; // 30 seconds
            notificationTimer.Tick += async (s, e) =>
            {
                await UpdateNotificationCountAsync();
                await Task.Run(() =>
                {
                    System.Diagnostics.Debug.WriteLine($"فحص الإشعارات في {DateTime.Now}");
                    NotificationService.CheckForUpcomingEvents();
                });
            };
            notificationTimer.Start();

            // Initial notification check
            Task.Run(() =>
            {
                System.Diagnostics.Debug.WriteLine($"فحص أولي للإشعارات في {DateTime.Now}");
                NotificationService.CheckForUpcomingEvents();
            });
        }

        private void ApplySavedTheme()
        {
            var settings = DatabaseHelper.GetSettings();
            if (settings.Rows.Count > 0)
            {
                string theme = settings.Rows[0]["Theme"]?.ToString() ?? "default";
                ThemeManager.ApplyTheme(theme);
                ThemeManager.ApplyThemeToForm(this);
            }
        }

        // ... existing UpdateNotificationCountAsync and UpdateNotificationUI methods ...

        private void OpenForm(Form form)
        {
            try
            {
                if (activeForm != null)
                {
                    activeForm.Close();
                    activeForm.Dispose();
                    mainPanel.Controls.Clear();
                }

                activeForm = form;
                form.TopLevel = false;
                form.FormBorderStyle = FormBorderStyle.None;
                form.Dock = DockStyle.Fill;
                mainPanel.Controls.Add(form);

                // تطبيق الثيم على النموذج الجديد
                var settings = DatabaseHelper.GetSettings();
                if (settings.Rows.Count > 0)
                {
                    string theme = settings.Rows[0]["Theme"]?.ToString() ?? "default";
                    ThemeManager.ApplyThemeToForm(form);
                }

                btnDashboard.Visible = false; // إخفاء الزر القديم
                form.Show();
                Application.DoEvents();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح النموذج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnDashboard_Click(object sender, EventArgs e)
        {
            try
            {
                // Clear existing form if any
                if (activeForm != null)
                {
                    activeForm.Close();
                    activeForm.Dispose();
                    mainPanel.Controls.Clear();
                }

                // Create and configure new dashboard form
                var dashboardForm = new DashboardForm();
                dashboardForm.TopLevel = false;
                dashboardForm.FormBorderStyle = FormBorderStyle.None;
                dashboardForm.Dock = DockStyle.Fill;

                // Apply theme
                var settings = DatabaseHelper.GetSettings();
                if (settings.Rows.Count > 0)
                {
                    string theme = settings.Rows[0]["Theme"]?.ToString() ?? "default";
                    dashboardForm.UpdateTheme(theme);
                }

                // Add and show dashboard
                mainPanel.Controls.Add(dashboardForm);
                activeForm = dashboardForm;
                dashboardForm.Show();

                // Update button appearance
                btnDashboard.BackColor = Color.FromArgb(87, 115, 153);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح لوحة المعلومات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void BtnManageEmployees_Click(object sender, EventArgs e)
        {
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new Form1());
                SetActiveButton(btnManageEmployees);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }

        }

        private async void BtnVacations_Click(object sender, EventArgs e)
        {
            if (!Properties.Settings.Default.IsActivated)
            {
                MessageBox.Show(
                 "أنت حالياً تستخدم النسخة التجريبية من البرنامج\n\n" +
                 "المميزات المتاحة:\n" +
                 "- يمكنك استخدام جميع خصائص النظام\n" +
                 "- مسموح بإضافة حتى 5 ملفات فقط\n\n" +
                 "للاستمرار بدون قيود:\n" +
                 "1. انتقل إلى نافذة التفعيل (قائمة الإعدادات)\n" +
                 "2. أدخل رمز التفعيل الخاص بك\n" +
                 "3. أو اتصل بفريق الدعم لمساعدتك\n\n" +
                 "شكراً لاختيارك منتجاتنا",
                 "النسخة التجريبية - ميزات مقيدة",
                  MessageBoxButtons.OK,
                  MessageBoxIcon.Information
                );
                return; // إيقاف التنفيذ إذا لم يكن البرنامج مفعلاً
            }
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new VacationForm());
                SetActiveButton(btnVacations);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }

        }

        private async void BtnUsers_Click(object sender, EventArgs e)
        {
            if (!Properties.Settings.Default.IsActivated)
            {
                MessageBox.Show(
                 "أنت حالياً تستخدم النسخة التجريبية من البرنامج\n\n" +
                 "المميزات المتاحة:\n" +
                 "- يمكنك استخدام جميع خصائص النظام\n" +
                 "- مسموح بإضافة حتى 5 ملفات فقط\n\n" +
                 "للاستمرار بدون قيود:\n" +
                 "1. انتقل إلى نافذة التفعيل (قائمة الإعدادات)\n" +
                 "2. أدخل رمز التفعيل الخاص بك\n" +
                 "3. أو اتصل بفريق الدعم لمساعدتك\n\n" +
                 "شكراً لاختيارك منتجاتنا",
                 "النسخة التجريبية - ميزات مقيدة",
                  MessageBoxButtons.OK,
                  MessageBoxIcon.Information
                );
                return; // إيقاف التنفيذ إذا لم يكن البرنامج مفعلاً
            }
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new UserForm());
                SetActiveButton(btnUsers);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }

        }

        private void BtnSettings_Click(object sender, EventArgs e)
        {
            if (!Properties.Settings.Default.IsActivated)
            {
                MessageBox.Show(
                 "أنت حالياً تستخدم النسخة التجريبية من البرنامج\n\n" +
                 "المميزات المتاحة:\n" +
                 "- يمكنك استخدام جميع خصائص النظام\n" +
                 "- مسموح بإضافة حتى 5 ملفات فقط\n\n" +
                 "للاستمرار بدون قيود:\n" +
                 "1. انتقل إلى نافذة التفعيل (قائمة الإعدادات)\n" +
                 "2. أدخل رمز التفعيل الخاص بك\n" +
                 "3. أو اتصل بفريق الدعم لمساعدتك\n\n" +
                 "شكراً لاختيارك منتجاتنا",
                 "النسخة التجريبية - ميزات مقيدة",
                  MessageBoxButtons.OK,
                  MessageBoxIcon.Information
                );
                return; // إيقاف التنفيذ إذا لم يكن البرنامج مفعلاً
            }
            using SettingsForm settingsForm = new();
            if (settingsForm.ShowDialog() == DialogResult.OK)
            {
                // تحديث الثيم للنموذج الحالي
                var settings = DatabaseHelper.GetSettings();
                if (settings.Rows.Count > 0)
                {
                    string theme = settings.Rows[0]["Theme"]?.ToString() ?? "default";
                    ThemeManager.ApplyTheme(theme);
                    ThemeManager.ApplyThemeToForm(this);
                    if (activeForm != null)
                    {
                        ThemeManager.ApplyThemeToForm(activeForm);
                    }
                }
            }
        }
        private void BtnLogout_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                // إغلاق النموذج النشط
                if (activeForm != null)
                {
                    activeForm.Close();
                    activeForm.Dispose();
                    activeForm = null;
                }
                GC.Collect();
                GC.WaitForPendingFinalizers();
                // إظهار نموذج تسجيل الدخول
                LoginForm loginForm = new();
                loginForm.Show();

                // إخفاء النموذج الحالي (نموذج الواجهة الرئيسية)
                this.Hide();
            }
        }
       
        private async void BtnHome_Click(object sender, EventArgs e)
        {
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new HomeForm(UserType, CurrentUsername));
                SetActiveButton(btnHome);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }
        }

        private async void ToolStripButtonCourses_Click(object sender, EventArgs e)
        {
            if (!Properties.Settings.Default.IsActivated)
            {
                MessageBox.Show(
                 "أنت حالياً تستخدم النسخة التجريبية من البرنامج\n\n" +
                 "المميزات المتاحة:\n" +
                 "- يمكنك استخدام جميع خصائص النظام\n" +
                 "- مسموح بإضافة حتى 5 ملفات فقط\n\n" +
                 "للاستمرار بدون قيود:\n" +
                 "1. انتقل إلى نافذة التفعيل (قائمة الإعدادات)\n" +
                 "2. أدخل رمز التفعيل الخاص بك\n" +
                 "3. أو اتصل بفريق الدعم لمساعدتك\n\n" +
                 "شكراً لاختيارك منتجاتنا",
                 "النسخة التجريبية - ميزات مقيدة",
                  MessageBoxButtons.OK,
                  MessageBoxIcon.Information
                );
                return; // إيقاف التنفيذ إذا لم يكن البرنامج مفعلاً
            }
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new CourseForm());
                SetActiveButton(toolStripButtonCourses);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }

        }

        private void SetActiveButton(ToolStripButton activeButton)
        {
            foreach (ToolStripItem item in toolStrip.Items)
            {
                if (item is ToolStripButton button)
                {
                    button.BackColor = button == activeButton ?
                        Color.FromArgb(87, 115, 153) :
                        Color.FromArgb(45, 66, 91);
                }
            }
        }

        private async void AboutForm_Click(object sender, EventArgs e)
        {
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new AboutForm());
                SetActiveButton(AboutForm);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }

        }

        private async void Activation_Click_1(object sender, EventArgs e)
        {
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new activationForm());
                SetActiveButton(Activation);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }

        }

        private async void MainForm_Load(object sender, EventArgs e)
        {
            OpenForm(new HomeForm(UserType, CurrentUsername));

            // قم بتعيين الزر النشط
            SetActiveButton(btnHome);
            await UpdateNotificationCountAsync();
        }

        private async Task UpdateNotificationCountAsync()
        {
            try
            {
                int count = await Task.Run(() =>
                {
                    using var connection = new SQLiteConnection(DatabaseHelper.ConnectionString);
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Notifications WHERE IsRead = 0";
                    using var command = new SQLiteCommand(query, connection);
                    return Convert.ToInt32(command.ExecuteScalar());
                });

                if (notificationCounter.InvokeRequired)
                {
                    notificationCounter.Invoke((MethodInvoker)delegate
                    {
                        UpdateNotificationUI(count);
                    });
                }
                else
                {
                    UpdateNotificationUI(count);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث عداد الإشعارات: {ex.Message}");
            }
        }

        private void UpdateNotificationUI(int count)
        {
            try
            {
                if (count > 0)
                {
                    notificationCounter.Text = count > 99 ? "99+" : count.ToString();
                    notificationCounter.Visible = true;
                }
                else
                {
                    notificationCounter.Visible = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث واجهة الإشعارات: {ex.Message}");
            }
        }
        private async void NotificationTimer_Tick(object? sender, EventArgs e)
        {
            await UpdateNotificationCountAsync();
            await Task.Run(() => NotificationService.CheckForUpcomingEvents());
        }


        private async void BtnNotifications_Click(object sender, EventArgs e)
        {
            var notificationsForm = new NotificationsForm();
            notificationsForm.FormClosed += async (s, args) => await UpdateNotificationCountAsync();

           
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة
            try
            {
                // تحميل النموذج المطلوب
                OpenForm(notificationsForm);
                SetActiveButton(btnNotifications);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }
        }

        private async void BtnDashboardStrip_Click(object sender, EventArgs e)
        {
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh();

            await Task.Delay(100);

            try
            {
                var dashboardForm = new DashboardForm();
                OpenForm(dashboardForm);
                SetActiveButton(btnDashboardStrip);

                // تطبيق السمة على النموذج
                var settings = DatabaseHelper.GetSettings();
                if (settings.Rows.Count > 0)
                {
                    string theme = settings.Rows[0]["Theme"]?.ToString() ?? "default";
                    dashboardForm.UpdateTheme(theme);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء فتح لوحة المعلومات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                loadingForm.Close();
            }
        }

        private async void BtnAttendance_Click(object sender, EventArgs e)
        {
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new AttendanceForm());
                SetActiveButton(btnAttendance);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }
        }

        private async void BtnAttendanceReport_Click(object sender, EventArgs e)
        {
            // عرض نافذة التحميل
            var loadingForm = new LoadingForm(this);
            loadingForm.Show();
            loadingForm.Refresh(); // لتحديث العرض قبل المتابعة

            await Task.Delay(100); // مهلة قصيرة لتحديث الواجهة

            try
            {
                // تحميل النموذج المطلوب
                OpenForm(new AttendanceReportForm());
                SetActiveButton(btnAttendanceReport);
            }
            finally
            {
                // إغلاق نافذة التحميل
                loadingForm.Close();
            }
        }

        private void MainForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            Application.Exit();
        }
    }

}