using System;

namespace EmployeeManagementSystem
{
    public class Notification
    {
        public int ID { get; set; }
        public int EmployeeID { get; set; }
        public string? Message { get; set; }
        public string? Type { get; set; }
        public bool IsRead { get; set; }
        public DateTime DateCreated { get; set; }
        public string? EmployeeName { get; set; }
        public DateTime TargetDate { get; set; }
    }
}
