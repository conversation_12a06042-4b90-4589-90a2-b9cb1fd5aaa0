﻿using System;
using System.Linq;
using System.Windows.Forms;
using System.Management;
using System.Diagnostics;
using System.Text;
using System.Security.Cryptography;

namespace EmployeeManagementSystem
{
    public partial class activationForm : Form
    {
        public activationForm()
        {
            InitializeComponent();
        }



        public string GetHardwareID()
        {
            string cpuID = "";
            string motherboardID = "";

            try
            {
                using (ManagementObjectSearcher cpuSearcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in cpuSearcher.Get())
                    {
                        cpuID = obj["ProcessorId"]?.ToString() ?? "UnknownCPU";
                        break;
                    }
                }

                using (ManagementObjectSearcher mbSearcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in mbSearcher.Get())
                    {
                        motherboardID = obj["SerialNumber"]?.ToString() ?? "UnknownBoard";
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء جلب معرف الجهاز: " + ex.Message);
            }

            return cpuID + motherboardID;
        }
        public string GenerateActivationKey(string input)
        {
            string programName = "HRMSDB"; // اسم البرنامج، غيره لكل برنامج
            string finalInput = input + programName; // دمج معرف الجهاز مع اسم البرنامج

            using (SHA256 sha256 = SHA256.Create())
            {
                byte[] bytes = Encoding.UTF8.GetBytes(finalInput);
                byte[] hash = sha256.ComputeHash(bytes);
                StringBuilder sb = new StringBuilder();
                foreach (byte b in hash)
                    sb.Append(b.ToString("X2")); // Hex
                return sb.ToString().Substring(0, 20); // أول 20 خانة
            }
        }

        private async void but_Activation_Click(object sender, EventArgs e)
        {
            string enteredKey = Text_Activation.Text.Trim();

            if (string.IsNullOrEmpty(enteredKey))
            {
                MessageBox.Show("يرجى إدخال مفتاح التفعيل أولًا.", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            lblStatus.Text = "🔄 جاري التحقق من التفعيل...";
            Application.DoEvents(); // تحديث الواجهة مباشرة

            await Task.Delay(2000); // الانتظار 1.5 ثانية (1500 ملي ثانية)

            string hwid = Text_user.Text.Trim();
            string expectedKey = GenerateActivationKey(hwid);

            if (enteredKey == expectedKey)
            {
                Properties.Settings.Default.IsActivated = true;
                Properties.Settings.Default.ActivationKey = enteredKey;
                Properties.Settings.Default.Save();

                lblStatus.Text = "✅ تم التفعيل بنجاح!\nالآن يمكنك استخدام البرنامج بكافة المزايا.";
                MessageBox.Show("تم التفعيل بنجاح، شكرًا لك.", "نجاح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                but_Activation.Enabled = false;
                Text_Activation.ReadOnly = true;
                but_Activation.Text = "مفعل"; 
            }
            else
            {
                lblStatus.Text = "❌ مفتاح التفعيل غير صحيح.";
                MessageBox.Show("مفتاح التفعيل غير صحيح.", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void activationForm_Load(object sender, EventArgs e)
        {
            string hwid = GetHardwareID();
            Text_user.Text = hwid;

            if (Properties.Settings.Default.IsActivated)
            {
                lblStatus.Text = "✅ تم التفعيل بنجاح!\nالآن يمكنك استخدام البرنامج بكافة المزايا.";
                but_Activation.Enabled = false;
                but_Activation.Text = "مفعل";
                Text_Activation.Text = Properties.Settings.Default.ActivationKey;
                Text_Activation.ReadOnly = true;
            }
            else
            {
                lblStatus.Text = "❌ يرجى تفعيل البرنامج للاستفادة من كافة المزايا";
                but_Activation.Enabled = true;
                Text_Activation.ReadOnly = false;
            }
        }
        private void linkLabel1_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            var psi = new ProcessStartInfo
            {
                FileName = "https://wsend.co/9647815040732",
                UseShellExecute = true
            };
            Process.Start(psi);
        }
    }
}
