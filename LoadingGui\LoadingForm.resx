﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBox1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhMAAwAPcAAAD/AEJCQkREREZGRktLS0xMTE5OTlJSUlNTU1RUVFVVVVZWVldXV1hYWFpaWltb
        W1xcXF1dXV9fX2NjY2RkZGVlZWZmZmhoaGlpaWpqamtra2xsbG1tbW9vb3BwcHFxcXJycnR0dHZ2dnd3
        d3h4eHl5eXp6ent7e319fX5+fn9/f4CAgIGBgYKCgoSEhIWFhYaGhoeHh4iIiImJiYuLi4yMjI2NjY6O
        jo+Pj5CQkJKSkpOTk5SUlJWVlZaWlpeXl5mZmZqampubm5ycnJ2dnZ6enqCgoKGhoaKioqOjo6SkpKWl
        paenp6ioqKmpqaqqqqurq6ysrK2tra+vr7CwsLGxsbKysrOzs7S0tLa2tre3t7i4uLm5ubq6uru7u729
        vb6+vr+/v8DAwMHBwcLCwsTExMXFxcbGxsfHx8jIyMnJycvLy8zMzM3Nzc7Ozs/Pz9DQ0NLS0tPT09TU
        1NXV1dbW1tfX19nZ2dra2tvb29zc3N3d3d7e3uDg4OHh4eLi4uPj4+Tk5OXl5efn5+jo6Onp6erq6uvr
        6+zs7O7u7u/v7/Dw8PHx8fLy8vPz8/X19fb29vf39/j4+Pn5+fr6+vz8/P39/f7+/v//////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        /yH/C05FVFNDQVBFMi4wAwEAAAAh+QQFBAAAACwAAAAAMAAwAIcA/wBCQkJERERGRkZLS0tMTExOTk5S
        UlJTU1NUVFRVVVVWVlZXV1dYWFhaWlpbW1tcXFxdXV1fX19jY2NkZGRlZWVmZmZoaGhpaWlqampra2ts
        bGxtbW1vb29wcHBxcXFycnJ0dHR2dnZ3d3d4eHh5eXl6enp7e3t9fX1+fn5/f3+AgICBgYGCgoKEhISF
        hYWGhoaHh4eIiIiJiYmLi4uMjIyNjY2Ojo6Pj4+QkJCSkpKTk5OUlJSVlZWWlpaXl5eZmZmampqbm5uc
        nJydnZ2enp6goKChoaGioqKjo6OkpKSlpaWnp6eoqKipqamqqqqrq6usrKytra2vr6+wsLCxsbGysrKz
        s7O0tLS2tra3t7e4uLi5ubm6urq7u7u9vb2+vr6/v7/AwMDBwcHCwsLExMTFxcXGxsbHx8fIyMjJycnL
        y8vMzMzNzc3Ozs7Pz8/Q0NDS0tLT09PU1NTV1dXW1tbX19fZ2dna2trb29vc3Nzd3d3e3t7g4ODh4eHi
        4uLj4+Pk5OTl5eXn5+fo6Ojp6enq6urr6+vs7Ozu7u7v7+/w8PDx8fHy8vLz8/P19fX29vb39/f4+Pj5
        +fn6+vr8/Pz9/f3+/v7/////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        ////////////////////////////////////////////////////////////////////////////////
        //////////////////////////////////8I/wArYRJIcKDBgggx3aHhYk/ChwcjYrKE6dLEihctUtSI
        0dIQEyd+dBzJsWRGiCgj5kBBwkfKl5hIZsSUpwkUQxsz8ihxQsdMinYsOBhjMqell1aMLJESUaCOFCN6
        JJz0AUGDC00lGh3JJckRJXV+9gDpsySWBAwSqNha8iUipUaePFLJ0uVBRxAaHGAQNqVMo2WQeFVTcmfP
        mT8YKFhw429HmIycwGVU8GnUgnscHGjQQBHMnzLdKBG8pePYwxpZLE4QpShJmAKdLFGqx+DKlgTVbEaw
        AVLWhBgFpWnDiG2fr0ikUKrogyePiZUyLEBrBrQcEA66aCRYposYL3YcNf/1AlePQC9QyQjM0sCqiaaJ
        VgwIMGCAwYtlwHgJ80UMn5GJeJXEGxcRUshGU6C1QB85RYIEAQEEIEAACWxEkCNmjMFFGN6RcUhBcUiR
        hSQQLULDBFkU5MUD9LUoARz3cfQHGft9od8bjYDmWkl5cDBhhAMUEMVy20U0iR5efDeGd3v85mQlUrQ4
        3wA0NAIcW4+wod8XXhCl45cKDCDAhCHkoWNKwnWZxpO/sUBfA2X4teNAguwxCVtfUuSIE1RQMudRbMIW
        KJt5OvanoTpOAkkijijyCKOOMiqeoA9dcQEGE1wwAaaaYoCBG4wkooioiSSCyKimelbojobQ9+OPEkb/
        eAAijCDSiCGLHMIIrroacgmlEREyZYsTzjehApUw4ggjjSyybLPLQoLntCNRkQEFF1RgAbbaUsDBGsCG
        O6hAiFJ76LnQsfnIJMuFG8kSWczl17SVLDKqI+jmdIKYDoRhKESUQMLrruMexOKUHPABkUyOLEKqw5GY
        CxoUxY4ZQA6K/IQQJYk0oqutGYsrkB0gxDrfAU5oNVAjpJrayCQZMUEAB4fo6EgJCzyRURgLiFmsBHJY
        OJAjtuqaCCUI7dEiEQT90aRASkxpx0GVEFEAsQfEiEkiDisinkkciBkAFBUlQV8SE11hsQcmEfKCxQUI
        BGglliSyblZfTMlAJQJVuDDfBQI9EkGLYzxURwcSXHGfxJE0YLGXllww5gQYheHzAn6uCpsSLYZQ0AR/
        FxQ2fUQMWi4iBBRr5kYW0EdBRnq4SgAh5cIkH300RAR6AIAfpEOLMXxGrRzFFrCIUZILQDlHihjQYtD/
        PhlCi1IktHvvBTlhLAeEzumAmBIQmVHrA7xulCUSTAjBny/lzUAcWV1v+AEGKD6voZdMm/zyoMkdvciY
        wMB8PABAcuWLJG04AALWsKo8BQQAIfkEBQQAAAAsAwAAACIAJQAACP8AAQgcSLAgQT5DevwxyLChQyg3
        cDBxSLGiER01llRs+KeLF0UMldjIgYShnhEc0jQsY2ULGIZHdNDQaLCFBQ0jGqLJgkVLH4NLIh4x+OVC
        hgs2GjK6wsWKl0gFL2Ys+KjDhgoZ7jiMk4UnnYIiSRZMchRDEIqOvDC94ohgzJkE/XCooCEDo4p3tHQ9
        QzAojqEDcVzAcCHLRgBdtjAFNFAqTQBx6FpAIUkgIjt4HjEc1DPLS4FLRiYRaMkE4QxrBspZE4cNIEgG
        1bSswhjAGZmpAXzRYGHDDIJy3rSB4waOoIKLeGbRKjBRooFbjGJYODCSnDhq4LCOc3dgHjBlKDWIbPQj
        xBeGheIMdyNcj+bDhwWxZhOH9XH4GyPdEe6GPn74l9H31X8ACiIegQgmqOCCDDbo4IMQRijhhAPpkUdb
        EhICBk98QajIGlRkUYUWVjxISRxbdNWZHg7+oUVTinFRBoYLJjIGFllcgYUVYdzXYBhMtZQFH5NAqIVe
        WbhRWYSDeKEGIhQGBAAh+QQFBAAAACwCAAAAKwAlAAAI/wABCBxIsGDBQFKYFDLIsKFDh12QJMnysKJF
        g1SWGKF4sWMhNm0cMcRyRIkVhn1kqHjTcaAcM2vaMKyikWNBHSRSzGgpsI4aNGoGGcwisYrBMyVOmBjC
        E8CjM2zMuJlUMOPGgpFYpBiBYk9TAHvS/PRTkKTJglOUnmDyFQCkNlDNSCJI8+rAQCu4phDZ9o8asXQI
        Ek1idGAREydKhGk7cM0aqIYGWrVphyuJGxYdCSJE1WAioGneVBJo9srAGolPxGFoZ4WGMQAE7QHER9Hc
        gndgmjkk8I3GwADMoMj5w6CiGw0QNGgAAJCfPn+gJyro6KeaPwMVLRo4JukJoQMlRdZ5oADBggQVAFQK
        FEjPn9l+IhEMBCfOaIaOlrhAQ5DMhgPJNXAACHUMxAgg0PXxXCGdfdXHCeeVx4AEWNw3ECWMzMZHILNN
        11QWCQiYnAM+8MUQJYM81wcfgHxlAQMKnMdCHxc1MsiGLTZ1A4AXqMGTJY4oYiFPVmwxJGNIJqnkkkw2
        6eSTUEYp5ZSMleGFh1QORAdqJRSXJQCBGGFZCiZk+cgUZKZ2ghhTTpKGVmP+sJCUfeSQFGIl1LAalTTk
        pNcYt015yQpqNcHIlwLFQYMRfCAqUEAAACH5BAUEAAAALAMAAAAqACUAAAj/AAEIHEiwIMFCYLQkMsiw
        ocOGabJkKfOwosWCYrZcMXOx46I+fSQxLINFyxiGgoLoqNNRYCA7fPowHMPFCsWClJDU0AGkJYBAevDs
        UWTQjEQxBt3YyGHjiU9Kd/rY6UMJo0aOBCXt0EEjxx+fAArlCXqoIEmTBbnkuIHDClgAkvpEtVOJIE2b
        BAntoKEjx6O3ABDtGQuIoNEsSAdGYWsjDWCBevhEJSow48aBe/jWMPJY4COheWQKNFPyJoAhOJbaYZjn
        xgnHFQfBtONI4J2afASy0bFziUFGQDRY2KDBoqSgehYKbFRboJqlNwwRpJRlwwULGC6IuIgIUJ9LDR9d
        lfHxhqAaFBWEb6jQ4k7nh39oZL+e4cOX9w/DXFgvnMORSPg9NEIGF2SHQ2EBPjREeiTEkaBFkHghhiUP
        VmjhhRhmqOGGHHboYUWVODEEIR+S4YAAARTH4R0hBIDiAAFsuIgNAwjwogC+YSiJFATA6KMGe2QohwQu
        BmBkA2BsyEAAPg6gxF8b9mhjC2V16EUDITj4YUAAACH5BAUEAAAALAAAAAAwABoAAAj/AAEIHEiwoEEA
        ieKoaXSwocOHD+2kUSMHosWLDd+sOUMHo0eCkhgtqnRQDho1cQ4WirJkz0eDjg4xUnQQDhszcw5eMbLE
        ycuCjRQlWhTJ4JyJcAzaOaLkCJefBC8hanSoEUmCGjkWpLRkiRElgqAShJRIaFGCJlEWPKMESRIxYglS
        ajT1ECaCNnESPMSEpxJJcQk+WlSWEcGjapIO9OL2SMrABBMxmvpoYNaOAgPxNEJlEmS5Q4cOnHPy8SUp
        SZj2OdiHSI03PyHJPHT1z81BAul4XZIF5hIUJFKg+FlJqKLKAiGdBTCHKZJEBcOsOFHihIkYUB85SnT3
        IKQxTexgqb0xAniKETpcfrY4CIh16idcnFl/MY2J88BXSKGPccb7EkWExd9FTZRHg3gDYhQJGWgk6OCD
        PxWihhhnjIEGhRZSWEdgXogwggcifPBhiCOUEIcXWWSBRRZXYGEFiy5mAR1UiCTAgAIKILBAAjnuqIAE
        WmxxBRdUZFGFFkUeSYVhUB3SwAFPPskAlAdMaQEiZphRRhlkaMmllncEtkUJIIRgZplnpvBGQAAAIfkE
        BQQAAAAsAAAAADAAGgAACP8AAQgcSLCgQQCOAOmJdLChw4cPBeXR4weixYsN+/C5IwijR4+A8OypaDDR
        lyyBPqrs08dOyoKVylzR4kXlx0AT+xjsg0XLFTQ2PWrkCFMLFytZDgXFGHJkwThasmRpsxQjS5cEFXWx
        siULpaoXcerROVCNVCx6wF4c2lGgIa5Xxqi92JQkADBZerYtGAjKkDpzARxquUjgni1XuJQxCMmKjho6
        cAQGMOmr4Z5ZGBVMo+NGDRw2gEwuGOkNlz8E6xih8VgHDSSoRzs01AS05xw93Mh+CAcy6xo7tEza/RBI
        jhugoxgiDjEL6yB7mFuUxEa39OvYG94pksOHjh/dv+dj+LEFbJkXMljEaJF+vQwZdmSYOFHixPz690/E
        tpnoQoYLF1iAAYACAgiCCyiQkMIICS7Y4Agv2YTIBhVoUCEFGVyY4Qh6ANHDDjzo8GGIPfBQU1ViyOAC
        DC68sGKLLthAR0AAACH5BAUEAAAALAAAAAAwACUAAAj/AAEIHEiwoEEAkxwhqnSwocOHDx0lUrQIosWL
        DRMxQuQIo0ePjRItqmiwkRs0iD6qVNToUEeDcc6sWaPyo0RFigwSQqMGTZ2aHjVyLHhJDRszanICvRhy
        ZEE+atKoubMUI0uXBB0dXZOGYVWLN5UKtCMVjaCvF4W+BMDIzJozcSh9lNSokVemIklietNTTaKDhbpI
        2WPQ0aGNkuRafNRykkBBb9nEMSgJzBIjS5IYXKRIpMhHFytZGjiIZxrQAyvFWYLESJIjUAxeYmlo0eFE
        dy9O0qOmEME9VDALv3L2YKRFnjs7yv0xkZbXrZUwsQOx0qTDiFoyWuvxjvDLTMoo9bZoiVHnRIhIfoSi
        BMlrL39VQlIYsmYZzFECfaUEabTKSXTYcQlaBBZooENqgDDBBRNgsGCDCwbx1Ro8/JCDDzpYiOEPPuyR
        gAACBBBiACKSKCIdSy1SwgkmnLBiiy+e4IIBAwRQY4g32hgAdUAlksIIKJDwY5BDkjBDGxxcUIEFFCjJ
        pJJEfJXGDzr0sAMPVV6pgxB4HOjll2B6JMkUYEAS5kUzMKCABmacCdEGBzSAQAMn/OFmQ1cwkICaCygg
        BCN3GrTHCgcwEOcBElQRqEFmXMDnnh1QtShBTEhwKAMTTFqQITsosEACEWhqUB4oeNDFQAEBACH5BAUE
        AAAALAMAAwAtACIAAAj/AAEIHEiwoMGDAiP1yeMIocOHEAcCusNHT8SLGAEswrMHT6CMIBFiytPHzp6G
        IVMOJLQnjx5BKmNCKskHD6aYKgW5xKMIZ0pJdvjcAVTp4p8xaHri7KOHI8qCidKA+SjQi5UtV+Q8BZlI
        aJ8+BimxwcoFy0AvWbBoycLlzqWQijjmmVRQzxYsVrJc8TKQERguVLJU0dKlEEhKhvQ0IhhozNUrXKyU
        OVSwTxe1WdKmWRSTkRm9eLVw6WPpYCQ7V8hevZPyD+THXeAUfdjoDGgrXVJ6WZtWDaOMjSGTSQnnqhdD
        KSv9uSMpOR+wPqNLn079YhwlTIwsOZJ9O5Mlf6oThnRkI8eNGzVw2ECv/kYP8QMX6aAxf34O+jTuB4E/
        8A0TJEskoQSAAiLxBHT8JajgdJNsYUZzCx70QwYXnMBGhAahUIEGFmxAA0wYVkUhhRhcsMRWC/aBAwUZ
        bFhBCLmFCAAbJJB4QQYqWBSiJVR0sCGLIcgoECJFXFCiB0IOxAcNKwwnUEAAACH5BAUEAAAALAMAAwAt
        ACoAAAj/AAEIHEiwoMGDAisxSkQJocOHEAc6QsQIUcSLGAFISrQoUaOMIB16PLRoUsiTBB11VOQIpUtK
        jQ4tdOnSUSJFiSTRPGlJJqKWF9vgGOJn5yKcJQ82suPGosAXI1CQkJII5aOKjRQZtGRnzRk2ZgbGOFHi
        hAkWXiyF3NhRLUFBatKcUYOmzUBANlJEJZEixhyQlTY2HHgojhmvYONoHXjJjIuyJsgOEeTy0Ry6ctWs
        IYSwEZYSUvWq+HLS0NfDX/VcgkhIiFmyLk62iUvXzqOMcXDw7XFyz+E2jE5SUtMFaMhBgzDtXM68ufOL
        ea5ooZKlyvTqWrAUek7w0RElSJAYzklyRDx5JE24D2y0xEj7JUWUuI9vRIr6gXayWMlyBcv+/lZwQdl9
        BBboHCVhsGGgQ0mYVUMcCxp0w14pALFdhAKZgQJkJ5xgBYYCBVIEhSPAUMZ9j8RwwYcDxTHDa2Xh0Ad3
        UTRwQAN7EIRJFy7wFVVsz3mhwAIJpGBQIlBEVkIL3EHCAQI2onEQIEHooIZ6ZjAw5AWVgDjQCQ1AyYSX
        AvWhgJYQGEImAETciMAOay4iQQIMJHDHmlXYyMAJa17CwQIIcLAmAHlMIEEXEAUEACH5BAUEAAAALAMA
        AwAtACoAAAj/AAEIHCjwTxcviggqXMiwoUOBZaxsAfOwosWGaLJg0dLnoseLjK5wseIl0seTDuNk0UgH
        pUuFjryIvOLopU0Ad7SsPHPTZpctIgH1ZLglwoc5DAdtzAKG0s1IgvrUFGggwIAAMQYtVCOxilCXmALx
        udPnzsAEAqwKIGDE6cBFGrOYPWlJkZ48d/Tg4TOwDoSraQckGEMwD5gybj02AmRnbB87gaYKtASl6gAB
        aT3gsTnpj168e/YsaphIh9XTA5C4bES2MVlBmCru8ZA2QIACLvnsyaNXkKSPYCJYxeDSUGM+v1FGsmIk
        kcu6CYdKn069ekNAZNCIOTNGO3c0ZJxb1hcYaSkWK1munE+Phct4gY9GbqmihUoW+varUHwPoI8ZMmaU
        UQaAAmZnCH8IJihdJWq0pCBDVORwAxF2PKhQETToUIMOTIhnYRs42CAhDjd4AYmFAAwSBQ05ZEjDD2u8
        BwkPJHRB0B1BjCiiEYJYl8UGFWzQEUFl9OBiDj5YV8YFGFxgw0KMaHFDiDxYJwkKFgDpRlJNHAHHeGxk
        wCQJlqA4EA0aZEmFmQIFcoGYHCDCJgBNVJCmEXM2AsKbF+gxZxdAZkDDnACogIEFJxDaRwggkFFRQAAA
        IfkEBQQAAAAsCwADACUALQAACP8A2biI4QeAwYMIEypcmFDHCBQ2GEqcqHDIiRInzFDcKBEQiRQjYDTi
        SDIhFBMXrZRcCSCRi48lDLEs6eUESiEzS8ZA8fFNTo5xMJqoIekngEqOFFFaWOQhiTU/GTFC1OjQQkEo
        MHaZKUlRIkReFzEMM+PHo5WRHB2aWtXRJKMJLTHy+nWRoqJwEVKiupbqyLwJFS1K5NVRJcAKJa1NZAnx
        wkuSIjmeTLnyoTh13tCBk3lznTh/jVJCoyZNmjNq0JxOnQYq3EhszKyRXUYNbdtwABOaE4e3nN5xftdR
        VLm4cYWX4ux5exyMEiRS+hynYmRJ9SxiK9dJcuR5EiRqJFO4PuSliJLqRp7MeX1kRhmEfaB4706lUElB
        0g2CAakiUEI2TlRnXhMkSZEAA1EYpAZKJQyhkCNlIMFdE41tFEIDB5BwEA4fjbCeQohoYYUdJIWgwAIg
        HBSHTSfMgJgHDSAwAkJA8JTCVnmFcGAICA1ygk0sJJLXBxiKkBAWTiUIl4koJuSIC0Ll9xOMMipUBkgo
        ALHkjgvhcNENcBF5gJEKAfKCC2oseWKKx5UQYwrHARCHBBT4ZFRAAAAh+QQFBAAAACwLAAAAJQAwAAAI
        /wABCBxIEEAhMFoSFVzIsCHDNFmylHFIseJAMVuumLFIUUuBBHMYlsGiZQxHhxICDIDAcAwXKxNPMvwg
        QCWUhWYiipHJsI7KAAcUEsSokSfDFwJq5ig4sqTRhYMK/OxD0CXMpwuNDEjqgWDOLDuxEqyUYIBKLxcz
        bhRLMIxKAQ4gCTRDMibbgSB+XhF45yWfuwTx1BRgZGAjR4ALLlHAYVHix5AjS55MubLly5Ad9RG0uTNn
        zpEAV8KzJ0+eO3rwnE6dRw/gSX3s8JFtZw9t234SLwLkJ9Af3r6BI8ZMPHIeQZjXaMkSBnnlMVYycikz
        XHKfLCQjZqEzaXIiNVSyVLvRYsXLnruSpghhQ3CQl+XYtYgRyrFQoIFndNDQMaigHS/jhbfFSVlckEEW
        AsVxAw42PLHQI3FEhAUXJ7mwQQUyDGREDfrlwRAjZpRBFUcwXIBBCwPZkcOCQTzGggYWxEAQEzpwaBdb
        LxjoAkGH3LDiDo7d1cKFMBQExn41aAFYiScW9IgPNuRgg3NivRjjQmvolwMTd+WYwY4FYWIEDjUUISSR
        DBXygw9w3MUkipbFAOMNl9kBQgh03BUQACH5BAUEAAAALC8ALwABAAEAAAgEAAEEBAAh+QQFBAAAACwW
        AAAAGgAwAAAI/wDv0HCxB4DBgwgTKgQwxMSJHwsjLsyBgoQPiRgP8ihxQkfGjDpSjOjxEWMPhx5LRqRo
        UWXEjR1dLgw5UqbCkzFtImR5UedBHxx5+DzoRSSZoQcJFULKtKnTp1CjSp3qs5KiR4kcXc26tdLHS4kW
        JUqESNHYsmMTfazU6BAjt4YWwZXbqKSkRoscMcKrl+8kqoANYgqUiBJTO2rSwEGENI6ZNWfYyJHkc5Aa
        NIkv+/Fqs9GdMmoem3EjKCOlL1LoIEzUJjPmN3UPHhp0MM4SI0sOJezT5jFoNQfDcARjMA+SJEe6KJS0
        J81l4AZ3iIQIYFIV3Eb+LHw0R05pgzhTAlroo+Q4lEsleSLMcnsJm5IwxQNIhKQ8E0cfaZJEuAa7mY/h
        JRRJE0coccRSGKmX0By3KZFFRvEtREUSRkgBkkj7JYSIE0/YkVGAEVnykQ8VDcGUHi7EgAdTAQEAIfkE
        BQQAAAAsCwAAACIAMAAACP8AAQgcSBCAmgUG3BRcyLAhQQ8CBGhwSLHihAEBLlTcyNBCgAEUGCb6kiUQ
        x4UXMzIsc0WLl5MFL0ScsLAPFi1X0MAkmFIjwUpauFjJcmjnQI8gC8bRkiVLG6MDexJU1MXKliyUoAqU
        KYDmQDVNsejRKlCqQENWr4yZRBbAhY8VBF4Ck+WmoLYAjGBkInDPlitcyuAV2KfPwD03szAavDDSGy5/
        GEueTLmy5cuYM2vezLmz588cETmqdFnQnjx9HFkGZIfPnT5+SE9WpAfP6dpFJ0MaZGdPazt9EjGc4iCF
        cIaV1IAxPPARn9u2/UQi+CdBgwN8BS7KDQCP0C3HBxrU6vP7NMETDBQowCJQjY0cOgEACptmIaVCeWrz
        GVjmegOfACChAw1LDDQGYFYUwhAlfwRynCQZKLBAAvEBsMQNOBwxkCBMZfHSRlIc0AACLhBkhA41FMjf
        X1zYUZEiD0zYAHMCKfEeEgQ1kgVTXDxC0Q0jNuBDQUcMqOJAdqQVh0N2TMiABIsRdGGGBU3SxU1XhFdQ
        Cwhcl8VCJ6a40B5CZSEYQxqk14FsBNmYA44LiZGFFWI01N8FdTBUJIEMMdKFFzTuNKWGli2BIhSX+fFD
        EIHCFBAAIfkEBQQAAAAsCwADACMALQAACP8AAQgcSLCgQQB9ZKh4c7ChQ4OTdJBIMeOhRYdnSpwwMeSi
        R4KRWKQYgWLPx5NTNp5gcvJjoBUkUzhq6bGIiRMlwtC8aIckiRuSdj60VAPniThCH5pBMfFH0odjNJ4Y
        9NShoyUu0FTdyrWr169gw4odS7as2bNo0yZ9VImso0WJGAUN6+gQI0SNFGECG0lRIrh+I136SgmSoUV2
        DzV61HCLBhuKHNp5I4ggJUWA/zJqSzDQhQ0UqgxslGhgIDZm1jQq6KiR3cOlCdLIcOGCF4FxjihBCsBQ
        GjVo7BisBCmR30WDBarZUEEDCUsCqywxkkUgpThnUC86eGmRI8bWT1ybwHDBzcAsSJKIFohIze82ey9q
        aW5BB0Eq06sPlLMme5+Li2xAngaAEISFblYQBAka7q0RiUVAaGDBBkcUJB11BQGS2hl8PJQHeRl8MBNB
        6KlXUCVsMIjGag3lMGEFOhWEH4YFCYKaGnI4dAJtKxx0oBIJGvSGGmfA4dAaG5SAx0EX6lfQI2uwUZlQ
        Ja4XFhbTdTHWIE1AMeVOAQEAIfkEBQQAAAAsCwADACIALQAACP8AAQgcSLCgQQByFBDocrChw4KSJAQY
        wOChxYZRJgb4cLHjwEYEBgQQAMejRxsDBAhoYbLjHZEDBiRqeTGEygBIaFoso/HBI50OKTkQMDHMwURp
        wASiKQWmBoOU2Gy5wgULTSYTBeQhWEnPFixWslzxQlNRigNSCAYaY2UqFytlDgElyMiMWLBauPSZS/AP
        1bZXusCpxJegFy1ZsmBRw6hwQThtvRhybLASnz6XKGvezLmz58+gQ4seTbq06c5jVAhpfPCSID+KaBYy
        kWIEWYGPGg1E1McOH0gtgZwwccKMQD1YtOAR2CiPHjyCTMKpjWLGQDJvzwisBOhO70geaxCXLzFnoN0s
        YgY62uOcT8cxI1CQMEJQzFTjA/3w8T75YaMVJ5SQQnQDlZHcGARNggd7e1Dy0BLypZAWQWO8VUZBifh2
        R38H9RHgCS78RNB56RXEx4J4gHeQESTUloZB9l2BH0GJ9LaHHw3VMFwOBxmoBYIG9aHHHTgeFEcKNLhn
        UIVwHUTJHn3MpBOJn5UxlRqfIcKFZEAFBAAh+QQFBAAAACwDAAsAKgAlAAAI/wABCBxIsKDBgwgF9iFS
        403ChxANOlqCgkQKFBEzJqQUZsWJEidMxNBIkuCbGyMqphihY09JkoOAhPx4wsWZSi81pjGxsuIKKZFy
        kpwxs0QRQUJLNklJw07Sl5HIoLH0tKrVq1izat3KtavXr2ABWLrC5FDYgWkyLEhg4uweFwcYHGiQAGyj
        Hw0UrGWwQIrXSVkgNJiLoEGJP17teEiwVgGCDGUIqtHxpBFCTI4aBY14ofDcBn4JIqqhg0ZkgZMcDXzU
        6BAjShEfMNCbI5HBJjlu3GgjUBAaNYAEUkqkKJFqiGQwtKhzsE5pHUAwCYzDxgydgY4Qtcb5lMgNHDbw
        DJ2ck0YNnIGSFhFX9FQNDR01Qgt8s+bM9YGKGGk//tKRDvA6GEKQHL/FQZAliai3CHclWQGfDloUBEd1
        cxQ0iWuISPJSIODl0AMkBZFnXkGXKJgIgxlJQRoN5xVEn30GsXbIIuyRNERuRhxEoBoGGsSIIogwUtId
        OQwRyEETmlGhQZck0sgjL0mHkIgtHnTJVnHU59RZAjGyhhs1vhQQACH5BAUEAAAALAMACwAtACUAAAj/
        AAEIHEiwoMGDCAXKAeGgS8KHEBMmWjEgwIABETNGjISEQIAAAgIk0EjyoJcHFlNKgFOyJYA8HEJ+HFAg
        CiWXJaWkrDiARiOcLRUMEBAyRB6gLllYbFAGKU5HTqjcdEq1qtWrEQGRQSPmzBiuXtGQSYSVYCQsWrJg
        sZLlytq2WLiUHfiIi5UtVbRQyZJ3bxUwcwf2MUPGTJkyhQ9vNRS4seOCmLxUQfT44BsTGC7MqEzQjw4K
        GSpouMAZwKMkozNnwKCFc5gOG0Rb2CAjUGU9Ky5kvmDhxBqCc5ZweVS2xGzRGbRAIrjIyJIiLAVWikSV
        QwbdQhQZ1KIECRI6AhPhf9lDFqmaEjnuHNyzxPkTwX3s2G5cSQqSJEf8DAyUR08fx3I4Z8QXBPXBxx2C
        NAbJEvgtUZ5AgIynX2BgtLdEUwXGN19ZheCnBBOSFMSff4F5IaB6BRmIYGBSdFfFQRHuMWFZfSQhxSAH
        9aFhaSL29x+Pgh2YIJACQcKHH444FRAAIfkEBQQAAAAsAwAWAC0AGgAACP8AAQgcSLCgwYME1/D4kcOH
        DoYOf/jYg7CiRYKLSpwwcUIjR48nXFwciTBRihEoSJxMuZLEDJIwC6b5oaPHDh41b+oQgiemz59Ag8Is
        4yWRUKF0anT8cfRnICMoVZpoGnNKio8nToihOjINi5YpfhTiarFPDpAlasQhuEcLGkhkB9JQiTLFGEkE
        HV3hQiXPQEyUqK7YeKIJI4NmtGTBwkegpESL4B6NQ8NIY4OBuFjZ4mWgokaHHMU9GCbLYkIDHSVSpGh0
        QT6br6whmIgRItGuBU7asnjL4YGNIC/KLZDNlr1wCn4OTTzRYi1cAudd3Tq3mr1W/hisfZs4GMVjLBkf
        DL5oeO5BWcIgOrgcN/GKqlm/t6jI9qP5FSvVntQ0IAAh+QQFBAAAACwAABYAMAAXAAAI/wDfsPAg4sMI
        ggY9lFACoKHDhxAjArhyAcOECxMsYsSAwU0FBQsShFSAYGTJOxJTQjQUYICAAAFewpR5QEIDBA0OMDiQ
        c2dOPSqDAiA0oKXRl0VfKohzIoRTEE5DQBXBRKhQKhkoXKhgQStXChzWWB1LtqzZs2gdrimzCO2lPIIm
        pW2YZwgOG03QrtGSJYwgtISk0MhBQ0cNtGOsbLnCpYwjspG46LhxN8cNNWj7ZMHCN0sWOnJVUoKzQ0fh
        GjqWIJqbSA2VLFW0WPGyJ6UgIzbu3qhBxM7DQWrqSDI7yEtnzmISRRyCunAOzA8jnWFTJhBaO15iv94C
        EZMOyziwPGOGOEdNmjR/0T6K4xkLF0sQ7QSR8kfiITZm1rCZ25CRmTJ9nAVHGmqgoQh//A2S3xl4IDhX
        JWoUqMYjDqZlxxrTAVXhWY1IuEYlG551x3RmGBLiWW6YB8eJZymCBhxtsfhQQAAAIfkEBQQAAAAsAAAW
        AC4AFwAACP8A5+RgEaOFDIIGWciQAqChw4cQI0qEGOICBosXLli4qBHDnokgQ0L0oMHCBgoZKmhAqZJC
        H5EwQdqhAcPFCxc1b8J4USWmz59AgwodShQiHTiNhFIKw0bS0D9SkhzRIjTJCRM14gA99KWIEiNLjAi9
        MQIFiRRACsGUdGYJEqlKkMgRagZFiasnTlhxBPLOkrCAsywiGqhI2bMjYJSJWKjKEalIjEh56VBRnkCV
        gMaZgfcuDsoNpYAFq2Tuw0p3+thJJLSLC8QoXDxcEjeJGEgR/+zJk4e10ERQTJwo0SJzwz5QvAyS2Eg1
        n49FAQXRocanJT959ODhW1QoIzt87hAu6i7Ukh7te5ySBxoofB9B64FGQr/HUvyfg1LbSXrfZ5/dfvT3
        EyR4/PGIgA4FBAAh+QQFBAAAACwAABYALQAYAAAI/wDvFMnhQ8cPggZz/NgCoKHDhxAjSnwow8SJEics
        YtR44s/EjyAfukBBIsUIkiZRjggUsuVEPUB67OChQybNHjy8uNzJs6fPn0CDNtSTx9HPSEuyPPpJCEwW
        LGd+nhggwEEYnorWUMlSRYuVnw8CDBDLgU9ISnG2ZHmqJYuen1AEiBUgN4ciiZX+aOFiZcsVLmWMArUD
        IoDcsQecQEw0BkuWK1ishBH0UFIiwXjVgOnzMMwCqoclyHEY5m/fLHwmPcSEqJEhSRPx8N2S6GElIgXE
        Gj7gUEvbLG5gQ2S0KFEiSBMBrcWSJiKhF3QDFHA4yIsaRBIjNTrEqPbESWNMF3SSWKeDhCs7GyVSlIgS
        SEG/vVwS2pASd0TIQ5bxy8UOfUyKsLdIJS01kkVbXCwVFCOMtNbITnb0dUUcQVUioCKY7DRJF1hocYV3
        Pj3S2iHu8bQHX1mUAZQixT3okxhZWCEGUJYg4ohqPjHShRec0ReUJR8FBAAh+QQFBAAAACwAAAsAJQAj
        AAAI/wABCATDIcUdgQgTKlzIcKGEAwwO7DDUsKLFhBQUIFiQ4EGTShdDLsyzocGBBRApoBHJUuClKxI2
        JtCYYk9LloqGQDxgssGTmyz7oNCY4IAEoC3LbOA5AmlLSVyYKHJKtapVlnykGFlyhMnWrkaYkLkKIMgN
        HDbQ3qihlq2gqz101NBBIwcNunbpFrrqp8mSJEqQ/A28RIkZsogTK24YKBCkxQkVvVGDhg5kAI7wlFFj
        Zs1hxZX4qElDefTbxIbUsOl8hk2cSIkbwUFDOc0ZOIluRjoyo0xDOK07pxlECSiYFCNUBGKoZrQaPCCR
        qjFxosQQhonY3FlUVRIOEsjnXDsWGOcE9RmWxgMAggJ8F/WDTphnkXs8lhHto6h35KKE+T7qlYEcCkCo
        dwkO1d2gHgCAvOCCGgsKBFJAAAAh+QQFBAAAACwAAAsAJQAlAAAI/wABCDRzgoYegQgTKlzIcKGHCRcm
        FGlIsaLCEBYoXKigoYoliyAX8jmBQULECyHahFyJsEsHjRUy1vDDcuUiJhAnYIB4pebKQDQyVpjAwSdL
        NSd0ujDKkpIYKoyYSp1K9acXKlmqaMGqFSubqgCiIElyhCwSI2bRGqrKZImRJUWUvI07N1HVQVqyXMFi
        RS9fvW/ACh5MuCGiRZMKJ4TURw+eQIoBQCJkZ48dPnYKWyK0J4/jznYHN8rT5/KdPn0qDY70B4/jPHf8
        OKrpqMSCJw3/nL6Mh5Fqi48aIVQyIMCAzAv1dNYj6CNIPVi04BF4RYCAAB4YPtoz6BFLMlysnHQR+CiC
        8QBjBJvJkkUMwjADrC+gBFbMlitmEnIoPoAI2DLRpYeQHsYJQAAhVY0RXhkK6XBeDFWt155CihhwnhxU
        2YffQk4UJ0BRUwGohYAJWSLBdRBQpaAVDC5UxwEG9DSVhO5FJlAZ96lho0CIcOHFWhUFBAAh+QQFBAAA
        ACwAAAMAGgAtAAAI/wABCBxIEAAYCRXuFFzIkKCHAw04NJxYUMWCBAyuUNyYB0GDAxIUbaS4Q8FFISMn
        GpLgEQGglA2dMDCZAiZDSxUaeCRDcU0NIH4YmsGoIEOkiSxCmAjxJNFCFhAPeJnogoSIEiNQcCnYh0GC
        BU0mAqJxIkQJpS7iEJRi4QSjjWVWXB1hNUggmwMbUVEaouyJqXgFDvphdUQIFYEJvqHRV0figmm4NHpM
        ufLAQmrEnBmDRjNnzXVSesmSBUuWK1isnE6dxelGLVuucKGSpYoW2rapvN2IyIyZMmXI/A7+W6Hl48gZ
        PppEqXKlRYoSOXpMCZKhRYcYGQqMydGiRNG/H221SSlRo+yIGinCZLNSI/CJEqWfFNhR+uyJmo88NIhg
        ou+KOMLeSGGUcAIYA1mSyHI27ZDCCD9Y1oMJJzhWWQ4okOCDZTwYaCFlOjzYg4QUfvgYhhpy6KFlIY4w
        YmUTVmiZDxkOYZkeLsSAR0AAACH5BAUEAAAALAAAAwAaAC0AAAj/AAEIHEgQgJkPIvYUXMiQ4IoKGlA0
        nFjQBoYLGbxQ3MjHwoYKIRptpFjkwsUlIyce6uDRgqCUDa1kMFkDJkNMIjR4VEMxTpAlgRiywXjhhKSJ
        Ol7QgJGFYQ6IFcpM3DEjBg0ZNsgU9DMTg5WJgn7UcEFD6Y47BLWMoOGIIiY1OazKqLqEkM2BjrxYhVFD
        qdS7Ag0tqSoDBg7ABOkI4WsEccE3Yx45nkx5YKI7b+jAqZN585s6fVK+SaMGTek0Z06nFrlRzZozbMqo
        MbNGNu0ykjcymjMnjpw4vX/PkQOosvHjyJMrX868OcU/CisnCTAgSeUKAwJcqHxBgIAJlSdkJd9O2QJ1
        CuHHc/cOnrJ47ZXND0DvXj3l7t8rY8juoXKbAwisERAAIfkEBQQAAAAsAAAAABoALQAACP8AAQgcSLCg
        QQBvLkiIc7DhQRUKFpxwSHGghwYIRlRk4yKGn4MhEjAIUVHHCBQ2Dn5ocEBExSEnSpwwYzBERBAVAZFI
        MQKGwYsZKwKAYiKmlYIhRwpN5GJnCUMEV7YUCsDLiaJCCNpcgJNqDBQ731jEqFGgHSdXCh2MI9NEjYEj
        RJIUmOQHkR9jGBkscpLEGoFPWE4ZqEQIkCFBjPwlKAiFzC4D/wAieIhJER9D7CbpQzDMjB+PKsY5cjiI
        YSyHqBZ8ZMbujyJ22agumMiK4SA/jsw2qOfJaym7D9pZHLy48YaOBvURtLw5c0FqhfrJowdP9Tx3rmeH
        JFQPnzt97OxwscNHPHk7k4RC+hNIsp/2gN7/QXS8vv37+PPr339/jxY03B3nyBVcUJFHfWZokQUWfBwX
        CBdWbOHFcZWEkcWChBzHR4RXEBfcJFssuIVexrGxBYFwHJfIglpwQclxahBoxR/1gaHgGPYNkkUY9FEU
        EAAh+QQFBAAAACwAAAAAGgAuAAAI/wABCBxIsKBBAHNGeLBzsOHBGxcw0HBIcSALDRZiVJTTA8iggy8u
        ZHBR8QgNHUQOtthQAUbFJzhs5GhjEEbEFhUH1dBB44ejghczVgSg5UZMLwVDjhyqqMfOGokIrmw5FICZ
        HEadELSJAWfVHzp20rGIUaPAPlvERDV4R+aNIZQExhD5YuCVJlKcsGlkUMpJGnIEXmHJxW6UJ1KgTJlz
        iWChmDjMDBT0ceCiLFOYSMFrRRBBNUGWRKqIpwpiKIfHKKpaEFIcJ3mn4J3DuiCjMIehOKFS22AfLk6m
        dOltsBKfOaOJK1/ecBKkRI4UPYIuHfrPoYwSKdKeKBGi7d4VVX8aiogRokaGFh1ilH69ocYVKzFyxKjR
        Ivr26UNizr+///8ABiigf2OoIAQj/BViQgojIMUcECeYcIJky8HBIAozWLIcJTVIWAJty40xAgokGMFc
        IyucUEIKni23BIkpSMFcHyqe4MIjzBlBAoNp8FdDhDmMx1wcKdDAh3/wVRQQADs=
</value>
  </data>
</root>