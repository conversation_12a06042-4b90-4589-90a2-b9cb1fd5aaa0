using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace EmployeeManagementSystem
{
    public partial class DailyTrackingForm : Form
    {
        private int workPeriodId;
        private DateTime startDate;
        private DateTime endDate;
        private string projectName;

        public DailyTrackingForm()
        {
            InitializeComponent();
        }

        public DailyTrackingForm(int workPeriodId, DateTime startDate, DateTime endDate, string projectName)
        {
            this.workPeriodId = workPeriodId;
            this.startDate = startDate;
            this.endDate = endDate;
            this.projectName = projectName;
            
            InitializeComponent();
            SetupForm();
            LoadEmployeesData();
        }

        private void SetupForm()
        {
            this.Text = "التتبع اليومي لفترة العمل";
            lblTitle.Text = $"التتبع اليومي - {projectName}";
            lblPeriod.Text = $"الفترة: من {startDate:dd/MM/yyyy} إلى {endDate:dd/MM/yyyy}";
        }

        private void LoadEmployeesData()
        {
            try
            {
                // الحصول على موظفي فترة العمل
                var employeesData = DatabaseHelper.GetWorkPeriodEmployees(workPeriodId);
                
                // إنشاء جدول للعرض
                var displayTable = new DataTable();
                displayTable.Columns.Add("كود الموظف", typeof(int));
                displayTable.Columns.Add("اسم الموظف", typeof(string));
                displayTable.Columns.Add("إجمالي الأيام", typeof(int));
                displayTable.Columns.Add("أيام الحضور", typeof(int));
                displayTable.Columns.Add("أيام الغياب", typeof(int));
                displayTable.Columns.Add("أيام الإجازة", typeof(int));
                displayTable.Columns.Add("معدل الحضور %", typeof(string));

                foreach (DataRow empRow in employeesData.Rows)
                {
                    int employeeCode = Convert.ToInt32(empRow["EmployeeCode"]);
                    string employeeName = empRow["EmployeeName"].ToString();
                    
                    // الحصول على ملخص التتبع اليومي
                    var (totalDays, presentDays, absentDays, vacationDays, attendanceRate) = 
                        DatabaseHelper.GetDailyWorkStatusSummary(workPeriodId, employeeCode);
                    
                    var newRow = displayTable.NewRow();
                    newRow["كود الموظف"] = employeeCode;
                    newRow["اسم الموظف"] = employeeName;
                    newRow["إجمالي الأيام"] = totalDays;
                    newRow["أيام الحضور"] = presentDays;
                    newRow["أيام الغياب"] = absentDays;
                    newRow["أيام الإجازة"] = vacationDays;
                    newRow["معدل الحضور %"] = $"{attendanceRate:F1}%";
                    
                    displayTable.Rows.Add(newRow);
                }

                dataGridViewEmployees.DataSource = displayTable;
                
                // تنسيق الأعمدة
                foreach (DataGridViewColumn column in dataGridViewEmployees.Columns)
                {
                    column.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleCenter;
                    column.HeaderCell.Style.Font = new Font("Cairo", 9F, FontStyle.Bold);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnEditStatus_Click(object sender, EventArgs e)
        {
            try
            {
                if (dataGridViewEmployees.SelectedRows.Count == 0)
                {
                    MessageBox.Show("الرجاء اختيار موظف لتعديل حالته", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var selectedRow = dataGridViewEmployees.SelectedRows[0];
                int employeeCode = Convert.ToInt32(selectedRow.Cells["كود الموظف"].Value);
                string employeeName = selectedRow.Cells["اسم الموظف"].Value.ToString();

                // فتح نموذج تعديل الحالة اليومية
                var editForm = new EmployeeStatusEditForm(workPeriodId, employeeCode, employeeName, startDate, endDate);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    // تحديث البيانات
                    LoadEmployeesData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تعديل الحالة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadEmployeesData();
        }
    }
}
