using System;
using System.Data;
using System.Data.SQLite;
using System.IO;

namespace EmployeeManagementSystem
{
    public class DatabaseHelper : IDisposable
    {
        private static string? _dbPath;
        public static string DbPath
        {
            get
            {
                if (string.IsNullOrEmpty(_dbPath))
                {
                    string appDataPath = Path.Combine(
                      Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                      "GXR", "HRMSDB", "net6.0-windows"
                    );



                    if (!Directory.Exists(appDataPath))
                        Directory.CreateDirectory(appDataPath);

                    _dbPath = Path.Combine(appDataPath, "EmployeeDB.sqlite");

                    // إذا لم تكن قاعدة البيانات موجودة، انسخها من مجلد التثبيت
                    string originalPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "EmployeeDB.sqlite");

                    if (!File.Exists(_dbPath) && File.Exists(originalPath))
                    {
                        File.Copy(originalPath, _dbPath);
                    }
                }

                return _dbPath;
            }
        }

        public static string ConnectionString => $"Data Source={DbPath};Version=3;";
        private SQLiteConnection? _connection;
        private bool disposed = false;

        public static void InitializeDatabase()
        {
            try
            {
                // التأكد من وجود المجلد
                string? dbDirectory = Path.GetDirectoryName(DbPath);
                string directory = dbDirectory ?? AppDomain.CurrentDomain.BaseDirectory;
                
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                if (!File.Exists(DbPath))
                {
                    SQLiteConnection.CreateFile(DbPath);
                }

                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // Create or update tables
                            string createTablesSql = @"
                                CREATE TABLE IF NOT EXISTS Courses (
                                    CourseId INTEGER PRIMARY KEY AUTOINCREMENT,
                                    EmployeeName TEXT NOT NULL,
                                    CourseType TEXT NOT NULL,
                                    CourseNumber TEXT NOT NULL,
                                    Category TEXT NOT NULL,
                                    StartDate TEXT NOT NULL,
                                    EndDate TEXT NOT NULL,
                                    DaysCount INTEGER NOT NULL,
                                    GraduationGrade TEXT
                                );

                                CREATE TABLE IF NOT EXISTS Employees (
                                    EmployeeCode INTEGER PRIMARY KEY AUTOINCREMENT,
                                    Name TEXT NOT NULL,
                                    MotherName TEXT,
                                    DateOfBirth TEXT,
                                    Province TEXT,
                                    Nationality TEXT,
                                    IdentityNumber TEXT,
                                    StartDate TEXT,
                                    AdministrativeOrder TEXT,
                                    StatisticalNumber TEXT,
                                    KeyCardNumber TEXT,
                                    PhoneNumber TEXT,
                                    Category TEXT,
                                    BadgeNumber TEXT,
                                    BadgeExpiryDate TEXT,
                                    PhotoPath TEXT,
                                    MaritalStatus TEXT,     
                                    WifeName TEXT,           
                                    ElectoralNumber TEXT,   
                                    EducationLevel TEXT     
                                );

                                CREATE TABLE IF NOT EXISTS Documents (
                                    DocId INTEGER PRIMARY KEY AUTOINCREMENT,
                                    EmployeeCode INTEGER,
                                    Description TEXT,
                                    FilePath TEXT,
                                    UploadDate TEXT,
                                    FOREIGN KEY(EmployeeCode) REFERENCES Employees(EmployeeCode)
                                );

                                CREATE TABLE IF NOT EXISTS Vacations (
                                    VacationId INTEGER PRIMARY KEY AUTOINCREMENT,
                                    EmployeeId INTEGER,
                                    EmployeeName TEXT,
                                    VacationType TEXT,
                                    StartDate TEXT,
                                    EndDate TEXT,
                                    DaysCount INTEGER,
                                    Reason TEXT,
                                    FOREIGN KEY(EmployeeId) REFERENCES Employees(EmployeeCode)
                                );

                                CREATE TABLE IF NOT EXISTS Users (
                                    UserId INTEGER PRIMARY KEY AUTOINCREMENT,
                                    Username TEXT UNIQUE NOT NULL,
                                    FullName TEXT NOT NULL,
                                    Password TEXT NOT NULL,
                                    UserType TEXT NOT NULL
                                );

                                CREATE TABLE IF NOT EXISTS Settings (
                                    SettingId INTEGER PRIMARY KEY AUTOINCREMENT,
                                    CompanyName TEXT,
                                    CompanyLogo TEXT,
                                    Theme TEXT,
                                    Language TEXT,
                                    LastModified TEXT
                                );

                                CREATE TABLE IF NOT EXISTS Attendance (
                                    AttendanceId INTEGER PRIMARY KEY AUTOINCREMENT,
                                    EmployeeCode INTEGER,
                                    EmployeeName TEXT,
                                    Date TEXT,
                                    CheckInTime TEXT,
                                    CheckOutTime TEXT,
                                    WorkingHours REAL,
                                    OvertimeHours REAL,
                                    Status TEXT,
                                    Notes TEXT,
                                    WorkPeriodId INTEGER,
                                    CreatedDate TEXT,
                                    FOREIGN KEY(EmployeeCode) REFERENCES Employees(EmployeeCode),
                                    FOREIGN KEY(WorkPeriodId) REFERENCES WorkPeriods(WorkPeriodId)
                                );

                                CREATE TABLE IF NOT EXISTS WorkPeriods (
                                    WorkPeriodId INTEGER PRIMARY KEY AUTOINCREMENT,
                                    EmployeeCode INTEGER,
                                    EmployeeName TEXT,
                                    ProjectName TEXT,
                                    Description TEXT,
                                    StartDate TEXT,
                                    EndDate TEXT,
                                    WorkingDays TEXT,
                                    DailyWorkingHours REAL,
                                    Status TEXT,
                                    CreatedDate TEXT,
                                    FOREIGN KEY(EmployeeCode) REFERENCES Employees(EmployeeCode)
                                );

                                CREATE TABLE IF NOT EXISTS WorkGroups (
                                    GroupId INTEGER PRIMARY KEY AUTOINCREMENT,
                                    GroupName TEXT NOT NULL,
                                    Description TEXT,
                                    CreatedDate TEXT NOT NULL,
                                    CreatedBy TEXT,
                                    LastModified TEXT,
                                    IsActive INTEGER DEFAULT 1
                                );

                                CREATE TABLE IF NOT EXISTS WorkGroupMembers (
                                    MemberId INTEGER PRIMARY KEY AUTOINCREMENT,
                                    GroupId INTEGER NOT NULL,
                                    EmployeeCode INTEGER NOT NULL,
                                    EmployeeName TEXT NOT NULL,
                                    MemberType TEXT NOT NULL,
                                    AddedDate TEXT NOT NULL,
                                    FOREIGN KEY (GroupId) REFERENCES WorkGroups(GroupId),
                                    FOREIGN KEY (EmployeeCode) REFERENCES Employees(EmployeeCode)
                                );

                                CREATE TABLE IF NOT EXISTS Notifications (
                                    ID INTEGER PRIMARY KEY AUTOINCREMENT,
                                    EmployeeID INTEGER,
                                    Message TEXT,
                                    Type TEXT,
                                    IsRead BOOLEAN DEFAULT 0,
                                    DateCreated DATETIME DEFAULT CURRENT_TIMESTAMP,
                                    TargetDate DATETIME,
                                    FOREIGN KEY(EmployeeID) REFERENCES Employees(EmployeeCode)
                                );                                -- إضافة إعدادات افتراضية إذا لم تكن موجودة
                                INSERT OR IGNORE INTO Settings (CompanyName, Theme, Language, LastModified)
                                SELECT 'اسم المؤسسة', 'Default', 'ar', datetime('now')
                                WHERE NOT EXISTS (SELECT 1 FROM Settings);";

                            using (var command = new SQLiteCommand(createTablesSql, connection))
                            {
                                command.Transaction = transaction;
                                command.ExecuteNonQuery();
                            }

                            // تحديث قاعدة البيانات الموجودة لإضافة الأعمدة الجديدة
                            UpdateExistingDatabase(connection, transaction);

                            transaction.Commit();
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في تهيئة قاعدة البيانات: " + ex.Message, ex);
            }
        }

        private static void UpdateExistingDatabase(SQLiteConnection connection, SQLiteTransaction transaction)
        {
            try
            {
                // التحقق من وجود عمود WorkPeriodId في جدول Attendance
                string checkColumnSql = "PRAGMA table_info(Attendance)";
                bool hasWorkPeriodId = false;

                using (var command = new SQLiteCommand(checkColumnSql, connection))
                {
                    command.Transaction = transaction;
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string columnName = reader.GetString("name");
                            if (columnName == "WorkPeriodId")
                            {
                                hasWorkPeriodId = true;
                                break;
                            }
                        }
                    }
                }

                // إضافة العمود إذا لم يكن موجوداً
                if (!hasWorkPeriodId)
                {
                    string addColumnSql = "ALTER TABLE Attendance ADD COLUMN WorkPeriodId INTEGER";
                    using (var command = new SQLiteCommand(addColumnSql, connection))
                    {
                        command.Transaction = transaction;
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                // في حالة خطأ، لا تفعل شيئاً (العمود قد يكون موجوداً بالفعل)
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث قاعدة البيانات: {ex.Message}");
            }
        }

        // دالة لإعادة إنشاء قاعدة البيانات (للاختبار فقط)
        public static void RecreateDatabase()
        {
            try
            {
                if (File.Exists(DbPath))
                {
                    File.Delete(DbPath);
                }
                InitializeDatabase();
            }
            catch (Exception ex)
            {
                throw new Exception("خطأ في إعادة إنشاء قاعدة البيانات: " + ex.Message, ex);
            }
        }

        public static int AddEmployee(Employee employee)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"INSERT INTO Employees (
                     Name, MotherName, DateOfBirth, Province, Nationality, 
                     IdentityNumber, StartDate, AdministrativeOrder, StatisticalNumber,
                     KeyCardNumber, PhoneNumber, Category, BadgeNumber, BadgeExpiryDate,
                     MaritalStatus, WifeName, EducationLevel, ElectoralNumber,
                     PhotoPath)
                     VALUES (
                     @Name, @MotherName, @DateOfBirth, @Province, @Nationality,
                     @IdentityNumber, @StartDate, @AdministrativeOrder, @StatisticalNumber,
                     @KeyCardNumber, @PhoneNumber, @Category, @BadgeNumber, @BadgeExpiryDate,
                     @MaritalStatus, @WifeName, @EducationLevel, @ElectoralNumber,
                     @PhotoPath);
                     SELECT last_insert_rowid();";


                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Name", employee.Name);
                    command.Parameters.AddWithValue("@MotherName", employee.MotherName);
                    command.Parameters.AddWithValue("@DateOfBirth", employee.DateOfBirth.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@Province", employee.Province);
                    command.Parameters.AddWithValue("@Nationality", employee.Nationality);
                    command.Parameters.AddWithValue("@IdentityNumber", employee.IdentityNumber);
                    command.Parameters.AddWithValue("@StartDate", employee.StartDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@AdministrativeOrder", employee.AdministrativeOrder);
                    command.Parameters.AddWithValue("@StatisticalNumber", employee.StatisticalNumber);
                    command.Parameters.AddWithValue("@KeyCardNumber", employee.KeyCardNumber);
                    command.Parameters.AddWithValue("@PhoneNumber", employee.PhoneNumber);
                    command.Parameters.AddWithValue("@Category", employee.Category);
                    command.Parameters.AddWithValue("@BadgeNumber", employee.BadgeNumber);
                    command.Parameters.AddWithValue("@BadgeExpiryDate", employee.BadgeExpiryDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@MaritalStatus", employee.MaritalStatus);
                    command.Parameters.AddWithValue("@WifeName", employee.WifeName);
                    command.Parameters.AddWithValue("@EducationLevel", employee.EducationLevel);
                    command.Parameters.AddWithValue("@ElectoralNumber", employee.ElectoralNumber);
                    command.Parameters.AddWithValue("@PhotoPath", (object?)employee.PhotoPath ?? DBNull.Value);
                  
                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        public static void UpdateEmployee(Employee employee)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"UPDATE Employees SET 
                    Name = @Name, 
                    MotherName = @MotherName,
                    DateOfBirth = @DateOfBirth,
                    Province = @Province,
                    Nationality = @Nationality,
                    IdentityNumber = @IdentityNumber,
                    StartDate = @StartDate,
                    AdministrativeOrder = @AdministrativeOrder,
                    StatisticalNumber = @StatisticalNumber,
                    KeyCardNumber = @KeyCardNumber,
                    PhoneNumber = @PhoneNumber,
                    Category = @Category,
                    BadgeNumber = @BadgeNumber,
                    BadgeExpiryDate = @BadgeExpiryDate,
                    PhotoPath = @PhotoPath,
                    MaritalStatus = @MaritalStatus,
                    WifeName = @WifeName,
                    EducationLevel = @EducationLevel,
                    ElectoralNumber = @ElectoralNumber
                WHERE EmployeeCode = @EmployeeCode";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employee.EmployeeCode);
                    command.Parameters.AddWithValue("@Name", employee.Name);
                    command.Parameters.AddWithValue("@MotherName", employee.MotherName);
                    command.Parameters.AddWithValue("@DateOfBirth", employee.DateOfBirth.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@Province", employee.Province);
                    command.Parameters.AddWithValue("@Nationality", employee.Nationality);
                    command.Parameters.AddWithValue("@IdentityNumber", employee.IdentityNumber);
                    command.Parameters.AddWithValue("@StartDate", employee.StartDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@AdministrativeOrder", employee.AdministrativeOrder);
                    command.Parameters.AddWithValue("@StatisticalNumber", employee.StatisticalNumber);
                    command.Parameters.AddWithValue("@KeyCardNumber", employee.KeyCardNumber);
                    command.Parameters.AddWithValue("@PhoneNumber", employee.PhoneNumber);
                    command.Parameters.AddWithValue("@Category", employee.Category);
                    command.Parameters.AddWithValue("@BadgeNumber", employee.BadgeNumber);
                    command.Parameters.AddWithValue("@BadgeExpiryDate", employee.BadgeExpiryDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@MaritalStatus", employee.MaritalStatus);
                    command.Parameters.AddWithValue("@WifeName", employee.WifeName);
                    command.Parameters.AddWithValue("@EducationLevel", employee.EducationLevel);
                    command.Parameters.AddWithValue("@ElectoralNumber", employee.ElectoralNumber);
                    command.Parameters.AddWithValue("@PhotoPath", (object?)employee.PhotoPath ?? DBNull.Value);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteEmployee(int employeeCode)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // حذف مستندات الموظف
                        var docCommand = connection.CreateCommand();
                        docCommand.Transaction = transaction;
                        docCommand.CommandText = "DELETE FROM Documents WHERE EmployeeCode = @EmployeeCode";
                        docCommand.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                        docCommand.ExecuteNonQuery();

                        // حذف الموظف
                        var empCommand = connection.CreateCommand();
                        empCommand.Transaction = transaction;
                        empCommand.CommandText = "DELETE FROM Employees WHERE EmployeeCode = @EmployeeCode";
                        empCommand.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                        empCommand.ExecuteNonQuery();

                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        public static System.Data.DataTable GetAllEmployees()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT 
                    EmployeeCode as 'كود الموظف',
                    Category as 'الصنف',
                    Name as 'الاسم',
                    MotherName as 'اسم الأم',
                    MaritalStatus as 'الحالة الاجتماعية',
                    WifeName as 'اسم الزوج/ـة',
                    Province as 'المحافظة',
                    Nationality as 'الجنسية',
                    IdentityNumber as 'رقم الهوية',
                    DateOfBirth as 'تاريخ الميلاد',
                    EducationLevel as 'المستوى التعليمي',
                    ElectoralNumber as 'الرقم الانتخابي',
                    StartDate as 'تاريخ الامر الاداري',
                    AdministrativeOrder as 'الأمر الإداري',
                    StatisticalNumber as 'الرقم الإحصائي',
                    KeyCardNumber as 'رقم الكي كارد',
                    PhoneNumber as 'رقم الهاتف',
                    BadgeNumber as 'رقم الباج',
                    BadgeExpiryDate as 'تاريخ انتهاء الباج',
                    PhotoPath
                    FROM Employees
                    ORDER BY EmployeeCode";
                using (var adapter = new SQLiteDataAdapter(sql, connection))
                {
                    var table = new System.Data.DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static DataTable GetEmployeeDocuments(int employeeCode)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT 
                    DocId,
                    Description,
                    FilePath,
                    UploadDate
                    FROM Documents 
                    WHERE EmployeeCode = @EmployeeCode
                    ORDER BY UploadDate DESC";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    var table = new DataTable();
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static void AddDocument(int employeeCode, string description, string filePath)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"INSERT INTO Documents 
                    (EmployeeCode, Description, FilePath, UploadDate) 
                    VALUES 
                    (@EmployeeCode, @Description, @FilePath, @UploadDate)";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    command.Parameters.AddWithValue("@Description", description);
                    command.Parameters.AddWithValue("@FilePath", filePath);
                    command.Parameters.AddWithValue("@UploadDate", DateTime.Now);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteDocument(int docId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                // First get the file path to delete the physical file
                string getPathSql = "SELECT FilePath FROM Documents WHERE DocId = @DocId";
                string filePath = "";
                using (var command = new SQLiteCommand(getPathSql, connection))
                {
                    command.Parameters.AddWithValue("@DocId", docId);
                    var result = command.ExecuteScalar();
                    filePath = result?.ToString() ?? "";
                }

                // Delete the database record
                string sql = "DELETE FROM Documents WHERE DocId = @DocId";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@DocId", docId);
                    command.ExecuteNonQuery();
                }

                // Delete the physical file if it exists
                if (!string.IsNullOrEmpty(filePath) && File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
        }

        public static Employee GetEmployeeById(int employeeCode)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "SELECT * FROM Employees WHERE EmployeeCode = @EmployeeCode";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            var employee = new Employee
                            {
                                EmployeeCode = reader.GetInt32(reader.GetOrdinal("EmployeeCode")),
                                Name = reader.GetString(reader.GetOrdinal("Name")),
                                MotherName = reader.GetString(reader.GetOrdinal("MotherName")),
                                DateOfBirth = DateTime.Parse(reader.GetString(reader.GetOrdinal("DateOfBirth"))),
                                Province = reader.GetString(reader.GetOrdinal("Province")),
                                Nationality = reader.GetString(reader.GetOrdinal("Nationality")),
                                IdentityNumber = reader.GetString(reader.GetOrdinal("IdentityNumber")),
                                StartDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("StartDate"))),
                                AdministrativeOrder = reader.GetString(reader.GetOrdinal("AdministrativeOrder")),
                                StatisticalNumber = reader.GetString(reader.GetOrdinal("StatisticalNumber")),
                                KeyCardNumber = reader.GetString(reader.GetOrdinal("KeyCardNumber")),
                                PhoneNumber = reader.GetString(reader.GetOrdinal("PhoneNumber")),
                                Category = reader.GetString(reader.GetOrdinal("Category")),
                                BadgeNumber = reader.GetString(reader.GetOrdinal("BadgeNumber")),
                                MaritalStatus = reader.GetString(reader.GetOrdinal("MaritalStatus")),
                                WifeName = reader.GetString(reader.GetOrdinal("WifeName")),
                                EducationLevel = reader.GetString(reader.GetOrdinal("EducationLevel")),
                                ElectoralNumber = reader.GetString(reader.GetOrdinal("ElectoralNumber")),
                                BadgeExpiryDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("BadgeExpiryDate")))
                            };

                            // Handle PhotoPath which may be null
                            var photoPathOrdinal = reader.GetOrdinal("PhotoPath");
                            if (!reader.IsDBNull(photoPathOrdinal))
                            {
                                employee.PhotoPath = reader.GetString(photoPathOrdinal);
                            }

                            return employee;
                        }
                        throw new Exception("Employee not found");
                    }
                }
            }
        }

        public static void AddEmployeeDocument(int employeeCode, string filePath, string description)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"INSERT INTO Documents (EmployeeCode, FilePath, Description, UploadDate) 
                              VALUES (@EmployeeCode, @FilePath, @Description, @UploadDate)";
                
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    command.Parameters.AddWithValue("@FilePath", filePath);
                    command.Parameters.AddWithValue("@Description", description);
                    command.Parameters.AddWithValue("@UploadDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.ExecuteNonQuery();
                }
            }
        }

        public int ExecuteNonQuery(string sql, SQLiteParameter[]? parameters = null)
        {
            if (_connection == null)
            {
                _connection = new SQLiteConnection(ConnectionString);
            }

            using var command = new SQLiteCommand(sql, _connection);
            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }
            
            _connection.Open();
            try
            {
                return command.ExecuteNonQuery();
            }
            finally
            {
                _connection.Close();
            }
        }

        public SQLiteDataReader ExecuteReader(string sql, SQLiteParameter[]? parameters = null)
        {
            if (_connection == null)
            {
                _connection = new SQLiteConnection(ConnectionString);
            }

            var command = new SQLiteCommand(sql, _connection);
            if (parameters != null)
            {
                command.Parameters.AddRange(parameters);
            }
            _connection.Open();
            return command.ExecuteReader(CommandBehavior.CloseConnection);
        }

        public static DataTable SearchEmployees(string searchTerm)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT 
                    EmployeeCode as 'كود الموظف',
                    Name as 'الاسم',
                    MotherName as 'اسم الأم',
                    DateOfBirth as 'تاريخ الميلاد',
                    Province as 'المحافظة',
                    Nationality as 'الجنسية',
                    IdentityNumber as 'رقم الهوية',
                    StartDate as 'تاريخ الامر الاداري',
                    AdministrativeOrder as 'الأمر الإداري',
                    StatisticalNumber as 'الرقم الإحصائي',
                    KeyCardNumber as 'رقم الكي كارد',
                    PhoneNumber as 'رقم الهاتف',
                    Category as 'الصنف',
                    BadgeNumber as 'رقم الباج',
                    BadgeExpiryDate as 'تاريخ انتهاء الباج',
                    MaritalStatus as 'الحالة الاجتماعية',
                    WifeName as 'اسم الزوج/ـة',
                    EducationLevel as 'المستوى التعليمي',
                    ElectoralNumber as 'الرقم الانتخابي',
                    PhotoPath
                    FROM Employees
                    WHERE Name LIKE @Search 
                    OR MotherName LIKE @Search 
                    OR Province LIKE @Search 
                    OR Nationality LIKE @Search 
                    OR IdentityNumber LIKE @Search 
                    OR AdministrativeOrder LIKE @Search 
                    OR StatisticalNumber LIKE @Search 
                    OR KeyCardNumber LIKE @Search 
                    OR PhoneNumber LIKE @Search 
                    OR Category LIKE @Search 
                    OR MaritalStatus LIKE @Search
                    OR WifeName LIKE @Search
                    OR EducationLevel LIKE @Search
                    OR ElectoralNumber LIKE @Search
                    OR BadgeNumber LIKE @Search";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Search", $"%{searchTerm}%");
                    var table = new DataTable();
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static string GenerateAllEmployeesHtml()
        {
            var employees = GetAllEmployees();
            System.Text.StringBuilder html = new System.Text.StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>قائمة الموظفين</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
        
        body { 
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px; 
            direction: rtl;
            background-color: #f5f5f5;
        }
        .report-container {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            margin: 0 auto;
            overflow-x: auto;
        }
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #45678a 0%, #2c3e50 100%);
            color: white;
            border-radius: 10px;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        .report-header h1 {
            font-size: 28px;
            margin: 0;
            padding: 0;
        }
        .search-container {
            margin: 20px 0;
            text-align: center;
        }
        .search-box {
            width: 50%;
            max-width: 400px;
            padding: 10px 15px;
            border: 2px solid #45678a;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
        }
        table { 
            width: 100%;
            border-collapse: collapse; 
            margin: 20px 0;
            background: white;
        }
        th, td { 
            border: 1px solid #ddd; 
            padding: 6px 8px;
            text-align: center;
            font-size: 13px;
            white-space: nowrap;
        }
        th { 
            background: linear-gradient(135deg, #45678a 0%, #2c3e50 100%);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        tr:nth-child(even) { 
            background-color: #f8f9fa; 
        }
        tr:hover {
            background-color: #f1f4f7;
        }
        .photo-cell {
            width: 50px;
            text-align: center;
        }
        .photo-cell img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #45678a;
            vertical-align: middle;
        }
        .actions {
            text-align: center;
            white-space: nowrap;
        }
        .button {
            display: inline-block;
            padding: 4px 8px;
            background: #45678a;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 12px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #334d6e;
        }
        .date-printed {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        @media print {
            @page {
                size: landscape;
            }
            body {
                margin: 0;
                background: white;
            }
            .search-container {
                display: none;
            }
            .button {
                display: none;
            }
        }
    </style>
    <script>
        function searchEmployees() {
            var input = document.getElementById('searchInput');
            var filter = input.value.toLowerCase();
            var table = document.getElementById('employeesTable');
            var rows = table.getElementsByTagName('tr');

            for (var i = 1; i < rows.length; i++) {
                var display = false;
                var cells = rows[i].getElementsByTagName('td');
                for (var j = 0; j < cells.length; j++) {
                    var cell = cells[j];
                    if (cell) {
                        var text = cell.textContent || cell.innerText;
                        if (text.toLowerCase().indexOf(filter) > -1) {
                            display = true;
                            break;
                        }
                    }
                }
                rows[i].style.display = display ? '' : 'none';
            }
        }
    </script>
</head>
<body>
    <div class=""report-container"">
        <div class=""report-header"">
            <h1>قائمة الموظفين</h1>
        </div>
        
        <div class=""search-container"">
            <input type=""text"" id=""searchInput"" class=""search-box"" 
                   placeholder=""اكتب للبحث في جميع الحقول..."" 
                   onkeyup=""searchEmployees()"">
        </div>

        <table id=""employeesTable"">
            <thead>
                <tr>
                    <th>صورة</th>
                    <th>كود الموظف</th>
                    <th>الصنف</th>
                    <th>الاسم</th>
                    <th>اسم الأم</th>
                    <th>الحالة الاجتماعية</th>
                    <th>اسم الزوج/ـة</th>
                    <th>المحافظة</th>
                    <th>الجنسية</th>
                    <th>رقم الهوية</th>
                    <th>تاريخ الميلاد</th>
                    <th>المستوى التعليمي</th>
                    <th>الرقم الانتخابي</th> 
                    <th>تاريخ الامر الاداري</th>
                    <th>الأمر الإداري</th>
                    <th>الرقم الإحصائي</th>
                    <th>رقم الكي كارد</th>
                    <th>رقم الهاتف</th>
                    <th>رقم الباج</th>
                    <th>تاريخ انتهاء الباج</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>");

            foreach (DataRow row in employees.Rows)
            {
                string photoHtml = "<div style='width: 40px; height: 40px; background: #f0f0f0; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto; color: #666; font-size: 12px;'>-</div>";
                string? photoPath = row["PhotoPath"]?.ToString();
                if (!string.IsNullOrEmpty(photoPath) && File.Exists(photoPath))
                {
                    string base64Image = Convert.ToBase64String(File.ReadAllBytes(photoPath));
                    string imageFormat = Path.GetExtension(photoPath).TrimStart('.').ToLower();
                    photoHtml = $"<img src='data:image/{imageFormat};base64,{base64Image}' alt='صورة الموظف'>";
                }

                html.AppendFormat(@"
                <tr>
                    <td class=""photo-cell"">{0}</td>
                    <td>{1}</td>
                    <td>{2}</td>
                    <td>{3}</td>
                    <td>{4}</td>
                    <td>{5}</td>
                    <td>{6}</td>
                    <td>{7}</td>
                    <td>{8}</td>
                    <td>{9}</td>
                    <td>{10}</td>
                    <td>{11}</td>
                    <td>{12}</td>
                    <td>{13}</td>
                    <td>{14}</td>
                    <td>{15}</td>
                    <td>{16}</td>
                    <td>{17}</td> 
                    <td>{18}</td>
                    <td>{19}</td>
                   
                    <td class=""actions"">
                        <a href=""employee_{1}.html"" class=""button"" target=""_blank"">عرض التفاصيل</a>
                    </td>
                </tr>",
                    photoHtml,
                    row["كود الموظف"],
                    row["الصنف"],
                    row["الاسم"],
                    row["اسم الأم"],
                    row["الحالة الاجتماعية"],
                    row["اسم الزوج/ـة"],
                    row["المحافظة"],
                    row["الجنسية"],
                    row["رقم الهوية"],
                    Convert.ToDateTime(row["تاريخ الميلاد"]).ToString("dd/MM/yyyy"),
                    row["المستوى التعليمي"],
                    row["الرقم الانتخابي"],
                    Convert.ToDateTime(row["تاريخ الامر الاداري"]).ToString("dd/MM/yyyy"),
                    row["الأمر الإداري"],
                    row["الرقم الإحصائي"],
                    row["رقم الكي كارد"],
                    row["رقم الهاتف"],
                    row["رقم الباج"],
                    Convert.ToDateTime(row["تاريخ انتهاء الباج"]).ToString("dd/MM/yyyy")
                   
                   
                );

                // إنشاء تقرير تفصيلي لكل موظف
                int employeeCode = Convert.ToInt32(row["كود الموظف"]);
                GenerateEmployeeReport(employeeCode);
            }

            html.AppendFormat(@"
            </tbody>
               </table>
                 <div class=""date-printed"">
                    تم إصدار هذا التقرير بتاريخ {0} الساعة {1}
                    </div>
                   </div>
                 </body>
               </html>", DateTime.Now.ToString("dd/MM/yyyy"), DateTime.Now.ToString("HH:mm"));

            string htmlFilePath = Path.Combine("html", "index.html");
            Directory.CreateDirectory("html");
            File.WriteAllText(htmlFilePath, html.ToString());

            return htmlFilePath;
        }

        public static void AddVacation(Vacation vacation)
        {
            try
            {
                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    // First get the employee ID
                    string getEmployeeIdSql = "SELECT EmployeeCode FROM Employees WHERE Name = @EmployeeName";
                    int employeeId;
                    using (var command = new SQLiteCommand(getEmployeeIdSql, connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeName", vacation.EmployeeName);
                        var result = command.ExecuteScalar();
                        if (result == null)
                        {
                            throw new Exception($"لم يتم العثور على موظف باسم {vacation.EmployeeName}");
                        }
                        employeeId = Convert.ToInt32(result);
                    }

                    // Then insert the vacation with the employee ID
                    string sql = @"INSERT INTO Vacations (
                        EmployeeId, EmployeeName, VacationType, StartDate, EndDate, DaysCount, Reason)
                    VALUES (
                        @EmployeeId, @EmployeeName, @VacationType, @StartDate, @EndDate, @DaysCount, @Reason)";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeId", employeeId);
                        command.Parameters.AddWithValue("@EmployeeName", vacation.EmployeeName);
                        command.Parameters.AddWithValue("@VacationType", vacation.VacationType);
                        command.Parameters.AddWithValue("@StartDate", vacation.StartDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@EndDate", vacation.EndDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@DaysCount", vacation.DaysCount);
                        command.Parameters.AddWithValue("@Reason", (object?)vacation.Reason ?? DBNull.Value);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"خطأ في إضافة الإجازة: {ex.Message}");
                throw;
            }
        }

        public static void UpdateVacation(Vacation vacation)
        {
            try
            {
                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    // First get the employee ID
                    string getEmployeeIdSql = "SELECT EmployeeCode FROM Employees WHERE Name = @EmployeeName";
                    int employeeId;
                    using (var command = new SQLiteCommand(getEmployeeIdSql, connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeName", vacation.EmployeeName);
                        var result = command.ExecuteScalar();
                        if (result == null)
                        {
                            throw new Exception($"لم يتم العثور على موظف باسم {vacation.EmployeeName}");
                        }
                        employeeId = Convert.ToInt32(result);
                    }

                    string sql = @"UPDATE Vacations SET 
                        EmployeeId = @EmployeeId,
                        EmployeeName = @EmployeeName,
                        VacationType = @VacationType,
                        StartDate = @StartDate,
                        EndDate = @EndDate,
                        DaysCount = @DaysCount,
                        Reason = @Reason
                    WHERE VacationId = @VacationId";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@VacationId", vacation.VacationId);
                        command.Parameters.AddWithValue("@EmployeeId", employeeId);
                        command.Parameters.AddWithValue("@EmployeeName", vacation.EmployeeName);
                        command.Parameters.AddWithValue("@VacationType", vacation.VacationType);
                        command.Parameters.AddWithValue("@StartDate", vacation.StartDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@EndDate", vacation.EndDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@DaysCount", vacation.DaysCount);
                        command.Parameters.AddWithValue("@Reason", (object?)vacation.Reason ?? DBNull.Value);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"خطأ في تحديث الإجازة: {ex.Message}");
                throw;
            }
        }

        public static void DeleteVacation(int vacationId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "DELETE FROM Vacations WHERE VacationId = @VacationId";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@VacationId", vacationId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static DataTable GetAllVacations()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT 
                    VacationId,
                    EmployeeName,
                    VacationType,
                    StartDate,
                    EndDate,
                    DaysCount,
                    Reason
                FROM Vacations
                ORDER BY VacationId DESC";

                using (var adapter = new SQLiteDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static DataTable SearchVacations(string searchTerm)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT 
                    VacationId,
                    EmployeeName,
                    VacationType,
                    StartDate,
                    EndDate,
                    DaysCount,
                    Reason
                FROM Vacations
                WHERE EmployeeName LIKE @Search 
                OR VacationType LIKE @Search 
                OR Reason LIKE @Search";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Search", $"%{searchTerm}%");
                    var table = new DataTable();
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static Vacation GetVacationById(int vacationId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "SELECT * FROM Vacations WHERE VacationId = @VacationId";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@VacationId", vacationId);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Vacation
                            {
                                VacationId = reader.GetInt32(reader.GetOrdinal("VacationId")),
                                EmployeeName = reader.GetString(reader.GetOrdinal("EmployeeName")),
                                VacationType = reader.GetString(reader.GetOrdinal("VacationType")),
                                StartDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("StartDate"))),
                                EndDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("EndDate"))),
                                DaysCount = reader.GetInt32(reader.GetOrdinal("DaysCount")),
                                Reason = reader.IsDBNull(reader.GetOrdinal("Reason")) ? null : reader.GetString(reader.GetOrdinal("Reason"))
                            };
                        }
                        throw new Exception("الإجازة غير موجودة");
                    }
                }
            }
        }

        public static void AddUser(User user)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"INSERT INTO Users (Username, FullName, Password, UserType)
                             VALUES (@Username, @FullName, @Password, @UserType)";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Username", user.Username);
                    command.Parameters.AddWithValue("@FullName", user.FullName);
                    command.Parameters.AddWithValue("@Password", user.Password);
                    command.Parameters.AddWithValue("@UserType", user.UserType);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void UpdateUser(User user)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = user.Password != null ?
                    @"UPDATE Users SET 
                        Username = @Username,
                        FullName = @FullName,
                        Password = @Password,
                        UserType = @UserType
                      WHERE UserId = @UserId" :
                    @"UPDATE Users SET 
                        Username = @Username,
                        FullName = @FullName,
                        UserType = @UserType
                      WHERE UserId = @UserId";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@UserId", user.UserId);
                    command.Parameters.AddWithValue("@Username", user.Username);
                    command.Parameters.AddWithValue("@FullName", user.FullName);
                    command.Parameters.AddWithValue("@UserType", user.UserType);
                    if (user.Password != null)
                        command.Parameters.AddWithValue("@Password", user.Password);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteUser(int userId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                
                // التحقق من أن المستخدم ليس المدير الافتراضي
                string checkSql = "SELECT Username FROM Users WHERE UserId = @UserId";
                using (var checkCommand = new SQLiteCommand(checkSql, connection))
                {
                    checkCommand.Parameters.AddWithValue("@UserId", userId);
                    string? username = checkCommand.ExecuteScalar()?.ToString();
                    if (username == "admin")
                        throw new Exception("لا يمكن حذف حساب المدير الافتراضي");
                }

                string sql = "DELETE FROM Users WHERE UserId = @UserId";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@UserId", userId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static DataTable GetAllUsers()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT 
                    UserId,
                    Username,
                    FullName,
                    UserType
                FROM Users
                ORDER BY UserId";

                using (var adapter = new SQLiteDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static User GetUserById(int userId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "SELECT * FROM Users WHERE UserId = @UserId";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@UserId", userId);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new User
                            {
                                UserId = reader.GetInt32(reader.GetOrdinal("UserId")),
                                Username = reader.GetString(reader.GetOrdinal("Username")),
                                FullName = reader.GetString(reader.GetOrdinal("FullName")),
                                UserType = reader.GetString(reader.GetOrdinal("UserType"))
                            };
                        }
                        throw new Exception("المستخدم غير موجود");
                    }
                }
            }
        }

        public static DataTable GetSettings()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "SELECT * FROM Settings LIMIT 1";

                using (var adapter = new SQLiteDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static void UpdateSettings(string companyName, string? companyLogo, string theme, string language)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"UPDATE Settings SET 
                    CompanyName = @CompanyName,
                    CompanyLogo = @CompanyLogo,
                    Theme = @Theme,
                    Language = @Language,
                    LastModified = datetime('now')";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@CompanyName", companyName);
                    command.Parameters.AddWithValue("@CompanyLogo", (object?)companyLogo ?? DBNull.Value);
                    command.Parameters.AddWithValue("@Theme", theme);
                    command.Parameters.AddWithValue("@Language", language);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static List<Vacation> GetVacationsForEmployee(string employeeName)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT 
                    VacationId,
                    EmployeeName,
                    VacationType,
                    StartDate,
                    EndDate,
                    DaysCount,
                    Reason
                FROM Vacations
                WHERE EmployeeName = @EmployeeName
                ORDER BY StartDate DESC";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeName", employeeName);
                    var vacations = new List<Vacation>();
                    
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            vacations.Add(new Vacation
                            {
                                VacationId = reader.GetInt32(reader.GetOrdinal("VacationId")),
                                EmployeeName = reader.GetString(reader.GetOrdinal("EmployeeName")),
                                VacationType = reader.GetString(reader.GetOrdinal("VacationType")),
                                StartDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("StartDate"))),
                                EndDate = DateTime.Parse(reader.GetString(reader.GetOrdinal("EndDate"))),
                                DaysCount = reader.GetInt32(reader.GetOrdinal("DaysCount")),
                                Reason = reader.IsDBNull(reader.GetOrdinal("Reason")) ? null : reader.GetString(reader.GetOrdinal("Reason"))
                            });
                        }
                    }
                    return vacations;
                }
            }
        }

        public static string GenerateEmployeeReport(int employeeCode)
        {
            var employee = GetEmployeeById(employeeCode);
            if (employee == null)
            {
                throw new Exception("لم يتم العثور على الموظف");
            }
            
            // حساب سنوات الخدمة
            TimeSpan serviceYears = DateTime.Now - employee.StartDate;
            string serviceText;
            int years = (int)(serviceYears.TotalDays / 365.25);
            int months = (int)((serviceYears.TotalDays % 365.25) / 30.44);
            
            if (years > 0 && months > 0)
            {
                serviceText = $"{years} سنة و {months} شهر";
            }
            else if (years > 0)
            {
                serviceText = $"{years} سنة";
            }
            else if (months > 0)
            {
                serviceText = $"{months} شهر";
            }
            else
            {
                serviceText = "أقل من شهر";
            }
            
            System.Text.StringBuilder html = new System.Text.StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير الموظف</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
        
        body { 
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px; 
            direction: rtl;
            background-color: #f5f5f5;
        }        .report-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            margin: 0 auto;
            max-width: 800px;
            position: relative;
            left: 50%;
            transform: translateX(-50%);
        }
        .employee-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #45678a 0%, #2c3e50 100%);
            color: white;
            border-radius: 10px;
            position: relative;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        .employee-header h1 {
            font-size: 32px;
            margin: 0;
            font-weight: 700;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }
        .employee-header .employee-name {
            font-size: 24px;
            margin-top: 10px;
            color: #ecf0f1;
        }        .content-wrapper {
            display: flex;
            flex-direction: row;
            gap: 15px;
            margin: 15px auto;
            max-width: 800px;
            align-items: flex-start;
        }
        .details-section {
            flex: 2;
        }
        .photo-section {
            flex: 1;
            max-width: 300px;
            position: sticky;
            top: 20px;
        }.info-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .info-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            margin-bottom: 5px;
        }
        .info-item:hover {
            transform: translateX(-5px);
            box-shadow: 2px 2px 10px rgba(0,0,0,0.1);
        }
        .info-label {
            font-weight: 600;
            color: #45678a;
            margin-left: 10px;
            white-space: nowrap;
        }
        .info-value {
            flex: 1;
            padding: 4px 8px;
            color: #2c3e50;
        }
        .photo-container {
            width: 100%;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .employee-photo {
            width: 100%;
            height: auto;
            display: block;
        }        .report-footer {
            margin-top: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
            padding-top: 15px;
            border-top: 1px solid #e1e8ed;
            page-break-before: avoid;
            page-break-inside: avoid;
        }        @media print {
            body {
                margin: 0;
                background: white;
                display: flex;
                justify-content: center;
            }
            .report-container {
                box-shadow: none;
                margin: 0 auto;
                padding: 15mm;
                width: 210mm;
                min-height: 297mm;
                transform: none;
                left: auto;
            }
            .content-wrapper {
                gap: 10px;
            }
            .photo-section {
                max-width: 200px;
            }
            .info-item {
                padding: 8px;
                margin-bottom: 4px;
            }
            .employee-header {
                margin-bottom: 20px;
                padding: 15px;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .info-group {
                gap: 5px;
            }            @page {
                margin: 0;
                size: A4 portrait;
            }
        }
    </style>
</head>
<body>
    <div class=""report-container"">
        <div class=""employee-header"">
            <h1>تقرير بيانات الموظف</h1>
            <div class=""employee-name"">" + employee.Name + @"</div>
        </div>
        
        <div class=""content-wrapper"">
            <div class=""details-section"">
                <div class=""info-group"">");

    // إضافة معلومات الموظف
    html.AppendFormat(@"
                    <div class=""info-item"">
                        <span class=""info-label"">كود الموظف:</span>
                        <span class=""info-value"">{0}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">الصنف:</span>
                        <span class=""info-value"">{11}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">اسم الأم:</span>
                        <span class=""info-value"">{1}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">تاريخ الميلاد:</span>
                        <span class=""info-value"">{2}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">المحافظة:</span>
                        <span class=""info-value"">{3}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">الجنسية:</span>
                        <span class=""info-value"">{4}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">رقم الهوية:</span>
                        <span class=""info-value"">{5}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">تاريخ الامر الاداري:</span>
                        <span class=""info-value"">{6}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">مدة الخدمة:</span>
                        <span class=""info-value"">{12}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">رقم الكي كارد:</span>
                        <span class=""info-value"">{7}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">رقم الباج:</span>
                        <span class=""info-value"">{8}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">تاريخ انتهاء الباج:</span>
                        <span class=""info-value"">{9}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">رقم الهاتف:</span>
                        <span class=""info-value"">{10}</span>
                    </div>
                </div>
            </div>
            <div class=""photo-section"">
                <div class=""photo-container"">",
                employee.EmployeeCode,
                employee.MotherName,
                employee.DateOfBirth.ToString("yyyy-MM-dd"),
                employee.Province,
                employee.Nationality,
                employee.IdentityNumber,
                employee.StartDate.ToString("yyyy-MM-dd"),
                employee.KeyCardNumber,
                employee.BadgeNumber,
                employee.BadgeExpiryDate.ToString("yyyy-MM-dd"),
                employee.PhoneNumber,
                employee.Category,
                serviceText);

    // إضافة صورة الموظف
    if (!string.IsNullOrEmpty(employee.PhotoPath) && File.Exists(employee.PhotoPath))
    {
        string base64Image = Convert.ToBase64String(File.ReadAllBytes(employee.PhotoPath));
        string imageFormat = Path.GetExtension(employee.PhotoPath).TrimStart('.').ToLower();
        html.AppendFormat(@"
                    <img src=""data:image/{0};base64,{1}"" alt=""صورة الموظف"" class=""employee-photo"">", 
                    imageFormat, base64Image);
    }
    else
    {
        html.Append(@"
                    <div style=""padding: 20px; text-align: center; color: #666;"">
                        لا تتوفر صورة
                    </div>");
    }

    html.AppendFormat(@"
                </div>
            </div>
        </div>

        <div class=""report-footer"">
            تم إصدار هذا التقرير بتاريخ {0} الساعة {1}
        </div>
    </div>
</body>
</html>", DateTime.Now.ToString("dd/MM/yyyy"), DateTime.Now.ToString("HH:mm"));

    string htmlFilePath = Path.Combine("html", $"employee_{employeeCode}.html");
    Directory.CreateDirectory("html");
    File.WriteAllText(htmlFilePath, html.ToString(), System.Text.Encoding.UTF8);

    return htmlFilePath;
}

        public static void AddCourse(Course course)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"INSERT INTO Courses 
                    (EmployeeName, CourseType, CourseNumber, Category, StartDate, EndDate, DaysCount, GraduationGrade) 
                    VALUES 
                    (@EmployeeName, @CourseType, @CourseNumber, @Category, @StartDate, @EndDate, @DaysCount, @GraduationGrade)";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeName", course.EmployeeName);
                    command.Parameters.AddWithValue("@CourseType", course.CourseType);
                    command.Parameters.AddWithValue("@CourseNumber", course.CourseNumber);
                    command.Parameters.AddWithValue("@Category", course.Category);
                    command.Parameters.AddWithValue("@StartDate", course.StartDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", course.EndDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@DaysCount", course.DaysCount);
                    command.Parameters.AddWithValue("@GraduationGrade", course.GraduationGrade);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void UpdateCourse(Course course)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"UPDATE Courses 
                    SET EmployeeName = @EmployeeName, 
                        CourseType = @CourseType, 
                        CourseNumber = @CourseNumber, 
                        Category = @Category,
                        StartDate = @StartDate, 
                        EndDate = @EndDate, 
                        DaysCount = @DaysCount,
                        GraduationGrade = @GraduationGrade
                    WHERE CourseId = @CourseId";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@CourseId", course.CourseId);
                    command.Parameters.AddWithValue("@EmployeeName", course.EmployeeName);
                    command.Parameters.AddWithValue("@CourseType", course.CourseType);
                    command.Parameters.AddWithValue("@CourseNumber", course.CourseNumber);
                    command.Parameters.AddWithValue("@Category", course.Category);
                    command.Parameters.AddWithValue("@StartDate", course.StartDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", course.EndDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@DaysCount", course.DaysCount);
                    command.Parameters.AddWithValue("@GraduationGrade", course.GraduationGrade);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteCourse(int courseId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "DELETE FROM Courses WHERE CourseId = @CourseId";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@CourseId", courseId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static DataTable GetAllCourses()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT 
                    CourseId as 'المعرف',
                    EmployeeName as 'اسم الموظف',
                    CourseNumber as 'رقم الدورة',
                    CourseType as 'نوع الدورة',
                    Category as 'الصنف',
                    StartDate as 'تاريخ البداية',
                    EndDate as 'تاريخ النهاية',
                    DaysCount as 'عدد الأيام',
                    GraduationGrade as 'درجة التخرج'
                FROM Courses
                ORDER BY CourseId DESC";

                using (var adapter = new SQLiteDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static DataTable SearchCourses(string searchText)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT 
                    CourseId as 'المعرف',
                    EmployeeName as 'اسم الموظف',
                    CourseType as 'نوع الدورة',
                    CourseNumber as 'رقم الدورة',
                    Category as 'الصنف',
                    StartDate as 'تاريخ البداية',
                    EndDate as 'تاريخ النهاية',
                    DaysCount as 'عدد الأيام'
                FROM Courses 
                WHERE EmployeeName LIKE @SearchText 
                OR CourseType LIKE @SearchText 
                OR CourseNumber LIKE @SearchText
                OR Category LIKE @SearchText
                ORDER BY CourseId DESC";

                using (var adapter = new SQLiteDataAdapter(sql, connection))
                {
                    adapter.SelectCommand.Parameters.AddWithValue("@SearchText", "%" + searchText + "%");
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static void CreateCoursesTable()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"CREATE TABLE IF NOT EXISTS Courses (
                    CourseId INTEGER PRIMARY KEY AUTOINCREMENT,
                    Category TEXT,
                    EmployeeName TEXT,
                    CourseType TEXT,
                    CourseNumber TEXT,
                    StartDate TEXT,
                    EndDate TEXT,
                    DaysCount INTEGER,
                    GraduationGrade DECIMAL
                )";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void SaveCourse(Course course)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"INSERT INTO Courses 
                    (EmployeeName, CourseType, CourseNumber, Category, StartDate, EndDate, DaysCount, GraduationGrade) 
                    VALUES 
                    (@EmployeeName, @CourseType, @CourseNumber, @Category, @StartDate, @EndDate, @DaysCount, @GraduationGrade)";
                
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeName", course.EmployeeName);
                    command.Parameters.AddWithValue("@CourseType", course.CourseType);
                    command.Parameters.AddWithValue("@CourseNumber", course.CourseNumber);
                    command.Parameters.AddWithValue("@Category", course.Category);
                    command.Parameters.AddWithValue("@StartDate", course.StartDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", course.EndDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@DaysCount", course.DaysCount);
                    command.Parameters.AddWithValue("@GraduationGrade", course.GraduationGrade);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static List<Course> LoadCourses()
        {
            List<Course> courses = new List<Course>();
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "SELECT * FROM Courses";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Course course = new Course
                            {
                                CourseId = Convert.ToInt32(reader["CourseId"]),
                                EmployeeName = reader["EmployeeName"]?.ToString() ?? string.Empty,
                                CourseType = reader["CourseType"]?.ToString() ?? string.Empty,
                                CourseNumber = reader["CourseNumber"]?.ToString() ?? string.Empty,
                                Category = reader["Category"]?.ToString() ?? string.Empty,
                                StartDate = DateTime.TryParse(reader["StartDate"]?.ToString(), out DateTime startDate) ? startDate : DateTime.MinValue,
                                EndDate = DateTime.TryParse(reader["EndDate"]?.ToString(), out DateTime endDate) ? endDate : DateTime.MinValue,
                                DaysCount = Convert.ToInt32(reader["DaysCount"]),
                                GraduationGrade = reader["GraduationGrade"]?.ToString() ?? string.Empty
                            };
                            courses.Add(course);
                        }
                    }
                }
            }
            return courses;
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    if (_connection != null)
                    {
                        try
                        {
                            if (_connection.State == System.Data.ConnectionState.Open)
                            {
                                _connection.Close();
                            }
                            _connection.Dispose();
                        }
                        catch { }
                        _connection = null;
                    }
                }
                disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        ~DatabaseHelper()
        {
            Dispose(false);
        }

        private static SQLiteConnection CreateAndOpenConnection()
        {
            var connection = new SQLiteConnection(ConnectionString);
            try
            {
                connection.Open();
                return connection;
            }
            catch
            {
                connection.Dispose();
                throw;
            }
        }

        private static void CloseConnection(SQLiteConnection? connection)
        {
            if (connection != null)
            {
                try
                {
                    if (connection.State == System.Data.ConnectionState.Open)
                    {
                        connection.Close();
                    }
                }
                finally
                {
                    connection.Dispose();
                }
            }
        }

        public static void CreateTablesIfNotExist()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"
                    CREATE TABLE IF NOT EXISTS Courses (
                        CourseId INTEGER PRIMARY KEY AUTOINCREMENT,
                        CourseName TEXT,
                        Description TEXT,
                        StartDate TEXT,
                        EndDate TEXT,
                        Instructor TEXT,
                        Location TEXT,
                        Category TEXT,
                        GraduationGrade DECIMAL
                    );";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        public static string GenerateCourseReport(string employeeName)
        {
            var courses = LoadCourses().Where(c => c.EmployeeName == employeeName).ToList();
            
            System.Text.StringBuilder html = new System.Text.StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير دورات الموظف</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
        
        body { 
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px; 
            direction: rtl;
            background-color: #f5f5f5;
        }
        .report-container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            margin: 0 auto;
            max-width: 1200px;
        }
        .report-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #45678a 0%, #2c3e50 100%);
            color: white;
            border-radius: 10px;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        .report-header h1 {
            font-size: 28px;
            margin: 0;
            padding: 0;
        }
        .report-header .employee-name {
            font-size: 24px;
            margin-top: 10px;
            color: #ecf0f1;
        }
        .courses-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .course-card {
            background: white;
            border: 1px solid #e1e8ed;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.2s;
        }
        .course-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .course-header {
            border-bottom: 2px solid #45678a;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .course-title {
            font-size: 18px;
            color: #2c3e50;
            margin: 0;
            font-weight: 600;
        }
        .course-number {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 5px;
        }
        .course-info {
            display: grid;
            gap: 10px;
        }
        .info-item {
            display: flex;
            align-items: center;
            padding: 8px;
            background-color: #f8f9fa;
            border-radius: 6px;
        }
        .info-label {
            font-weight: 600;
            color: #45678a;
            min-width: 120px;
        }
        .info-value {
            color: #2c3e50;
        }
        .grade-badge {
            display: inline-block;
            padding: 6px 12px;
            background: #45678a;
            color: white;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
        }        .report-footer {
            margin-top: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
            padding-top: 15px;
            border-top: 1px solid #e1e8ed;
            page-break-before: avoid;
            page-break-inside: avoid;
        }
        .summary-section {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #e1e8ed;
        }
        .summary-title {
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 600;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .summary-item {
            background: white;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e1e8ed;
        }
        .summary-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
        }
        .summary-value {
            font-size: 20px;
            color: #45678a;
            font-weight: 600;
        }
        @media print {
            body {
                background: white;
            }
            .report-container {
                box-shadow: none;
            }
            .course-card {
                break-inside: avoid;
            }
            .report-header {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            .grade-badge {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class=""report-container"">
        <div class=""report-header"">
            <h1>تقرير الدورات التدريبية</h1>
            <div class=""employee-name"">" + employeeName + @"</div>
        </div>

        <div class=""summary-section"">
            <h3 class=""summary-title"">ملخص الدورات</h3>
            <div class=""summary-grid"">
                <div class=""summary-item"">
                    <div class=""summary-label"">عدد الدورات</div>
                    <div class=""summary-value"">" + courses.Count + @"</div>
                </div>
                <div class=""summary-item"">
                    <div class=""summary-label"">مجموع الأيام</div>
                    <div class=""summary-value"">" + courses.Sum(c => c.DaysCount) + @"</div>
                </div>
                <div class=""summary-item"">
                    <div class=""summary-label"">معدل درجات التخرج</div>
                    <div class=""summary-value"">" + (courses.Any() ? courses.Average(c => 
                        double.TryParse(c.GraduationGrade, out double grade) ? grade : 0
                    ).ToString("F2") : "0") + @"</div>
                </div>
            </div>
        </div>
        
        <div class=""courses-container"">");

        foreach (var course in courses)
        {
            html.AppendFormat(@"
            <div class=""course-card"">
                <div class=""course-header"">
                    <h3 class=""course-title"">{0}</h3>
                    <div class=""course-number"">رقم الدورة: {1}</div>
                </div>
                <div class=""course-info"">
                    <div class=""info-item"">
                        <span class=""info-label"">الصنف:</span>
                        <span class=""info-value"">{2}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">تاريخ البداية:</span>
                        <span class=""info-value"">{3:dd/MM/yyyy}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">تاريخ النهاية:</span>
                        <span class=""info-value"">{4:dd/MM/yyyy}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">عدد الأيام:</span>
                        <span class=""info-value"">{5}</span>
                    </div>
                    <div class=""info-item"">
                        <span class=""info-label"">درجة التخرج:</span>
                        <span class=""info-value""><span class=""grade-badge"">{6}</span></span>
                    </div>
                </div>
            </div>",
            course.CourseType,
            course.CourseNumber,
            course.Category,
            course.StartDate,
            course.EndDate,
            course.DaysCount,
            course.GraduationGrade);
        }

        html.AppendFormat(@"
        </div>
        <div class=""report-footer"">
            تم إصدار هذا التقرير بتاريخ {0} الساعة {1}
        </div>
    </div>
</body>
</html>", DateTime.Now.ToString("dd/MM/yyyy"), DateTime.Now.ToString("HH:mm"));

        string htmlFilePath = Path.Combine("html", $"courses_{employeeName}_{DateTime.Now:yyyyMMdd_HHmmss}.html");
        Directory.CreateDirectory("html");
        File.WriteAllText(htmlFilePath, html.ToString());

        return htmlFilePath;
        }



        public static string GenerateAllCoursesHtml()
        {
            var courses = GetAllCourses();
            System.Text.StringBuilder html = new System.Text.StringBuilder();
            html.Append(@"<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <title>تقرير الدورات</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
        
        body { 
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px; 
            direction: rtl;
            background-color: #f5f5f5;
        }
        .report-container {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            margin: 0 auto;
            overflow-x: auto;
        }
        .report-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #45678a 0%, #2c3e50 100%);
            color: white;
            border-radius: 10px;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        .report-header h1 {
            font-size: 28px;
            margin: 0;
            padding: 0;
        }
        .search-container {
            margin: 20px 0;
            text-align: center;
        }
        .search-box {
            width: 50%;
            max-width: 400px;
            padding: 10px 15px;
            border: 2px solid #45678a;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
        }
        table { 
            width: 100%;
            border-collapse: collapse; 
            margin: 20px 0;
            background: white;
        }
        th, td { 
            border: 1px solid #ddd; 
            padding: 8px 12px;
            text-align: center;
            font-size: 14px;
        }
        th { 
            background: linear-gradient(135deg, #45678a 0%, #2c3e50 100%);
            color: white;
            font-weight: 600;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        tr:nth-child(even) { 
            background-color: #f8f9fa; 
        
        }
        tr:hover {
            background-color: #f1f4f7;
        }
        .date-printed {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        @media print {
            @page {
                size: landscape;
            }
            body {
                margin: 0;
                background: white;
            }
            .search-container {
                display: none;
            }
        }
    </style>
    <script>
        function searchCourses() {
            var input = document.getElementById('searchInput');
            var filter = input.value.toLowerCase();
            var table = document.getElementById('coursesTable');
            var rows = table.getElementsByTagName('tr');

            for (var i = 1; i < rows.length; i++) {
                var display = false;
                var cells = rows[i].getElementsByTagName('td');
                for (var j = 0; j < cells.length; j++) {
                    var cell = cells[j];
                    if (cell) {
                        var text = cell.textContent || cell.innerText;
                        if (text.toLowerCase().indexOf(filter) > -1) {
                            display = true;
                            break;
                        }
                    }
                }
                rows[i].style.display = display ? '' : 'none';
            }
        }
    </script>
</head>
<body>
    <div class=""report-container"">
        <div class=""report-header"">
            <h1>تقرير الدورات التدريبية</h1>
        </div>
        
        <div class=""search-container"">
            <input type=""text"" id=""searchInput"" class=""search-box"" 
                   placeholder=""اكتب للبحث في جميع الحقول..."" 
                   onkeyup=""searchCourses()"">
        </div>

        <table id=""coursesTable"">
            <thead>
                <tr>
                    <th>المعرف</th>
                    <th>الصنف</th>
                    <th>اسم الموظف</th>
                    <th>رقم الدورة</th>
                    <th>نوع الدورة</th>
                    <th>تاريخ البداية</th>
                    <th>تاريخ النهاية</th>
                    <th>عدد الأيام</th>
                    <th>درجة التخرج</th>
                </tr>
            </thead>
            <tbody>");

            foreach (DataRow row in courses.Rows)
            {
                html.AppendFormat(@"
                <tr>
                    <td>{0}</td>
                    <td>{1}</td>
                    <td>{2}</td>
                    <td>{3}</td>
                    <td>{4}</td>
                    <td>{5}</td>
                    <td>{6}</td>
                    <td>{7}</td>
                    <td>{8}</td>
                </tr>",
                    row["المعرف"],
                    row["الصنف"],
                    row["اسم الموظف"],
                    row["رقم الدورة"],
                    row["نوع الدورة"],
                    Convert.ToDateTime(row["تاريخ البداية"]).ToString("dd/MM/yyyy"),
                    Convert.ToDateTime(row["تاريخ النهاية"]).ToString("dd/MM/yyyy"),
                    row["عدد الأيام"],
                    row["درجة التخرج"]
                );
            }

            html.AppendFormat(@"
            </tbody>
        </table>
        <div class=""date-printed"">
            تم إصدار هذا التقرير بتاريخ {0} الساعة {1}
        </div>
    </div>
</body>
</html>", DateTime.Now.ToString("dd/MM/yyyy"), DateTime.Now.ToString("HH:mm"));

            string htmlFilePath = Path.Combine("html", "courses.html");
            Directory.CreateDirectory("html");
            File.WriteAllText(htmlFilePath, html.ToString());

            return htmlFilePath;
        }

        public static int GetEmployeeIdByName(string name)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "SELECT EmployeeCode FROM Employees WHERE Name = @Name";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@Name", name);
                    var result = command.ExecuteScalar();
                    if (result != null && result != DBNull.Value)
                    {
                        return Convert.ToInt32(result);
                    }
                    throw new Exception("لم يتم العثور على الموظف");
                }
            }
        }

        public static void LogMessage(string message)
        {
            try
            {
                string logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "app.log");
                string logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}{Environment.NewLine}";
                File.AppendAllText(logPath, logEntry);
                System.Diagnostics.Debug.WriteLine(logEntry);
            }
            catch
            {
                // تجاهل أي أخطاء في التسجيل
            }
        }

        public static DataTable GetVacationsEndingTomorrow()
        {
            try
            {
                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    var tomorrow = DateTime.Now.Date.AddDays(1).ToString("yyyy-MM-dd");
                    LogMessage($"البحث عن الإجازات التي تنتهي في: {tomorrow}");

                    // طباعة جميع الإجازات للتشخيص
                    string debugQuery = @"
                        SELECT v.*, e.Name as EmployeeName 
                        FROM Vacations v 
                        JOIN Employees e ON v.EmployeeId = e.EmployeeCode";
                    using (var debugCommand = new SQLiteCommand(debugQuery, connection))
                    {
                        using (var reader = debugCommand.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var id = reader["EmployeeId"];
                                var name = reader["EmployeeName"];
                                var endDate = Convert.ToDateTime(reader["EndDate"]).ToString("yyyy-MM-dd");
                                LogMessage($"إجازة موجودة: الموظف {name} (ID: {id}) - تاريخ الانتهاء: {endDate}");
                            }
                        }
                    }

                    string query = @"
                        SELECT v.*, e.Name as EmployeeName
                        FROM Vacations v
                        JOIN Employees e ON v.EmployeeId = e.EmployeeCode
                        WHERE date(v.EndDate) = @tomorrow";

                    using (var command = new SQLiteCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@tomorrow", tomorrow);
                        
                        LogMessage($"تنفيذ الاستعلام: {query}");
                        LogMessage($"مع المعامل @tomorrow = {tomorrow}");

                        var dt = new DataTable();
                        using (var adapter = new SQLiteDataAdapter(command))
                        {
                            adapter.Fill(dt);
                        }
                        LogMessage($"تم العثور على {dt.Rows.Count} إجازة تنتهي غداً");

                        // طباعة النتائج للتشخيص
                        foreach (DataRow row in dt.Rows)
                        {
                            var empId = row["EmployeeId"];
                            var empName = row["EmployeeName"];
                            var endDate = Convert.ToDateTime(row["EndDate"]).ToString("yyyy-MM-dd");
                            LogMessage($"نتيجة الاستعلام: الموظف {empName} (ID: {empId}) - تاريخ الانتهاء: {endDate}");
                        }

                        return dt;
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"خطأ في الحصول على الإجازات: {ex.Message}");
                LogMessage($"Stack Trace: {ex.StackTrace}");
                throw;
            }
        }

        public static DataTable GetCoursesEndingTomorrow()
        {
            try
            {
                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    var tomorrow = DateTime.Now.Date.AddDays(1).ToString("yyyy-MM-dd");
                    LogMessage($"البحث عن الدورات التي تنتهي في: {tomorrow}");

                    string query = @"
                        SELECT c.*, e.EmployeeCode
                        FROM Courses c
                        JOIN Employees e ON e.Name = c.EmployeeName
                        WHERE date(c.EndDate) = @tomorrow";

                    using (var command = new SQLiteCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@tomorrow", tomorrow);
                        var dt = new DataTable();
                        using (var adapter = new SQLiteDataAdapter(command))
                        {
                            adapter.Fill(dt);
                        }
                        LogMessage($"تم العثور على {dt.Rows.Count} دورة تنتهي غداً");
                        return dt;
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"خطأ في الحصول على الدورات: {ex.Message}");
                throw;
            }
        }

        public static bool HasUsers()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "SELECT COUNT(*) FROM Users";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    return Convert.ToInt32(command.ExecuteScalar()) > 0;
                }
            }
        }

        // دوال إدارة الحضور والغياب
        public static int AddAttendance(Attendance attendance)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"INSERT INTO Attendance (
                    EmployeeCode, EmployeeName, Date, CheckInTime, CheckOutTime,
                    WorkingHours, OvertimeHours, Status, Notes, WorkPeriodId, CreatedDate)
                VALUES (
                    @EmployeeCode, @EmployeeName, @Date, @CheckInTime, @CheckOutTime,
                    @WorkingHours, @OvertimeHours, @Status, @Notes, @WorkPeriodId, @CreatedDate);
                SELECT last_insert_rowid();";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", attendance.EmployeeCode);
                    command.Parameters.AddWithValue("@EmployeeName", attendance.EmployeeName);
                    command.Parameters.AddWithValue("@Date", attendance.Date.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@CheckInTime", attendance.CheckInTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@CheckOutTime", attendance.CheckOutTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@WorkingHours", attendance.WorkingHours);
                    command.Parameters.AddWithValue("@OvertimeHours", attendance.OvertimeHours);
                    command.Parameters.AddWithValue("@Status", attendance.Status);
                    command.Parameters.AddWithValue("@Notes", attendance.Notes ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@WorkPeriodId", attendance.WorkPeriodId ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@CreatedDate", attendance.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));

                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        public static void UpdateAttendance(Attendance attendance)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"UPDATE Attendance SET
                    EmployeeCode = @EmployeeCode,
                    EmployeeName = @EmployeeName,
                    Date = @Date,
                    CheckInTime = @CheckInTime,
                    CheckOutTime = @CheckOutTime,
                    WorkingHours = @WorkingHours,
                    OvertimeHours = @OvertimeHours,
                    Status = @Status,
                    Notes = @Notes,
                    WorkPeriodId = @WorkPeriodId
                WHERE AttendanceId = @AttendanceId";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@AttendanceId", attendance.AttendanceId);
                    command.Parameters.AddWithValue("@EmployeeCode", attendance.EmployeeCode);
                    command.Parameters.AddWithValue("@EmployeeName", attendance.EmployeeName);
                    command.Parameters.AddWithValue("@Date", attendance.Date.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@CheckInTime", attendance.CheckInTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@CheckOutTime", attendance.CheckOutTime?.ToString("yyyy-MM-dd HH:mm:ss") ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@WorkingHours", attendance.WorkingHours);
                    command.Parameters.AddWithValue("@OvertimeHours", attendance.OvertimeHours);
                    command.Parameters.AddWithValue("@Status", attendance.Status);
                    command.Parameters.AddWithValue("@Notes", attendance.Notes ?? (object)DBNull.Value);
                    command.Parameters.AddWithValue("@WorkPeriodId", attendance.WorkPeriodId ?? (object)DBNull.Value);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static bool DeleteAttendance(int attendanceId)
        {
            try
            {
                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    string sql = "DELETE FROM Attendance WHERE AttendanceId = @AttendanceId";
                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@AttendanceId", attendanceId);
                        int rowsAffected = command.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حذف سجل الحضور: {ex.Message}");
                return false;
            }
        }

        public static DataTable GetAllAttendance()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT
                    AttendanceId as 'المعرف',
                    EmployeeCode as 'كود الموظف',
                    EmployeeName as 'اسم الموظف',
                    Date as 'التاريخ',
                    CheckInTime as 'وقت الحضور',
                    CheckOutTime as 'وقت الانصراف',
                    WorkingHours as 'ساعات العمل',
                    OvertimeHours as 'الساعات الإضافية',
                    Status as 'الحالة',
                    Notes as 'ملاحظات'
                FROM Attendance
                ORDER BY Date DESC, CheckInTime DESC";

                using (var adapter = new SQLiteDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static DataTable GetAttendanceByEmployee(int employeeCode)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT
                    AttendanceId as 'المعرف',
                    Date as 'التاريخ',
                    CheckInTime as 'وقت الحضور',
                    CheckOutTime as 'وقت الانصراف',
                    WorkingHours as 'ساعات العمل',
                    OvertimeHours as 'الساعات الإضافية',
                    Status as 'الحالة',
                    Notes as 'ملاحظات'
                FROM Attendance
                WHERE EmployeeCode = @EmployeeCode
                ORDER BY Date DESC";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    var table = new DataTable();
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static DataTable GetAttendanceByDateRange(DateTime startDate, DateTime endDate)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT
                    AttendanceId as 'المعرف',
                    EmployeeCode as 'كود الموظف',
                    EmployeeName as 'اسم الموظف',
                    Date as 'التاريخ',
                    CheckInTime as 'وقت الحضور',
                    CheckOutTime as 'وقت الانصراف',
                    WorkingHours as 'ساعات العمل',
                    OvertimeHours as 'الساعات الإضافية',
                    Status as 'الحالة',
                    Notes as 'ملاحظات'
                FROM Attendance
                WHERE Date BETWEEN @StartDate AND @EndDate
                ORDER BY Date DESC, CheckInTime DESC";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));
                    var table = new DataTable();
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static Attendance? GetTodayAttendance(int employeeCode)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT * FROM Attendance
                    WHERE EmployeeCode = @EmployeeCode AND Date = @Today";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    command.Parameters.AddWithValue("@Today", DateTime.Today.ToString("yyyy-MM-dd"));

                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new Attendance
                            {
                                AttendanceId = reader.GetInt32("AttendanceId"),
                                EmployeeCode = reader.GetInt32("EmployeeCode"),
                                EmployeeName = reader.GetString("EmployeeName"),
                                Date = DateTime.Parse(reader.GetString("Date")),
                                CheckInTime = reader.IsDBNull("CheckInTime") ? null : DateTime.Parse(reader.GetString("CheckInTime")),
                                CheckOutTime = reader.IsDBNull("CheckOutTime") ? null : DateTime.Parse(reader.GetString("CheckOutTime")),
                                WorkingHours = reader.GetDouble("WorkingHours"),
                                OvertimeHours = reader.GetDouble("OvertimeHours"),
                                Status = reader.GetString("Status"),
                                Notes = reader.IsDBNull("Notes") ? null : reader.GetString("Notes"),
                                WorkPeriodId = reader.IsDBNull("WorkPeriodId") ? null : reader.GetInt32("WorkPeriodId"),
                                CreatedDate = DateTime.Parse(reader.GetString("CreatedDate"))
                            };
                        }
                    }
                }
            }
            return null;
        }

        public static bool CheckIn(int employeeCode, string employeeName)
        {
            var todayAttendance = GetTodayAttendance(employeeCode);

            if (todayAttendance != null && todayAttendance.CheckInTime.HasValue)
            {
                return false; // الموظف سجل حضوره بالفعل اليوم
            }

            if (todayAttendance == null)
            {
                // إنشاء سجل حضور جديد
                var attendance = new Attendance
                {
                    EmployeeCode = employeeCode,
                    EmployeeName = employeeName,
                    Date = DateTime.Today,
                    CheckInTime = DateTime.Now,
                    Status = "حاضر"
                };

                // فحص التأخير
                attendance.CheckLateStatus(new TimeSpan(8, 0, 0)); // وقت العمل الرسمي 8:00 صباحاً

                AddAttendance(attendance);
            }
            else
            {
                // تحديث وقت الحضور
                todayAttendance.CheckInTime = DateTime.Now;
                todayAttendance.Status = "حاضر";
                todayAttendance.CheckLateStatus(new TimeSpan(8, 0, 0));
                UpdateAttendance(todayAttendance);
            }

            return true;
        }

        public static bool CheckOut(int employeeCode)
        {
            var todayAttendance = GetTodayAttendance(employeeCode);

            if (todayAttendance == null || !todayAttendance.CheckInTime.HasValue)
            {
                return false; // لم يسجل الموظف حضوره بعد
            }

            if (todayAttendance.CheckOutTime.HasValue)
            {
                return false; // الموظف سجل انصرافه بالفعل
            }

            // تسجيل وقت الانصراف وحساب ساعات العمل
            todayAttendance.CheckOutTime = DateTime.Now;
            todayAttendance.CalculateWorkingHours();
            UpdateAttendance(todayAttendance);

            return true;
        }

        // دالة مساعدة لجلب الموظفين بأسماء الأعمدة الإنجليزية للاستخدام في نظام الحضور
        public static DataTable GetEmployeesForAttendance()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT
                    EmployeeCode,
                    Name,
                    Category,
                    Province
                FROM Employees
                ORDER BY Name";

                using (var adapter = new SQLiteDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        // دوال إدارة فترات العمل
        public static int AddWorkPeriod(WorkPeriod workPeriod)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"INSERT INTO WorkPeriods (
                    EmployeeCode, EmployeeName, ProjectName, Description, StartDate, EndDate,
                    WorkingDays, DailyWorkingHours, Status, CreatedDate)
                VALUES (
                    @EmployeeCode, @EmployeeName, @ProjectName, @Description, @StartDate, @EndDate,
                    @WorkingDays, @DailyWorkingHours, @Status, @CreatedDate);
                SELECT last_insert_rowid();";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", workPeriod.EmployeeCode);
                    command.Parameters.AddWithValue("@EmployeeName", workPeriod.EmployeeName);
                    command.Parameters.AddWithValue("@ProjectName", workPeriod.ProjectName);
                    command.Parameters.AddWithValue("@Description", workPeriod.Description);
                    command.Parameters.AddWithValue("@StartDate", workPeriod.StartDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", workPeriod.EndDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@WorkingDays", workPeriod.WorkingDays);
                    command.Parameters.AddWithValue("@DailyWorkingHours", workPeriod.DailyWorkingHours);
                    command.Parameters.AddWithValue("@Status", workPeriod.Status);
                    command.Parameters.AddWithValue("@CreatedDate", workPeriod.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));

                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        public static void UpdateWorkPeriod(WorkPeriod workPeriod)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"UPDATE WorkPeriods SET
                    EmployeeCode = @EmployeeCode,
                    EmployeeName = @EmployeeName,
                    ProjectName = @ProjectName,
                    Description = @Description,
                    StartDate = @StartDate,
                    EndDate = @EndDate,
                    WorkingDays = @WorkingDays,
                    DailyWorkingHours = @DailyWorkingHours,
                    Status = @Status
                WHERE WorkPeriodId = @WorkPeriodId";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@WorkPeriodId", workPeriod.WorkPeriodId);
                    command.Parameters.AddWithValue("@EmployeeCode", workPeriod.EmployeeCode);
                    command.Parameters.AddWithValue("@EmployeeName", workPeriod.EmployeeName);
                    command.Parameters.AddWithValue("@ProjectName", workPeriod.ProjectName);
                    command.Parameters.AddWithValue("@Description", workPeriod.Description);
                    command.Parameters.AddWithValue("@StartDate", workPeriod.StartDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", workPeriod.EndDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@WorkingDays", workPeriod.WorkingDays);
                    command.Parameters.AddWithValue("@DailyWorkingHours", workPeriod.DailyWorkingHours);
                    command.Parameters.AddWithValue("@Status", workPeriod.Status);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void DeleteWorkPeriod(int workPeriodId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "DELETE FROM WorkPeriods WHERE WorkPeriodId = @WorkPeriodId";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static DataTable GetAllWorkPeriods()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT
                    WorkPeriodId as 'المعرف',
                    EmployeeCode as 'كود الموظف',
                    EmployeeName as 'اسم الموظف',
                    ProjectName as 'مكان العمل',
                    Description as 'الوصف',
                    StartDate as 'تاريخ البداية',
                    EndDate as 'تاريخ النهاية',
                    WorkingDays as 'أيام العمل',
                    DailyWorkingHours as 'ساعات العمل اليومية',
                    Status as 'الحالة'
                FROM WorkPeriods
                ORDER BY StartDate DESC";

                using (var adapter = new SQLiteDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static DataTable GetWorkPeriodsByEmployee(int employeeCode)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT
                    WorkPeriodId as 'المعرف',
                    ProjectName as 'مكان العمل',
                    Description as 'الوصف',
                    StartDate as 'تاريخ البداية',
                    EndDate as 'تاريخ النهاية',
                    WorkingDays as 'أيام العمل',
                    DailyWorkingHours as 'ساعات العمل اليومية',
                    Status as 'الحالة'
                FROM WorkPeriods
                WHERE EmployeeCode = @EmployeeCode
                ORDER BY StartDate DESC";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    var table = new DataTable();
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static List<WorkPeriod> GetActiveWorkPeriods(int employeeCode)
        {
            var workPeriods = new List<WorkPeriod>();

            try
            {
                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    string sql = @"SELECT * FROM WorkPeriods
                        WHERE EmployeeCode = @EmployeeCode
                        AND Status IN ('نشط', 'نشطة')
                        AND StartDate <= @Today
                        AND EndDate >= @Today";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                        command.Parameters.AddWithValue("@Today", DateTime.Today.ToString("yyyy-MM-dd"));

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                try
                                {
                                    var workPeriod = new WorkPeriod
                                    {
                                        WorkPeriodId = reader.GetInt32("WorkPeriodId"),
                                        EmployeeCode = reader.GetInt32("EmployeeCode"),
                                        EmployeeName = reader.IsDBNull("EmployeeName") ? "" : reader.GetString("EmployeeName"),
                                        ProjectName = reader.IsDBNull("ProjectName") ? "مكان عمل غير محدد" : reader.GetString("ProjectName"),
                                        Description = reader.IsDBNull("Description") ? "" : reader.GetString("Description"),
                                        WorkingDays = reader.IsDBNull("WorkingDays") ? "الأحد,الاثنين,الثلاثاء,الأربعاء,الخميس" : reader.GetString("WorkingDays"),
                                        DailyWorkingHours = reader.IsDBNull("DailyWorkingHours") ? 8.0 : reader.GetDouble("DailyWorkingHours"),
                                        Status = reader.IsDBNull("Status") ? "نشط" : reader.GetString("Status")
                                    };

                                    // معالجة التواريخ بحذر
                                    if (!reader.IsDBNull("StartDate") && DateTime.TryParse(reader.GetString("StartDate"), out DateTime startDate))
                                    {
                                        workPeriod.StartDate = startDate;
                                    }
                                    else
                                    {
                                        workPeriod.StartDate = DateTime.Today;
                                    }

                                    if (!reader.IsDBNull("EndDate") && DateTime.TryParse(reader.GetString("EndDate"), out DateTime endDate))
                                    {
                                        workPeriod.EndDate = endDate;
                                    }
                                    else
                                    {
                                        workPeriod.EndDate = DateTime.Today.AddDays(30);
                                    }

                                    if (!reader.IsDBNull("CreatedDate") && DateTime.TryParse(reader.GetString("CreatedDate"), out DateTime createdDate))
                                    {
                                        workPeriod.CreatedDate = createdDate;
                                    }
                                    else
                                    {
                                        workPeriod.CreatedDate = DateTime.Now;
                                    }

                                    workPeriods.Add(workPeriod);
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"خطأ في قراءة فترة العمل: {ex.Message}");
                                    continue;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على فترات العمل النشطة: {ex.Message}");
            }

            return workPeriods;
        }

        public static WorkPeriod? GetWorkPeriodById(int workPeriodId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "SELECT * FROM WorkPeriods WHERE WorkPeriodId = @WorkPeriodId";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@WorkPeriodId", workPeriodId);
                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            return new WorkPeriod
                            {
                                WorkPeriodId = reader.GetInt32("WorkPeriodId"),
                                EmployeeCode = reader.GetInt32("EmployeeCode"),
                                EmployeeName = reader.GetString("EmployeeName"),
                                ProjectName = reader.IsDBNull("ProjectName") ? null : reader.GetString("ProjectName"),
                                Description = reader.IsDBNull("Description") ? null : reader.GetString("Description"),
                                StartDate = DateTime.Parse(reader.GetString("StartDate")),
                                EndDate = DateTime.Parse(reader.GetString("EndDate")),
                                WorkingDays = reader.IsDBNull("WorkingDays") ? null : reader.GetString("WorkingDays"),
                                DailyWorkingHours = reader.GetDouble("DailyWorkingHours"),
                                Status = reader.GetString("Status"),
                                CreatedDate = DateTime.Parse(reader.GetString("CreatedDate"))
                            };
                        }
                    }
                }
            }
            return null;
        }



        public static DataTable GetWorkPeriodsByEmployee(int employeeCode, DateTime startDate, DateTime endDate)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT
                    WorkPeriodId as 'المعرف',
                    EmployeeName as 'اسم الموظف',
                    ProjectName as 'مكان العمل',
                    Description as 'الوصف',
                    StartDate as 'تاريخ البداية',
                    EndDate as 'تاريخ النهاية',
                    WorkingDays as 'أيام العمل',
                    DailyWorkingHours as 'ساعات العمل اليومية',
                    Status as 'الحالة',
                    CreatedDate as 'تاريخ الإنشاء'
                FROM WorkPeriods
                WHERE EmployeeCode = @EmployeeCode
                AND ((StartDate >= @StartDate AND StartDate <= @EndDate)
                     OR (EndDate >= @StartDate AND EndDate <= @EndDate)
                     OR (StartDate <= @StartDate AND EndDate >= @EndDate))
                ORDER BY StartDate DESC";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));

                    var table = new DataTable();
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static DataTable GetAllWorkPeriods(DateTime startDate, DateTime endDate)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT
                    WorkPeriodId as 'المعرف',
                    EmployeeCode as 'كود الموظف',
                    EmployeeName as 'اسم الموظف',
                    ProjectName as 'مكان العمل',
                    Description as 'الوصف',
                    StartDate as 'تاريخ البداية',
                    EndDate as 'تاريخ النهاية',
                    WorkingDays as 'أيام العمل',
                    DailyWorkingHours as 'ساعات العمل اليومية',
                    Status as 'الحالة',
                    CreatedDate as 'تاريخ الإنشاء'
                FROM WorkPeriods
                WHERE ((StartDate >= @StartDate AND StartDate <= @EndDate)
                       OR (EndDate >= @StartDate AND EndDate <= @EndDate)
                       OR (StartDate <= @StartDate AND EndDate >= @EndDate))
                ORDER BY EmployeeName, StartDate DESC";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
                    command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));

                    var table = new DataTable();
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        // دوال إدارة مجموعات العمل
        public static int CreateWorkGroup(string groupName, string description, string createdBy)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"INSERT INTO WorkGroups (GroupName, Description, CreatedDate, CreatedBy, LastModified, IsActive)
                              VALUES (@GroupName, @Description, @CreatedDate, @CreatedBy, @LastModified, 1);
                              SELECT last_insert_rowid();";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@GroupName", groupName);
                    command.Parameters.AddWithValue("@Description", description ?? "");
                    command.Parameters.AddWithValue("@CreatedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.Parameters.AddWithValue("@CreatedBy", createdBy ?? "");
                    command.Parameters.AddWithValue("@LastModified", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                    return Convert.ToInt32(command.ExecuteScalar());
                }
            }
        }

        public static void AddMemberToWorkGroup(int groupId, int employeeCode, string employeeName, string memberType)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();

                // التحقق من عدم وجود العضو مسبقاً
                string checkSql = "SELECT COUNT(*) FROM WorkGroupMembers WHERE GroupId = @GroupId AND EmployeeCode = @EmployeeCode";
                using (var checkCommand = new SQLiteCommand(checkSql, connection))
                {
                    checkCommand.Parameters.AddWithValue("@GroupId", groupId);
                    checkCommand.Parameters.AddWithValue("@EmployeeCode", employeeCode);

                    int count = Convert.ToInt32(checkCommand.ExecuteScalar());
                    if (count > 0)
                    {
                        // تحديث نوع العضوية إذا كان موجوداً
                        string updateSql = "UPDATE WorkGroupMembers SET MemberType = @MemberType WHERE GroupId = @GroupId AND EmployeeCode = @EmployeeCode";
                        using (var updateCommand = new SQLiteCommand(updateSql, connection))
                        {
                            updateCommand.Parameters.AddWithValue("@GroupId", groupId);
                            updateCommand.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                            updateCommand.Parameters.AddWithValue("@MemberType", memberType);
                            updateCommand.ExecuteNonQuery();
                        }
                        return;
                    }
                }

                // إضافة عضو جديد
                string sql = @"INSERT INTO WorkGroupMembers (GroupId, EmployeeCode, EmployeeName, MemberType, AddedDate)
                              VALUES (@GroupId, @EmployeeCode, @EmployeeName, @MemberType, @AddedDate)";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@GroupId", groupId);
                    command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                    command.Parameters.AddWithValue("@EmployeeName", employeeName);
                    command.Parameters.AddWithValue("@MemberType", memberType);
                    command.Parameters.AddWithValue("@AddedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.ExecuteNonQuery();
                }
            }
        }

        public static DataTable GetAllWorkGroups()
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT
                    GroupId as 'معرف المجموعة',
                    GroupName as 'اسم المجموعة',
                    Description as 'الوصف',
                    CreatedDate as 'تاريخ الإنشاء',
                    CreatedBy as 'أنشأ بواسطة',
                    (SELECT COUNT(*) FROM WorkGroupMembers WHERE GroupId = wg.GroupId AND MemberType = 'عمل') as 'موظفين العمل',
                    (SELECT COUNT(*) FROM WorkGroupMembers WHERE GroupId = wg.GroupId AND MemberType = 'إجازة') as 'موظفين الإجازة'
                FROM WorkGroups wg
                WHERE IsActive = 1
                ORDER BY CreatedDate DESC";

                using (var adapter = new SQLiteDataAdapter(sql, connection))
                {
                    var table = new DataTable();
                    adapter.Fill(table);
                    return table;
                }
            }
        }

        public static DataTable GetWorkGroupMembers(int groupId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"SELECT
                    MemberId as 'معرف العضو',
                    EmployeeCode as 'كود الموظف',
                    EmployeeName as 'اسم الموظف',
                    MemberType as 'نوع العضوية',
                    AddedDate as 'تاريخ الإضافة'
                FROM WorkGroupMembers
                WHERE GroupId = @GroupId
                ORDER BY MemberType, EmployeeName";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@GroupId", groupId);
                    var table = new DataTable();
                    using (var adapter = new SQLiteDataAdapter(command))
                    {
                        adapter.Fill(table);
                    }
                    return table;
                }
            }
        }

        public static void LoadWorkGroupToForm(int groupId, out List<int> workEmployees, out List<int> vacationEmployees)
        {
            workEmployees = new List<int>();
            vacationEmployees = new List<int>();

            try
            {
                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    string sql = "SELECT EmployeeCode, MemberType FROM WorkGroupMembers WHERE GroupId = @GroupId";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@GroupId", groupId);
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                // التعامل مع أنواع البيانات المختلفة
                                var employeeCodeValue = reader["EmployeeCode"];
                                int employeeCode = Convert.ToInt32(employeeCodeValue);
                                string memberType = reader.GetString("MemberType");

                                System.Diagnostics.Debug.WriteLine($"تحميل عضو: {employeeCode} - {memberType}");

                                if (memberType == "عمل")
                                    workEmployees.Add(employeeCode);
                                else if (memberType == "إجازة")
                                    vacationEmployees.Add(employeeCode);
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"تم تحميل {workEmployees.Count} موظف عمل و {vacationEmployees.Count} موظف إجازة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل أعضاء المجموعة: {ex.Message}");
                throw;
            }
        }

        public static void DeleteWorkGroup(int groupId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // حذف أعضاء المجموعة
                        string deleteMembersSql = "DELETE FROM WorkGroupMembers WHERE GroupId = @GroupId";
                        using (var command = new SQLiteCommand(deleteMembersSql, connection))
                        {
                            command.Transaction = transaction;
                            command.Parameters.AddWithValue("@GroupId", groupId);
                            command.ExecuteNonQuery();
                        }

                        // حذف المجموعة
                        string deleteGroupSql = "DELETE FROM WorkGroups WHERE GroupId = @GroupId";
                        using (var command = new SQLiteCommand(deleteGroupSql, connection))
                        {
                            command.Transaction = transaction;
                            command.Parameters.AddWithValue("@GroupId", groupId);
                            command.ExecuteNonQuery();
                        }

                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        public static void ClearWorkGroupMembers(int groupId)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = "DELETE FROM WorkGroupMembers WHERE GroupId = @GroupId";
                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@GroupId", groupId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public static void UpdateWorkGroup(int groupId, string groupName, string description)
        {
            using (var connection = new SQLiteConnection(ConnectionString))
            {
                connection.Open();
                string sql = @"UPDATE WorkGroups SET
                    GroupName = @GroupName,
                    Description = @Description,
                    LastModified = @LastModified
                WHERE GroupId = @GroupId";

                using (var command = new SQLiteCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@GroupId", groupId);
                    command.Parameters.AddWithValue("@GroupName", groupName);
                    command.Parameters.AddWithValue("@Description", description ?? "");
                    command.Parameters.AddWithValue("@LastModified", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.ExecuteNonQuery();
                }
            }
        }

        public static DataTable GetWorkPeriodsByGroup(string groupName, DateTime startDate, DateTime endDate)
        {
            try
            {
                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();

                    // البحث عن فترات العمل التي تحتوي على موظفين من المجموعة المحددة
                    string sql = @"SELECT DISTINCT
                        wp.WorkPeriodId as 'المعرف',
                        wp.EmployeeCode as 'كود الموظف',
                        wp.EmployeeName as 'اسم الموظف',
                        wp.ProjectName as 'مكان العمل',
                        wp.Description as 'الوصف',
                        wp.StartDate as 'تاريخ البداية',
                        wp.EndDate as 'تاريخ النهاية',
                        wp.WorkingDays as 'أيام العمل',
                        wp.DailyWorkingHours as 'ساعات العمل اليومية',
                        wp.Status as 'الحالة',
                        wp.CreatedDate as 'تاريخ الإنشاء',
                        wg.GroupName as 'اسم المجموعة'
                    FROM WorkPeriods wp
                    INNER JOIN WorkGroupMembers wgm ON wp.EmployeeCode = wgm.EmployeeCode
                    INNER JOIN WorkGroups wg ON wgm.GroupId = wg.GroupId
                    WHERE wg.GroupName = @GroupName
                    AND ((wp.StartDate >= @StartDate AND wp.StartDate <= @EndDate)
                         OR (wp.EndDate >= @StartDate AND wp.EndDate <= @EndDate)
                         OR (wp.StartDate <= @StartDate AND wp.EndDate >= @EndDate))
                    ORDER BY wp.StartDate DESC, wp.EmployeeName";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@GroupName", groupName);
                        command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));

                        var table = new DataTable();
                        using (var adapter = new SQLiteDataAdapter(command))
                        {
                            adapter.Fill(table);
                        }

                        System.Diagnostics.Debug.WriteLine($"تم العثور على {table.Rows.Count} فترة عمل للمجموعة {groupName}");
                        return table;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث بالمجموعة: {ex.Message}");
                return new DataTable();
            }
        }

        public static DataTable GetWorkGroupsByDateRange(DateTime startDate, DateTime endDate)
        {
            try
            {
                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();

                    // البحث عن المجموعات التي لها فترات عمل في النطاق الزمني المحدد
                    string sql = @"SELECT DISTINCT
                        wg.GroupId as 'معرف المجموعة',
                        wg.GroupName as 'اسم المجموعة',
                        wg.Description as 'الوصف',
                        wg.CreatedDate as 'تاريخ الإنشاء',
                        COUNT(DISTINCT wp.WorkPeriodId) as 'عدد فترات العمل',
                        COUNT(DISTINCT wgm.EmployeeCode) as 'عدد الموظفين',
                        MIN(wp.StartDate) as 'أقدم فترة',
                        MAX(wp.EndDate) as 'أحدث فترة'
                    FROM WorkGroups wg
                    INNER JOIN WorkGroupMembers wgm ON wg.GroupId = wgm.GroupId
                    INNER JOIN WorkPeriods wp ON wgm.EmployeeCode = wp.EmployeeCode
                    WHERE ((wp.StartDate >= @StartDate AND wp.StartDate <= @EndDate)
                           OR (wp.EndDate >= @StartDate AND wp.EndDate <= @EndDate)
                           OR (wp.StartDate <= @StartDate AND wp.EndDate >= @EndDate))
                    AND wg.IsActive = 1
                    GROUP BY wg.GroupId, wg.GroupName, wg.Description, wg.CreatedDate
                    ORDER BY wg.CreatedDate DESC";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));

                        var table = new DataTable();
                        using (var adapter = new SQLiteDataAdapter(command))
                        {
                            adapter.Fill(table);
                        }

                        return table;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن المجموعات بالتاريخ: {ex.Message}");
                return new DataTable();
            }
        }

        public static DataTable SearchAttendanceByName(string employeeName, DateTime startDate, DateTime endDate)
        {
            try
            {
                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    string sql = @"SELECT
                        AttendanceId as 'المعرف',
                        EmployeeCode as 'كود الموظف',
                        EmployeeName as 'اسم الموظف',
                        Date as 'التاريخ',
                        CheckInTime as 'وقت الحضور',
                        CheckOutTime as 'وقت الانصراف',
                        WorkingHours as 'ساعات العمل',
                        OvertimeHours as 'الساعات الإضافية',
                        Status as 'الحالة',
                        Notes as 'ملاحظات'
                    FROM Attendance
                    WHERE EmployeeName LIKE @EmployeeName COLLATE NOCASE
                    AND Date >= @StartDate AND Date <= @EndDate
                    ORDER BY Date DESC, CheckInTime DESC";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeName", $"%{employeeName}%");
                        command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd"));
                        command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd"));

                        var table = new DataTable();
                        using (var adapter = new SQLiteDataAdapter(command))
                        {
                            adapter.Fill(table);
                        }

                        System.Diagnostics.Debug.WriteLine($"البحث عن '{employeeName}' - تم العثور على {table.Rows.Count} سجل");
                        return table;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث بالاسم: {ex.Message}");
                return new DataTable();
            }
        }

        public static void CreateAbsenceRecordsForDate(DateTime date)
        {
            try
            {
                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();

                    // الحصول على جميع الموظفين النشطين
                    string getAllEmployeesSql = "SELECT EmployeeCode, Name FROM Employees WHERE IsActive = 1";
                    var allEmployees = new List<(int Code, string Name)>();

                    using (var command = new SQLiteCommand(getAllEmployeesSql, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                allEmployees.Add((reader.GetInt32("EmployeeCode"), reader.GetString("Name")));
                            }
                        }
                    }

                    // الحصول على الموظفين الذين لديهم سجلات حضور لهذا التاريخ
                    string getAttendedEmployeesSql = "SELECT DISTINCT EmployeeCode FROM Attendance WHERE Date = @Date";
                    var attendedEmployees = new HashSet<int>();

                    using (var command = new SQLiteCommand(getAttendedEmployeesSql, connection))
                    {
                        command.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                attendedEmployees.Add(reader.GetInt32("EmployeeCode"));
                            }
                        }
                    }

                    // إنشاء سجلات غياب للموظفين الذين لم يحضروا
                    int absenceCount = 0;
                    foreach (var employee in allEmployees)
                    {
                        if (!attendedEmployees.Contains(employee.Code))
                        {
                            var absenceRecord = new Attendance
                            {
                                EmployeeCode = employee.Code,
                                EmployeeName = employee.Name,
                                Date = date,
                                Status = "غائب",
                                CheckInTime = null,
                                CheckOutTime = null,
                                WorkingHours = 0,
                                OvertimeHours = 0,
                                Notes = "غياب تلقائي - لم يسجل حضور",
                                CreatedDate = DateTime.Now
                            };

                            AddAttendance(absenceRecord);
                            absenceCount++;
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"تم إنشاء {absenceCount} سجل غياب للتاريخ {date:yyyy-MM-dd}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء سجلات الغياب: {ex.Message}");
                throw;
            }
        }

        public static void ProcessDailyAttendance(DateTime date)
        {
            try
            {
                // إنشاء سجلات الغياب للموظفين الذين لم يحضروا
                CreateAbsenceRecordsForDate(date);

                System.Diagnostics.Debug.WriteLine($"تم معالجة حضور يوم {date:yyyy-MM-dd}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في معالجة الحضور اليومي: {ex.Message}");
                throw;
            }
        }

        public static void ProcessAttendanceForDateRange(DateTime startDate, DateTime endDate)
        {
            try
            {
                DateTime currentDate = startDate;
                while (currentDate <= endDate)
                {
                    // تجاهل أيام الجمعة والسبت (عطلة نهاية الأسبوع)
                    if (currentDate.DayOfWeek != DayOfWeek.Friday && currentDate.DayOfWeek != DayOfWeek.Saturday)
                    {
                        CreateAbsenceRecordsForDate(currentDate);
                    }
                    currentDate = currentDate.AddDays(1);
                }

                System.Diagnostics.Debug.WriteLine($"تم معالجة الحضور للفترة من {startDate:yyyy-MM-dd} إلى {endDate:yyyy-MM-dd}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في معالجة الحضور للفترة: {ex.Message}");
                throw;
            }
        }

        public static Attendance GetAttendanceByEmployeeAndDate(int employeeCode, DateTime date)
        {
            try
            {
                using (var connection = new SQLiteConnection(ConnectionString))
                {
                    connection.Open();
                    string sql = @"SELECT * FROM Attendance
                                 WHERE EmployeeCode = @EmployeeCode
                                 AND Date = @Date
                                 ORDER BY CreatedDate DESC
                                 LIMIT 1";

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        command.Parameters.AddWithValue("@EmployeeCode", employeeCode);
                        command.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Attendance
                                {
                                    AttendanceId = reader.GetInt32("AttendanceId"),
                                    EmployeeCode = reader.GetInt32("EmployeeCode"),
                                    EmployeeName = reader.GetString("EmployeeName"),
                                    Date = DateTime.Parse(reader.GetString("Date")),
                                    CheckInTime = reader.IsDBNull("CheckInTime") ? null : DateTime.Parse(reader.GetString("CheckInTime")),
                                    CheckOutTime = reader.IsDBNull("CheckOutTime") ? null : DateTime.Parse(reader.GetString("CheckOutTime")),
                                    WorkingHours = reader.GetDouble("WorkingHours"),
                                    OvertimeHours = reader.GetDouble("OvertimeHours"),
                                    Status = reader.GetString("Status"),
                                    Notes = reader.IsDBNull("Notes") ? "" : reader.GetString("Notes"),
                                    CreatedDate = DateTime.Parse(reader.GetString("CreatedDate"))
                                };
                            }
                        }
                    }
                }

                return null; // لم يتم العثور على سجل
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في البحث عن سجل الحضور: {ex.Message}");
                return null;
            }
        }
    }
}
